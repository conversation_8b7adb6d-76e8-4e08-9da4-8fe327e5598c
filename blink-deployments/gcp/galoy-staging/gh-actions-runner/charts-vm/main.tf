module "shared" {
  source = "../../shared"
}

locals {
  project = module.shared.gcp_project
  zone    = "${module.shared.gcp_region}-b"
  region  = module.shared.gcp_region

  image                = "ubuntu-os-cloud/ubuntu-2204-lts"
  default_machine_type = "e2-highcpu-4"
  deployer_sa          = "<EMAIL>"
  users = [
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "serviceAccount:${local.deployer_sa}",
  ]
}

provider "google" {
  project = local.project
  region  = local.region
  zone    = local.zone
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/gha-runner-charts-vm"
  }
}
