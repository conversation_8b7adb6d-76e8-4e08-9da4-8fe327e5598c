data "honeycombio_query_specification" "bria_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "ERROR"
  }

  filter {
    column = "service.name"
    op     = "="
    value  = local.bria_service_name
  }

  breakdowns = ["name", "error.name", "error.message"]
}

resource "honeycombio_query" "bria_errors" {
  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_errors.json
}

resource "honeycombio_trigger" "bria_errors" {
  name        = "${local.name_prefix}-bria-errors"
  description = "Alert when the bria server experiences a critical error"
  alert_type  = "on_true"

  query_id = honeycombio_query.bria_errors.id
  dataset  = local.bria_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "bria_essential_queue" {
  for_each = toset(local.essential_bria_queues)

  calculation {
    op = "COUNT"
  }

  time_range = 600 // 10 minutes

  filter {
    column = "job_name"
    op     = "="
    value  = each.value
  }

  filter {
    column = "service.name"
    op     = "="
    value  = local.bria_service_name
  }

  breakdowns = ["job_name"]
}

resource "honeycombio_query" "bria_essential_queue" {
  for_each = toset(local.essential_bria_queues)

  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_essential_queue[each.value].json
}

resource "honeycombio_trigger" "bria_essential_queue" {
  for_each = toset(local.essential_bria_queues)

  name        = "${local.name_prefix}-bria-${replace(each.value, "_", "-")}-stalled"
  description = <<EOF
An essential bria queue has not been processed in the last two minutes.
This should never happen but has been observed in the past.
Refer to https://github.com/blinkbitcoin/blink-deployments#stalled-job-queues for more information.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.bria_essential_queue[each.value].id
  dataset  = local.bria_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = "<"
    value = 1
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
