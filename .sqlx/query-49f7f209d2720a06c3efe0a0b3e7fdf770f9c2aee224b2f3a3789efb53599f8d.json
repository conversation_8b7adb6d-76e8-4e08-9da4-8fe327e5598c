{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT id FROM bria_payout_queues WHERE (COALESCE(id < $2, true)) ORDER BY id DESC LIMIT $1) SELECT i.id AS \"entity_id: PayoutQueueId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_payout_queue_events e ON i.id = e.id ORDER BY i.id desc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: PayoutQueueId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "49f7f209d2720a06c3efe0a0b3e7fdf770f9c2aee224b2f3a3789efb53599f8d"}