secrets:
  create: true
  nextAuthSecret: ""
  clientSecret: ""
  escrowApiKey: ""
  pgCon: ""
voucher:
  hydraPublic: "http://galoy-hydra-public.galoy-dev-galoy.svc.cluster.local:4444"
  coreUrl: "http://galoy-oathkeeper-proxy.galoy-dev-galoy.svc.cluster.local:4455/graphql"
  voucherUrl: "http://localhost:3000"
  nextAuthUrl: ""
  clientId: ""
  otelExporterOtlpEndpoint: http://localhost:4318
  tracingServiceName: "voucher"
  platformFeesInPpm: 2000
image:
  repository: us.gcr.io/galoy-org/galoy-voucher
  digest: "sha256:132efc02e22b1ddcad684e6ae76fb56b13eefd008d859f807b21aa85396fc843" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=62cd3f3;app=voucher;monorepo_subdir=apps/voucher;
ingress:
  enabled: false
service:
  port: 3000
  type: ClusterIP
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: voucher
    password: voucher
    database: voucher
resources: {}
