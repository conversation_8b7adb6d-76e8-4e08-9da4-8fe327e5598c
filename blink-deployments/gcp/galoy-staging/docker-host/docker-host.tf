resource "google_compute_instance" "docker_host" {
  name         = local.docker_host_name
  project      = local.project
  machine_type = local.default_machine_type
  zone         = local.zone

  boot_disk {
    initialize_params {
      image = local.image
      size  = 200
      type  = "pd-ssd"
    }
  }

  metadata_startup_script = file("${path.module}/setup-host.sh")

  network_interface {
    subnetwork = data.google_compute_subnetwork.docker_host.self_link
  }

  tags = [local.docker_host_tag]

  service_account {
    email  = "<EMAIL>"
    scopes = ["cloud-platform"]
  }

  metadata = {
    enable-oslogin     = "TRUE"
    enable-oslogin-2fa = "TRUE"
  }
}

data "google_iam_policy" "docker_host" {
  binding {
    role    = "roles/compute.osLogin"
    members = local.users
  }
  binding {
    role    = "roles/compute.viewer"
    members = local.users
  }
  binding {
    role    = "roles/compute.admin"
    members = ["serviceAccount:${local.deployer_sa}"]
  }
}

resource "google_compute_instance_iam_policy" "docker_host" {
  project       = local.project
  zone          = google_compute_instance.docker_host.zone
  instance_name = google_compute_instance.docker_host.name
  policy_data   = data.google_iam_policy.docker_host.policy_data
}

resource "google_compute_firewall" "docker_host_allow_all_inbound" {
  name        = "${local.name_prefix}-docker-host-allow-ingress-${local.name_postfix}"
  project     = local.project
  network     = data.google_compute_network.vpc.self_link
  target_tags = [local.docker_host_tag]

  direction = "INGRESS"
  # this is google's IP used for IAP access
  source_ranges = ["************/20"]

  priority = "1000"

  allow {
    protocol = "tcp"
    ports    = [22]
  }
}
