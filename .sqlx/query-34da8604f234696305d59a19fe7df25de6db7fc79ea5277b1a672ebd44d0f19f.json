{"db_name": "PostgreSQL", "query": "INSERT INTO bria_payout_queue_events (id, recorded_at, sequence, event_type, event) SELECT unnested.id, $1, unnested.sequence, unnested.event_type, unnested.event FROM UNNEST($2::UUID[], $3::INT[], $4::TEXT[], $5::JSONB[]) AS unnested(id, sequence, event_type, event)", "describe": {"columns": [], "parameters": {"Left": ["Timestamptz", "UuidArray", "Int4Array", "TextArray", "JsonbArray"]}, "nullable": []}, "hash": "34da8604f234696305d59a19fe7df25de6db7fc79ea5277b1a672ebd44d0f19f"}