## Default values for Galoy Helm Chart.
## This is a YAML-formatted file.
## Declare variables to be passed into your templates.

## Provide a name in place of `galoy` for `app:` labels
##
nameOverride:
## Provide a name to substitute for the full names of resources
##
fullnameOverride:
galoy:
  ## Bitcoin Network to run this Galoy instance on
  ##
  network: mainnet
  ## Application Custom Config
  ## Ref: https://github.com/GaloyMoney/galoy/blob/main/default.yaml
  ##
  router:
    port: 80
  # LND used as primary lightning node, can be either lnd1 or lnd2
  lndPriority: lnd1
  config:
    bria:
      hotWalletName: dev
      queueNames:
        fast: fast
      coldStorage:
        walletName: cold
        hotToColdRebalanceQueueName: dev
  ## Docker Images for running Galoy Banking Codebase
  ##
  images:
    ## Galoy Application Image details
    ##
    app:
      repository: us.gcr.io/galoy-org/galoy-api
      ## Digest of the image
      ##
      digest: "sha256:c1d579d8dcd0400b4c3907188aab60d1991ea33c196182be23fc3586c33e02f1"
      ## Not used by <PERSON><PERSON>, just used to ref to the source https://github.com/GaloyMoney/galoy.git
      ## Reference for timestamping the corresponding docker image and used by internal CI.
      ##
      git_ref: "d5c8414"
    websocket:
      repository: us.gcr.io/galoy-org/galoy-api-ws-server
      ## Digest of the image
      ##
      digest: "sha256:33607f049177792e4fb2ac7c391c90d5595111a4494f91f309f6a86c2cd5859f"
    trigger:
      repository: us.gcr.io/galoy-org/galoy-api-trigger
      ## Digest of the image
      ##
      digest: "sha256:4dd63c5b715490b01e2f7c64388ab059a29526d88adc999e9668eea2cdf7ac7d"
    cron:
      repository: us.gcr.io/galoy-org/galoy-api-cron
      ## Digest of the image
      ##
      digest: "sha256:68f11a9bfbb2d6b5ee4b0f0d21b45150d16d4af3c15e0a82c4df9e4f12b51da5"
    exporter:
      repository: us.gcr.io/galoy-org/galoy-api-exporter
      ## Digest of the image
      ##
      digest: "sha256:b6fa05cb0fc2a6d4202f6ba06cf9dc1e1f7f74570878cb1ab2b828885b7297f3"
    consent:
      repository: us.gcr.io/galoy-org/galoy-consent
      ## Digest of the image
      ##
      digest: "sha256:ff2bda2c67d8ba13f135f0393c8288d130ab0cc15cacd1348cbfd58ef5ce50b6" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=553cbd2;app=consent;monorepo_subdir=apps/consent;
    apiKeys:
      repository: us.gcr.io/galoy-org/galoy-api-keys
      ## Digest of the image
      ##
      digest: "sha256:fa8561484d749b16bc4746112e6729c085253952b197586574077feee49ffff7" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=ce663d1;app=api-keys;monorepo_subdir=core/api-keys;
    notifications:
      repository: us.gcr.io/galoy-org/galoy-notifications
      ## Digest of the image
      ##
      digest: "sha256:71c53ab3d9a4282d44d0605b95f1691ebc23334e8108bd4d06021a0c627a1205" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=4994b6e;app=notifications;monorepo_subdir=core/notifications;
    ## Galoy Application MongoDB Migration Image details
    ##
    mongodbMigrate:
      ## Has mongodb migration
      ##
      repository: us.gcr.io/galoy-org/galoy-api-migrate
      ## Digest of the image
      ##
      digest: "sha256:cf788eea9882fc5d4e8c0da658bed8b416514dcc3c5aefc5e44788c45e4b90dd"
    ## Galoy Application MongoDB Backup Image details
    ##
    mongoBackup:
      ## Has mongodb backup
      ##
      repository: us.gcr.io/galoy-org/mongo-backup
      ## Digest of the image
      digest: "sha256:66d22e50f472f8fb43a93edec095cd724df2393130d6fa3f17e98906eaedb269"
      ## Not used by Helm, just used to ref to the source https://github.com/GaloyMoney/galoy.git
      ## Reference for timestamping the corresponding docker image and used by internal CI.
      ##
      git_ref: "ce9792c"
  ## Configuration values for Galoy API components.
  ##
  api:
    ## Override the components name (defaults to api).
    ##
    nameOverride:
    ## Number of replicas.
    ##
    replicas: 2
    ## Log Level
    ##
    logLevel: debug
    ## Port on which API Server is running
    ##
    port: 4002
    ## Service type
    ##
    serviceType: ClusterIP
    ## Resource configuration
    resources: {}
    ## Ingress configuration.
    ## Ref: https://kubernetes.io/docs/user-guide/ingress/
    ##
    ingress:
      ## Enable Ingress.
      ##
      enabled: false
      ## Ingress Host
      ##
      hosts: ["api.staging.blink.sv"]
      ## Cluster Issuer (Default: LetsEncrypt Certificate Isser defined at galoy-infra)
      ## Read More: https://cert-manager.io/docs/usage/ingress/
      ##
      clusterIssuer: letsencrypt-issuer
      limitRpm: 120
      limitBurstMultiplier: 20
      limitConnections: 80
    ## Firebase Notifications Configuration
    ## It would need a Firebase Service Account to send out notifications
    ##
    firebaseNotifications:
      # Enable/disable notifications on the app
      enabled: false
      # Firebase Notifications Service Account must be injected via a secret
      existingSecret:
        ## Secret Name of the Service Account JSON
        name: galoyapp-firebase-serviceaccount
        # Secret Key
        key: galoyapp-firebase-serviceaccount.json
    ## CAUTION - UNSECURE FLAG!
    ## Enable Unsercure default logins to any phone number via the code
    unsecureDefaultLoginCode:
      enabled: false # Should be disabled in all production systems
      existingSecret:
        name: galoyapp-unsecure-login-code
        key: code
    ## Liveness/Readiness Probes Configuration
    ## Determines if pod is healthy or if it should be killed.
    ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
    ##
    probes:
      ## Enable/disable API probes for healthcheck
      enabled: true
      ## If pod is starting up/healthy
      liveness:
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 1
      ## When to expose the pod to the service
      readiness:
        initialDelaySeconds: 5
        failureThreshold: 5
        successThreshold: 2
        timeoutSeconds: 1
  ## Configuration values for Trigger components.
  ##
  trigger:
    ## Override the components name (defaults to trigger).
    ##
    nameOverride:
    ## Number of replicas.
    ##
    replicas: 1
    ## Log Level
    ##
    logLevel: debug
    ## Port on which Exporter Server is running
    ##
    port: 8888
    ## Service type
    ##
    serviceType: ClusterIP
    ## Resource configuration
    resources: {}
    ## Liveness/Readiness Probes Configuration
    ## Determines if pod is healthy or if it should be killed.
    ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
    ##
    probes:
      ## Enable/disable Admin API probes for healthcheck
      enabled: true
      ## If pod is starting up/healthy
      liveness:
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 1
      ## When to expose the pod to the service
      readiness:
        initialDelaySeconds: 5
        failureThreshold: 5
        successThreshold: 2
        timeoutSeconds: 1
    ## Galoy database backup configuration
    ## This is a custom backup configuration, which means data lying in
    ## MongoDB and LND can be backed up by custom scripts present in the
    ## galoy codebase itself.
    ##
    backups:
      ## Backup data to Google Cloud Storage Bucket
      ## To backup to GCS bucket, you would need a GCP Service Account
      ## that lets you upload to a specific bucket
      ##
      gcs:
        # Enable/disable uploading backup to GCS bucket
        enabled: false
        # GCS bucket name to upload to (it should already be created)
        bucketName: gcs-bucket-name
        # Service account to use to upload backup to above-mentioned bucket
        # Service account credentials json file must be injected via a secret
        serviceAccountExistingSecret:
          # Secret name
          name: gcs-sa-key
          # Secret Key
          key: gcs-sa-key.json
      ## Backup data to S3 bucket
      ## To backup to S3 bucket, you would need an access key and secret key
      ## to upload data to your S3 bucket
      ##
      s3:
        # Enable/disable uploading backup to S3 bucket
        enabled: false
        # S3 bucket name to upload to (it should already be created)
        bucketName: s3-bucket-name
        # Aws region
        region: us-east-1
        # S3 access key to use to upload backup to above-mentioned bucket
        accessKeyExistingSecret:
          # Secret name
          name: s3-creds
          # Secret Key
          key: access-key
        # S3 secret key to use to upload backup to above-mentioned bucket
        secretKeyExistingSecret:
          # Secret name
          name: s3-creds
          # Secret Key
          key: secret-key
  ## Configuration values for Galoy Admin components.
  ##
  admin:
    ## Override the components name (defaults to admin).
    ##
    nameOverride:
    ## Number of replicas.
    ##
    replicas: 1
    ## Log Level
    ##
    logLevel: debug
    ## Port on which Admin API Server is running
    ##
    port: 4001
    ## Service type
    ##
    serviceType: ClusterIP
    ## Ingress configuration.
    ## Ref: https://kubernetes.io/docs/user-guide/ingress/
    ##
    ingress:
      ## Enable Ingress.
      ##
      enabled: false
      ## Ingress Host
      ##
      hosts: ["admin.staging.blink.sv"]
      ## Cluster Issuer (Default: LetsEncrypt Certificate Isser defined at galoy-infra)
      ## Read More: https://cert-manager.io/docs/usage/ingress/
      ##
      clusterIssuer: letsencrypt-issuer
      ## TLS Secret Name
      ##
      tlsSecretName: graphql-admin-tls
    ## Liveness/Readiness Probes Configuration
    ## Determines if pod is healthy or if it should be killed.
    ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
    ##
    probes:
      ## Enable/disable Admin API probes for healthcheck
      enabled: true
      ## If pod is starting up/healthy
      liveness:
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 1
      ## When to expose the pod to the service
      readiness:
        initialDelaySeconds: 5
        failureThreshold: 5
        successThreshold: 2
        timeoutSeconds: 1
  ## Configuration values for Galoy Metrics Exporter components.
  ##
  exporter:
    ## Override the components name (defaults to admin).
    ##
    nameOverride:
    ## Number of replicas.
    ##
    replicas: 1
    ## Log Level
    ##
    logLevel: debug
    ## Port on which Exporter Server is running
    ##
    port: 3000
    ## Resource configuration
    resources: {}
    ## Liveness/Readiness Probes Configuration
    ## Determines if pod is healthy or if it should be killed.
    ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
    ##
    probes:
      ## Enable/disable Admin API probes for healthcheck
      enabled: true
      ## If pod is starting up/healthy
      liveness:
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 1
      ## When to expose the pod to the service
      readiness:
        initialDelaySeconds: 5
        failureThreshold: 5
        successThreshold: 2
        timeoutSeconds: 1
  ## Configuration values for Galoy Websocket components.
  ##
  websocket:
    ## Override the components name (defaults to websocket).
    ##
    nameOverride:
    ## Number of replicas.
    ##
    replicas: 1
    ## Log Level
    ##
    logLevel: debug
    ## Port on which websocket server is running
    ##
    port: 4000
    ## Service type
    ##
    serviceType: ClusterIP
    ## Resource configuration
    resources: {}
    ## Ingress configuration.
    ## Ref: https://kubernetes.io/docs/user-guide/ingress/
    ##
    ingress:
      ## Enable Ingress.
      ##
      enabled: false
      ## Ingress Host
      ##
      hosts: [ws.staging.blink.sv]
      ## Cluster Issuer (Default: LetsEncrypt Certificate Isser defined at galoy-infra)
      ## Read More: https://cert-manager.io/docs/usage/ingress/
      ##
      clusterIssuer: letsencrypt-issuer
      ## TLS Secret Name
      ##
      tlsSecretName: websocket-tls
    ## Liveness/Readiness Probes Configuration
    ## Determines if pod is healthy or if it should be killed.
    ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/
    ##
    firebaseNotifications:
      # Enable/disable notifications on the app
      enabled: false
      # Firebase Notifications Service Account must be injected via a secret
      existingSecret:
        ## Secret Name of the Service Account JSON
        name: galoyapp-firebase-serviceaccount
        # Secret Key
        key: galoyapp-firebase-serviceaccount.json
    probes:
      ## If pod is starting up/healthy
      liveness:
        initialDelaySeconds: 60
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 1
      ## When to expose the pod to the service
      readiness:
        initialDelaySeconds: 5
        failureThreshold: 5
        successThreshold: 2
        timeoutSeconds: 1
  apiKeys:
    nameOverride:
    port: 5397
    replicas: 2
    serviceType: ClusterIP
    resources: {}
    config:
      keyPrefix: galoy
  notifications:
    nameOverride:
    serverDeployment:
      replicas: 2
      resources: {}
    jobsDeployment:
      replicas: 2
      resources: {}
    serviceType: ClusterIP
    grpcPort: 6685
    graphqlPort: 6684
    config:
      db:
        poolSize: 20
      importFromKratos: false
      smtp:
        enabled: false
        username: ""
        fromEmail: ""
        fromName: ""
        relayHost: ""
        relayPort: 587
      jobs:
        kickoffLinkEmailReminderDelay: 21600
      linkEmailReminder:
        accountLivenessThresholdMinutes: 30240
        accountAgeThresholdMinutes: 30240
        notificationCoolOffThresholdMinutes: 129600
  consent:
    resources: {}
    port: 80
    containerPort: 3000
    graphqlPublicApi: http://galoy-oathkeeper-proxy:4455/graphql
    coreAuthUrl: http://galoy-oathkeeper-proxy:4455/auth
    hydraAdminUrl: http://galoy-hydra-admin:4445
    ingress:
      enabled: false
      hosts: [consent.staging.blink.sv]
      clusterIssuer: letsencrypt-issuer
      tlsSecretName: consent-tls
  mongoBackupCron:
    resources: {}
  galoyCron:
    resources: {}
  mongoMigrationJob:
    resources: {}
  ## Kratos
  kratos:
    publicApiUrl: http://galoy-kratos-public
    adminApiUrl: http://galoy-kratos-admin
    existingSecret:
      name: kratos-secret
      master_user_password: master_user_password
      callback_api_key: callback_api_key
  ## Configuration values for establishing connection to LND-1
  ## TODO: This should be injected as ConfigMap from LND Chart
  ##
  lnd1:
    ## DNS for LND1
    ##
    dns: lnd1.default.svc.cluster.local
    ## Credentials to access LND RPC
    ##
    credentialsExistingSecret:
      ## Secret Name
      name: lnd1-credentials
      ## Macaroon Key
      macaroon_key: admin_macaroon_base64
      ## TLS Key
      tls_key: tls_base64
    ## Pubkey to verify LND RPC
    ##
    pubkeyExistingSecret:
      ## Secret Name
      name: lnd1-pubkey
      ## Secret Key
      key: pubkey
  ## Configuration values for establishing connection to LND-2
  ## TODO: This should be injected as ConfigMap from LND Chart
  ##
  lnd2:
    ## DNS for LND2
    ##
    dns: lnd2.default.svc.cluster.local
    ## Credentials to access LND RPC
    ##
    credentialsExistingSecret:
      ## Secret Name
      name: lnd2-credentials
      ## Macaroon Key
      macaroon_key: admin_macaroon_base64
      ## TLS Key
      tls_key: tls_base64
    ## Pubkey to verify LND RPC
    ##
    pubkeyExistingSecret:
      ## Secret Name
      name: lnd2-pubkey
      ## Secret Key
      key: pubkey
  bria:
    host: bria-api.default.svc.cluster.local
    port: 2742
    apiKeyExistingSecret:
      name: bria-api-key
      key: api-key
  ## Dealer Service Connection details
  ##
  dealer:
    ## Dealer Service Host
    host: dealer-price.default.svc.cluster.local
    ## Dealer Service Port
    port: 3325
  ## Geetest Configuration
  ##
  geetestExistingSecret:
    # Secret Name
    name: geetest-key
    # Geetest ID Secret Key
    id_key: id
    # Geetest Key Secret Key
    secret_key: key
  ## OpenAI Assistant Configuration
  openai:
    assistantId: "assistant-id"
    existingSecret:
      name: openai-secret
      key: api-key
  pineconeExistingSecret:
    name: pinecone-secret
    key: api-key
  ## Twilio Configuration
  ##
  twilioExistingSecret:
    # Secret Name
    name: twilio-secret
    # Twilio Verify Service Account
    verify_service_id: TWILIO_VERIFY_SERVICE_ID
    # Twilio Account ID
    account_sid_key: TWILIO_ACCOUNT_SID
    # Twilio Authentication Token
    auth_token_key: TWILIO_AUTH_TOKEN
    # Twilio messaging Service ID
    messaging_service_id: TWILIO_MESSAGING_SERVICE_ID
    # Twilio welcome content SID
    welcome_content_sid: TWILIO_WELCOME_CONTENT_SID
  ## Telegram Configuration
  telegramExistingSecret:
    # Secret Name
    name: telegram-secret
    # Telegram passport private key
    private_key: TELEGRAM_PASSPORT_PRIVATE_KEY
    # Telegram bot API Token
    api_token_key: TELEGRAM_BOT_API_TOKEN
  ## Svix secret
  svixExistingSecret:
    # Secret Name
    name: svix-secret
    # Svix secret
    secret_key: svix-secret
  ## Proxy check api key
  proxyCheckExistingSecret:
    # Secret Name
    name: proxy-check-api-key
    # Proxy check api key
    key: api-key
  ## Mattermost webhook url
  mattermostWebhookUrl: "mattermostwebhookurl"
# Configuration values for the mongodb dependency.
# Ref: https://artifacthub.io/packages/helm/bitnami/mongodb/
#
# This is a necessary component and without the database, the chart will not work.
# Therefore it doesn't have any enable/disable flag.
#
mongodb:
  # Authentication Configuration
  auth:
    # Existing secret for authentication
    # This secret gets autogenerated if secrets.create is set to true.
    #
    existingSecret: galoy-mongodb
    # Existing  secret for mongodb connection string
    connectionStringExistingSecret: galoy-mongodb-connection-string
    # Username to be used for Galoy
    # Autocreated at MongoDB initialization
    #
    # There should be one and only one username in the array here.
    #
    usernames: [testGaloy]
    # Database to be used for Galoy
    # Autocreated at MongoDB initialization
    #
    # There should be one and only one database in the array here.
    #
    databases: [galoy]
  # Replicaset for no-downtime
  #
  architecture: replicaset
  # Replicas for MongoDB
  #
  replicaCount: 3
  persistence:
    enabled: true
  arbiter:
    enabled: false
  initdbScripts:
    grant_user_admin_role.sh: |
      isMaster=$(mongosh admin -u root -p "$MONGODB_ROOT_PASSWORD" --eval "rs.isMaster().ismaster" | tail -1 | tr -d '\n')
      if [ "$isMaster" == "true" ]; then
      mongosh admin -u root -p $MONGODB_ROOT_PASSWORD << EOF
        use admin;
        db.grantRolesToUser('root', [ {role: "userAdmin", db: "galoy"} ]);
      EOF
      fi;
  # Prometheus metrics
  metrics:
    enabled: true
## Configuration values for the redis dependency.
## Ref: https://artifacthub.io/packages/helm/bitnami/redis/
##
## This is a necessary component and without the cache, the chart will not work.
## Therefore it doesn't have any enable/disable flag.
##
redis:
  ## Redis replica config params
  replica:
    # Number of Redis to deploy
    replicaCount: 3
  ## Redis master config params
  master:
    persistence:
      enabled: true
  ## Redis auth config params
  auth:
    existingSecret: galoy-redis-pw
    existingSecretPasswordKey: redis-password
  ## Use sentinel on Redis pods
  sentinel:
    enabled: true
    ## Master set name
    masterSet: mymaster
  ## Sidecar prometheus exporter
  metrics:
    enabled: true
## For managing secrets using Helm
## Set the values if you'd want to make it configurable
##
secrets:
  ## Create the secret resource from the following values. Set this to false
  ## to manage these secrets outside Helm.
  ##
  create: true
  ## Secrets for MongoDB
  mongodbPassword:
  mongodbRootPassword:
  mongodbMetricsPassword:
  mongodbReplicaSetKey:
  mongodbConnectionString:
  ## Secrets for Redis
  redisPassword:
  ## Secrets for PostgreSQL
  postgresPassword:
  ## Secrets for Geetest
  geetestId:
  geetestSecretKey:
  ## Secret for Twilio
  twilioVerifyService:
  twilioAccountSid:
  twilioAuthToken:
  twilioMessagingServiceId:
  twilioWelcomeContentSid:
  ## Secrets for Telegram
  telegramPrivateKey:
  telegramApiToken:
  ## Secrets for LND1
  lnd1Macaroon:
  lnd1Tls:
  lnd1PubKey:
  ## Secrets for LND2
  lnd2Macaroon:
  lnd2Tls:
  lnd2PubKey:
  ## Secrets for Bria
  briaApiKey:
  ## Secret for Galoy app
  kratosMasterUserPassword:
  kratosCallbackApiKey:
  ## Secret for Svix
  svixSecretKey:
  ## Api key for proxy check
  proxyCheckApiKey:
  ## postgres secret for api-keys
  apiKeysPgCon:
  ## postgres secret for notifications
  notificationsPgCon:
  ## Smtp password for notifications
  notificationsSmtpPassword:
  ## Service account to send firebase noitifications
  firebaseServiceAccountJson:
  ## OpenAI api key
  openaiApiKey:
  ## Pinecone api key
  pineconeApiKey:
## Tracing details
##
tracing:
  ## OTEL Exporter OTLP Endpoint
  ##
  otelExporterOtlpEndpoint: http://localhost:4318
  otelExporterGrpcEndpoint: http://localhost:4317
  ## Prefix for tracing
  ##
  prefix: galoy-dev
  ## OTEL sampling config
  ##
  otelTracesSampler: always_on
price:
  realtime:
    host: galoy-price-realtime
  history:
    postgresqlHost: galoy-postgresql
    host: galoy-price-history
    port: 50052
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: price-history
    database: price-history
    existingSecret: galoy-price-history-postgres-creds
oathkeeper:
  replicaCount: 2
  oathkeeper:
    config:
      log:
        level: debug
      authenticators:
        jwt:
          enabled: true
          config:
            trusted_issuers:
              - https://firebaseappcheck.googleapis.com/***********
            target_audience:
              - projects/***********
            jwks_urls:
              - https://firebaseappcheck.googleapis.com/v1beta/jwks
            token_from:
              header: Appcheck
        bearer_token:
          enabled: true
          config:
            check_session_url: http://galoy-kratos-public:80/sessions/whoami
            preserve_path: true
            subject_from: identity.id
            extra_from: identity.traits
        oauth2_introspection:
          enabled: true
          config:
            introspection_url: http://galoy-hydra-admin:4445/admin/oauth2/introspect
            token_from:
              header: Oauth2-Token
        anonymous:
          enabled: true
          config:
            subject: anon
        cookie_session:
          enabled: true
          config:
            check_session_url: http://galoy-kratos-public:80/sessions/whoami
            preserve_path: true
            subject_from: identity.id
            extra_from: identity.traits
        unauthorized:
          enabled: true
      authorizers:
        allow:
          enabled: true
      mutators:
        noop:
          enabled: true
        id_token:
          enabled: true
          config:
            jwks_url: "http://galoy-oathkeeper-api:4456/.well-known/jwks.json"
            issuer_url: "galoy.io"
        header:
          enabled: true
          config:
            headers:
              X-Appcheck-Jti: "{{ print .Extra.jti }}"
              Authorization: '{{ .MatchContext.Header.Get "Authorization" }}'
      errors:
        fallback:
          - json
        handlers:
          json:
            enabled: true
            config:
              verbose: true
      access_rules:
        matching_strategy: regexp
    mutatorIdTokenJWKs: "http://galoy-oathkeeper-api:4456/.well-known/jwks.json"
    accessRules: |
      - id: anonymous-rest-auth
        upstream:
          url: http://api:4002
        match:
          url: "<(http|https)>://<[a-zA-Z0-9-.:]+>/auth/<.*>"
          methods:
            - GET
            - POST
            - OPTIONS
        authenticators:
          - handler: jwt
            config:
              trusted_issuers:
                - https://firebaseappcheck.googleapis.com/***********
              target_audience:
                - projects/***********
              jwks_urls:
                - https://firebaseappcheck.googleapis.com/v1beta/jwks
              token_from:
                header: Appcheck
          - handler: anonymous
        authorizer:
          handler: allow
        mutators:
          - handler: header
      - id: galoy-router
        upstream:
          url: http://galoy-router:80
        match:
          url: "<(http|https)>://<[a-zA-Z0-9-.:]+>/graphql"
          methods:
            - POST
            - GET
            - OPTIONS
        authenticators:
          - handler: cookie_session
            config:
              check_session_url: http://galoy-kratos-public:80/sessions/whoami
              preserve_path: true
              preserve_query: true
              subject_from: identity.id
              extra_from: "@this"
          - handler: bearer_token
            config:
              check_session_url: http://galoy-kratos-public:80/sessions/whoami
              preserve_path: true
              preserve_query: true
              subject_from: identity.id
              extra_from: "@this"
          - handler: bearer_token
            config:
              token_from:
                header: X-API-KEY
              forward_http_headers:
              - "X-API-KEY"
              check_session_url: "http://api-keys:5397/auth/check"
              force_method: GET
              preserve_path: true
              preserve_query: true
              subject_from: sub
              extra_from: "@this"
          - handler: anonymous
        authorizer:
          handler: allow
        mutators:
          - handler: id_token
            config:
              claims: '{"sub": "{{ print .Subject }}", "session_id": "{{ print .Extra.id }}", "expires_at": "{{ print .Extra.expires_at }}" }'
      - id: admin-backend
        upstream:
          url: http://graphql-admin:4001
          strip_path: /admin
        match:
          url: "<(http|https)>://<.*><[0-9]*>/admin/<.*>"
          methods:
            - GET
            - POST
            - OPTIONS
        authenticators:
          - handler: cookie_session
            config:
              check_session_url: http://galoy-kratos-public:80/sessions/whoami
              preserve_path: true
              preserve_query: true
              subject_from: identity.id
              extra_from: "@this"
          - handler: oauth2_introspection
            config:
              introspection_url: http://galoy-hydra-admin:4445/admin/oauth2/introspect
              token_from:
                header: Oauth2-Token
        authorizer:
          handler: allow
        mutators:
          - handler: id_token
            config:
              claims: '{"sub": "{{ print .Subject }}", "scope": "{{ print .Extra.scope }}" }'
  maester:
    enabled: false
kratos:
  secret:
    enabled: true
    nameOverride: ""
  ingress:
    admin:
      enabled: false
    public:
      enabled: false
  replicaCount: 2
  deployment:
    livenessProbe:
      periodSeconds: 1
  kratos:
    automigration:
      enabled: true
    config:
      dsn: *****************************************************
      cookies:
        domain: localhost # var.root_domain
      serve:
        public:
          request_log:
            disable_for_health: true
          base_url: http://galoy-kratos-public:80/
          cors:
            enabled: true
            allow_credentials: true
            allowed_methods:
              - POST
              - GET
              - PUT
              - PATCH
              - DELETE
            allowed_headers:
              - Authorization
              - Cookie
              - Content-Type
              - X-Session-Token
            exposed_headers:
              - Content-Type
              - Set-Cookie
            debug: true
        admin:
          request_log:
            disable_for_health: true
          base_url: http://galoy-kratos-admin:80/
      selfservice:
        default_browser_return_url: http://localhost:3000/
        methods:
          oidc:
            enabled: false
          webauthn:
            enabled: false
          totp:
            enabled: true
          password:
            enabled: true
          code:
            enabled: true
            config:
              # Defines how long the verification or the recovery code is valid for (default 1h)
              lifespan: 15m
        flows:
          registration:
            lifespan: 10m
            ui_url: http://localhost:3000/register
            after:
              password:
                hooks:
                  - hook: session
          login:
            ui_url: http://localhost:3000/login
            lifespan: 10m
            # this below make phone authentication fails even if there is no email in the schema
            # after:
            #   password:
            #     hooks:
            #     - hook: require_verified_address
          settings:
            ui_url: http://api:4002/settings
            privileged_session_max_age: 15m
            required_aal: aal1
            after:
              profile:
                hooks:
                  - hook: web_hook
                    config:
                      # url: http://e2e-tests:4002/auth/after_settings_hooks
                      # url: http://api:4002/kratos/registration
                      url: http://invalid-because-we-dont-want-profile-to-be-updated
                      method: POST
                      body: base64://ZnVuY3Rpb24oY3R4KSB7CiAgICBpZGVudGl0eV9pZDogaWYgc3RkLm9iamVjdEhhcyhjdHgsICJpZGVudGl0eSIpIHRoZW4gY3R4LmlkZW50aXR5LmlkIGVsc2UgbnVsbCwKICAgIHBob25lOiBpZiBzdGQub2JqZWN0SGFzKGN0eC5pZGVudGl0eS50cmFpdHMsICJwaG9uZSIpIHRoZW4gY3R4LmlkZW50aXR5LnRyYWl0cy5waG9uZSBlbHNlIG51bGwsCiAgICB0cmFuc2llbnRfcGF5bG9hZDogaWYgc3RkLm9iamVjdEhhcyhjdHguZmxvdywgInRyYW5zaWVudF9wYXlsb2FkIikgdGhlbiBjdHguZmxvdy50cmFuc2llbnRfcGF5bG9hZCBlbHNlIG51bGwsCiAgICBzY2hlbWFfaWQ6IGN0eC5pZGVudGl0eS5zY2hlbWFfaWQsCiAgICBmbG93X2lkOiBjdHguZmxvdy5pZCwKICAgIGZsb3dfdHlwZTogY3R4LmZsb3cudHlwZQp9Cg==
                      auth:
                        type: api_key
                        config:
                          name: Authorization
                          value: callback_api_key
                          in: header
          verification:
            use: code # Defines which method is used, one of 'code' or 'link'.
            enabled: true
            lifespan: 15m
            # notify_unknown_recipients: false
          recovery:
            enabled: true
            ui_url: http://api:4002/recovery
          error:
            ui_url: http://api:4002/error
          logout:
            after:
              default_browser_return_url: http://api:4002/login
      courier:
        smtp:
          connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true # TODO: need to make this a secret and connect to an SMTP server we verified our domain with (sendgrid, mailgun etc...) see https://www.ory.sh/docs/kratos/emails-sms/sending-emails-smtp
          from_address: <EMAIL>
          from_name: Blink Verification Code
        templates:
          recovery_code:
            valid:
              email:
                subject: base64://eW91ciBjb2RlCg==
                body:
                  # courier/template/courier/builtin/templates/recovery_code/valid/email.body.plaintext.gotmpl
                  # Hi,
                  # You can confirm access to your blink account by entering the following code:
                  # {{ .RecoveryCode }}
                  # Don't share this code with anyone. Our employee will never ask for this code
                  plaintext: base64://SGksCgpZb3UgY2FuIGNvbmZpcm0gYWNjZXNzIHRvIHlvdXIgYmxpbmsgYWNjb3VudCBieSBlbnRlcmluZyB0aGUgZm9sbG93aW5nIGNvZGU6Cgp7eyAuUmVjb3ZlcnlDb2RlIH19CgpEb24ndCBzaGFyZSB0aGlzIGNvZGUgd2l0aCBhbnlvbmUuIE91ciBlbXBsb3llZSB3aWxsIG5ldmVyIGFzayBmb3IgdGhpcyBjb2RlCg==
                  html: base64://SGksCgpZb3UgY2FuIGNvbmZpcm0gYWNjZXNzIHRvIHlvdXIgYmxpbmsgYWNjb3VudCBieSBlbnRlcmluZyB0aGUgZm9sbG93aW5nIGNvZGU6Cgp7eyAuUmVjb3ZlcnlDb2RlIH19CgpEb24ndCBzaGFyZSB0aGlzIGNvZGUgd2l0aCBhbnlvbmUuIE91ciBlbXBsb3llZSB3aWxsIG5ldmVyIGFzayBmb3IgdGhpcyBjb2RlCg==
      tracing: {}
      identity:
        schemas:
          - id: phone_no_password_v0
            url: file:///etc/config/identity.schema.json
          - id: username_password_deviceid_v0
            url: file:///etc/config/username_password_deviceid_v0.identity.schema.json
          - id: email_no_password_v0
            url: file:///etc/config/email_no_password_v0.identity.schema.json
          - id: phone_email_no_password_v0
            url: file:///etc/config/phone_email_no_password_v0.identity.schema.json
        default_schema_id: phone_no_password_v0
      session:
        lifespan: "9360h" # 1 year and 1 month
        earliest_possible_extend: "9360h" # 1 year and 1 month
        whoami:
          required_aal: highest_available
      log:
        level: debug
        format: json
        leak_sensitive_values: true
      ciphers:
        algorithm: xchacha20-poly1305
      hashers:
        algorithm: bcrypt
        bcrypt:
          cost: 8
    identitySchemas:
      "identity.schema.json": |
        {
          "$id": "https://schemas.ory.sh/presets/kratos/quickstart/phone-no-password/identity.schema.json",
          "$schema": "http://json-schema.org/draft-07/schema#",
          "title": "PhoneOrEmailPassword",
          "type": "object",
          "properties": {
            "traits": {
              "type": "object",
              "properties": {
                "phone": {
                  "type": "string",
                  "format": "string",
                  "title": "phone",
                  "minLength": 3,
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    }
                  }
                },
                "email": {
                  "title": "E-Mail",
                  "type": "string",
                  "format": "email",
                  "minLength": 3,
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    }
                  }
                }
              },
              "additionalProperties": false
            }
          }
        }
      "username_password_deviceid_v0.identity.schema.json": |
        {
          "$id": "https://schemas.ory.sh/presets/kratos/quickstart/username-password-deviceid/identity.schema.json",
          "$schema": "http://json-schema.org/draft-07/schema#",
          "title": "UsernamePasswordDeviceId",
          "type": "object",
          "properties": {
            "traits": {
              "type": "object",
              "properties": {
                "username": {
                  "type": "string",
                  "minLength": 36,
                  "maxLength": 36,
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      "email_no_password_v0.identity.schema.json": |
        {
          "$id": "http://mydomain.com/schemas/v2/customer.schema.json",
          "$schema": "http://json-schema.org/draft-07/schema#",
          "title": "A email user",
          "type": "object",
          "properties": {
            "traits": {
              "type": "object",
              "properties": {
                "email": {
                  "title": "E-Mail",
                  "type": "string",
                  "format": "email",
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    },
                    "verification": {
                      "via": "email"
                    },
                    "recovery": {
                      "via": "email"
                    }
                  }
                }
              },
              "required": ["email"],
              "additionalProperties": false
            }
          }
        }
      "phone_email_no_password_v0.identity.schema.json": |
        {
          "$id": "http://mydomain.com/schemas/v2/customer.schema.json",
          "$schema": "http://json-schema.org/draft-07/schema#",
          "title": "A phone+email user",
          "type": "object",
          "properties": {
            "traits": {
              "type": "object",
              "properties": {
                "email": {
                  "title": "E-Mail",
                  "type": "string",
                  "format": "email",
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    },
                    "verification": {
                      "via": "email"
                    },
                    "recovery": {
                      "via": "email"
                    }
                  }
                },
                "phone": {
                  "title": "Phone",
                  "type": "string",
                  "format": "string",
                  "ory.sh/kratos": {
                    "credentials": {
                      "password": {
                        "identifier": true
                      }
                    }
                  }
                }
              },
              "required": ["email", "phone"],
              "additionalProperties": false
            }
          }
        }
    emailTemplates: {}
hydra:
  enabled: true
  replicaCount: 2
  maester:
    enabled: false
  hydra:
    config:
      dsn: memory
    dev: true
    serve:
      cookies:
        same_site_mode: Lax
    urls:
      self:
        issuer: http://127.0.0.1:4444
      consent: http://127.0.0.1:3000/consent
      login: http://127.0.0.1:3000/login
      logout: http://127.0.0.1:3000/logout
router:
  supergraphFilePath: apollo-router/supergraph.graphql
  extraEnvVars:
    - name: APOLLO_ROUTER_SUPERGRAPH_PATH
      value: /etc/apollo/supergraph-schema.graphql
    - name: ALWAYS_UPGRADE_DEPLOYMENT
      value: "{{ now }}"
  extraVolumeMounts:
    - name: supergraph
      mountPath: /etc/apollo/supergraph-schema.graphql
      subPath: supergraph-schema.graphql
  extraVolumes:
    - name: supergraph
      configMap:
        name: galoy-supergraph
  router:
    configuration:
      include_subgraph_errors:
        all: true
      sandbox:
        enabled: true
      homepage:
        enabled: false
      supergraph:
        introspection: true
        path: /graphql
      override_subgraph_url:
        public: http://api:4002/graphql
      headers:
        all:
          request:
            - propagate:
                matching: .*
      traffic_shaping:
        router:
          timeout: 180s # If client requests to the router take longer
        all:
          timeout: 180s # If subgraph requests takes longer
      telemetry:
        tracing:
          otlp:
            endpoint: http://localhost:4318
            protocol: http
