apiVersion: v2
name: galoy
description: A Helm chart for galoy servers
# A chart can be either an 'application' or a 'library' chart.
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application
# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.34.7-dev
# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
appVersion: 0.21.46
dependencies:
  - name: redis
    repository: https://charts.bitnami.com/bitnami
    version: 20.11.3
  - name: mongodb
    repository: https://charts.bitnami.com/bitnami
    version: 15.6.26
  - name: postgresql
    repository: https://charts.bitnami.com/bitnami
    version: 16.4.16
    condition: postgresql.enabled
  - name: oathkeeper
    repository: https://k8s.ory.sh/helm/charts
    version: 0.39.1
  - name: kratos
    repository: https://k8s.ory.sh/helm/charts
    version: 0.39.1
  - name: hydra
    repository: https://k8s.ory.sh/helm/charts
    version: 0.47.0
    condition: hydra.enabled
  - name: router
    repository: oci://ghcr.io/apollographql/helm-charts
    version: 1.25.0
