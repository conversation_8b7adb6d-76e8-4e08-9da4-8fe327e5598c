# trigger_ref: ${trigger_ref}

secrets:
  create: false

galoy:
  network: ${btc_network}

  config:
    lightningAddressDomain: ${lightning_address_domain}
    lightningAddressDomainAliases: ${lightning_address_domain_aliases}
    accounts:
      initialStatus: ${accounts_initial_status}
      enableIpCheck: true
      enablePhoneCheck: ${accounts_enable_phone_check}
      denyIPCountries: ${accounts_deny_ip_countries}
      denyPhoneCountries: ${accounts_deny_phone_countries}
    dealer:
      usd:
        hedgingEnabled: true
    ipRecording:
      enabled: ${ip_recording_enabled}
      proxyChecking:
        enabled: ${proxy_checking_enabled}
    quizzes:
      allowPhoneCountries: ${quizzes_allow_phone_countries}
      denyPhoneCountries: ${quizzes_deny_phone_countries}
      denyASNs: ${quizzes_deny_asns}
    smsAuthUnsupportedCountries: ${unsupported_sms_countries}
    whatsAppAuthUnsupportedCountries: ${unsupported_whatsapp_countries}
    telegramAuthUnsupportedCountries: ${unsupported_telegram_countries}
    captcha:
      mandatory: true
    fees:
      withdraw:
        method: ${withdraw_method}
        ratioAsBasisPoints: ${withdraw_ratio_basis_points_on_imbalance}
        defaultMin: ${withdraw_min_fee}
        threshold: ${withdraw_fee_threshold}
      deposit:
        defaultMin: 5000
        threshold: 1000000
        ratioAsBasisPoints: 0
    bria:
      hotWalletName: ${bria_hot_wallet_name}
      queueNames:
        fast: ${bria_fast_queue_name}
      coldStorage:
        walletName: ${bria_cold_wallet_name}
        hotToColdRebalanceQueueName: ${bria_fast_queue_name}
    buildVersion:
      android:
        minBuildNumber: ${android_min_build_number}
        lastBuildNumber: ${android_last_build_number}
      ios:
        minBuildNumber: ${ios_min_build_number}
        lastBuildNumber: ${ios_last_build_number}
    funder: ${funder_user_name}
    onChainWallet:
      dustThreshold: 546
    coldStorage:
      minOnChainHotWalletBalance: 2******** # 2.5 BTC
      minRebalanceSize: ******** # 0.5 BTC
      maxHotWalletBalance: ${max_hot_wallet}
    rateLimits:
      requestCodePerIp:
        points: ${request_code_per_ip_points}
      addQuizPerIp:
        points: 60
        duration: 1800
        blockDuration: 604800
    accountLimits:
      withdrawal:
        level:
          0: ${withdrawal_level_zero} # cents
          1: ${withdrawal_level_one} # cents
          2: ${withdrawal_level_two} # cents
          3: ${withdrawal_level_three} # cents
      intraLedger:
        level:
          0: ${intraledger_level_zero} # cents
          1: ${intraledger_level_one} # cents
          2: ${intraledger_level_two} # cents
          3: ${intraledger_level_three} # cents
      tradeIntraAccount:
        level:
          0: ${trade_intra_account_level_zero} # cents
          1: ${trade_intra_account_level_one} # cents
          2: ${trade_intra_account_level_two} # cents
          3: ${trade_intra_account_level_three} # cents
    cronConfig:
      rebalanceEnabled: ${rebalance_enabled}
    lndScbBackupBucketName: ${backups_bucket_name}
    skipFeeProbeConfig:
      pubkey:
      - "038f8f113c580048d847d6949371726653e02b928196bad310e3eda39ff61723f6" # Muun
      - "03a6ce61fcaacd38d31d4e3ce2d506602818e3856b4b44faff1dde9642ba705976" # Muun
      - "025eee29468652d3a05c7ecb32c50ab5f3d3ebfa2115acccacae905ceaf6f30a23" # Muun
      - "02dd3fcdaa17b9bc83bf7138fbea85d0e83385a68b5fc8f9933658c8ee04644f68" # Fedi
      - "03864ef025fde8fb587d989186ce6a4a186895ee44a926bfc370e2c366597a3f8f" # ACINQ
      chanId:
      - "1x0x0" # Breez

  jwtSecretExistingSecret:
    name: jwt

  api:
    firebaseNotifications:
      enabled: true
    unsecureDefaultLoginCode:
      enabled: ${unsecure_default_login_code_enabled}
    ingress:
      enabled: true
      hosts: ${api_hosts}
      limitRpm: ${api_limit_rpm}
      limitBurstMultiplier: ${api_limit_burst_multiplier}
      limitConnections: ${api_limit_connections}

  admin:
    ingress:
      enabled: true
      hosts: ${admin_api_hosts}

  websocket:
    ingress:
      enabled: true
      hosts: ${websocket_hosts}

  consent:
    graphqlPublicApi: http://${core_via_oathkeeper_proxy}/graphql
    coreAuthUrl: http://${core_via_oathkeeper_proxy}/auth
    ingress:
      enabled: true
      hosts: ${consent_hosts}
  apiKeys:
    config:
      keyPrefix: ${api_keys_prefix}
  notifications:
    config:
      db:
        poolSize: 40
      importFromKratos: false
      smtp:
        enabled: true
        username: ${notifications_smtp_username}
        fromEmail: ${notifications_from_email}
        fromName: ${notifications_from_name}
        relayHost: "smtp.mailgun.org"
        relayPort: 587
      linkEmailReminder:
        accountLivenessThresholdMinutes: 1440 # 1 day
        accountAgeThresholdMinutes: 10080 # 7 days


  trigger:
    backups:
      gcs:
        enabled: true
        bucketName: ${backups_bucket_name}
      dropbox:
        enabled: true
      s3:
        enabled: true
        bucketName: ${backups_bucket_name}

  lndPriority: ${lnd_priority}

  lnd1:
    dns: ${lnd1_host}
    credsChecksum: ${lnd1_credentials_checksum}

  lnd2:
    dns: ${lnd2_host}
    credsChecksum: ${lnd2_credentials_checksum}

  bria:
    host: ${bria_host}

  dealer:
    host: stablesats-price.${stablesats_namespace}.svc.cluster.local
    port: 3325

  kratos:
    publicApiUrl: ${kratos_public_api_url}
    adminApiUrl: ${kratos_admin_api_url}

tracing:
  otelExporterOtlpEndpoint: http://${otel_exporter_otlp_endpoint}
  otelExporterGrpcEndpoint: http://${otel_exporter_grpc_endpoint}
  prefix: ${tracing_prefix}

mongodb:
  auth:
    existingSecret: galoy-mongodb
    usernames:
    - ${mongo_username}
    databases:
    - ${mongo_database}
  persistence:
    storageClass: premium-rwo
    size: ${mongodb_ssd_size}

postgresql:
  enabled: false

price:
  # realtime price config
  realtime:
    tracing:
      otelExporterOtlpEndpoint: http://${otel_exporter_otlp_endpoint}
      otelServiceName: ${price_realtime_tracing_service_name}
    config:
      quotes:
        - { code: "USD", symbol: "$", name: "US Dollar", flag: "🇺🇸" }
        - { code: "EUR", symbol: "€", name: "Euro", flag: "🇪🇺" }
        - { code: "AED", symbol: "د.إ", name: "UAE Dirham", flag: "🇦🇪" }
        - { code: "ALL", symbol: "L", name: "Albanian Lek", flag: "🇦🇱" }
        - { code: "ANG", symbol: "ƒ", name: "Netherlands Antillean Guilder", flag: "🇨🇼" }
        - { code: "ARS", symbol: "$", name: "Argentine Peso", flag: "🇦🇷" }
        - { code: "AUD", symbol: "$", name: "Australian Dollar", flag: "🇦🇺" }
        - { code: "AWG", symbol: "ƒ", name: "Aruban Florin", flag: "🇦🇼" }
        - { code: "BGN", symbol: "лв", name: "Bulgarian Lev", flag: "🇧🇬" }
        - { code: "BOB", symbol: "Bs", name: "Bolivian Boliviano", flag: "🇧🇴" }
        - { code: "BRL", symbol: "R$", name: "Brazilian Real", flag: "🇧🇷" }
        - { code: "BZD", symbol: "BZ$", name: "Belize Dollar", flag: "🇧🇿" }
        - { code: "CAD", symbol: "$", name: "Canadian Dollar", flag: "🇨🇦" }
        - { code: "CHF", symbol: "CHF", name: "Swiss Franc", flag: "🇨🇭" }
        - { code: "CLP", symbol: "$", name: "Chilean Peso", flag: "🇨🇱" }
        - { code: "COP", symbol: "$", name: "Colombian Peso", flag: "🇨🇴" }
        - { code: "CRC", symbol: "₡", name: "Costa Rican Colón", flag: "🇨🇷" }
        - { code: "CZK", symbol: "Kč", name: "Czech Koruna", flag: "🇨🇿" }
        - { code: "DKK", symbol: "kr.", name: "Danish Krone", flag: "🇩🇰" }
        - { code: "DOP", symbol: "RD$", name: "Dominican Peso", flag: "🇩🇴" }
        - { code: "EGP", symbol: "E£", name: "Egyptian Pound", flag: "🇪🇬" }
        - { code: "ETB", symbol: "Br", name: "Ethiopian Birr", flag: "🇪🇹" }
        - { code: "GBP", symbol: "£", name: "Pound Sterling", flag: "🇬🇧" }
        - { code: "GHS", symbol: "GH₵‎", name: "Ghanaian Cedi", flag: "🇬🇭" }
        - { code: "GTQ", symbol: "Q", name: "Guatemalan Quetzal", flag: "🇬🇹" }
        - { code: "HKD", symbol: "HK$", name: "Hong Kong Dollar", flag: "🇭🇰" }
        - { code: "HNL", symbol: "L", name: "Honduran Lempira", flag: "🇭🇳" }
        - { code: "HTG", symbol: "G", name: "Haitian Gourde", flag: "🇭🇹" }
        - { code: "HUF", symbol: "Ft", name: "Hungarian Forint", flag: "🇭🇺" }
        - { code: "IDR", symbol: "Rp", name: "Indonesian Rupiah", flag: "🇮🇩" }
        - { code: "ILS", symbol: "₪", name: "Israeli Shekel", flag: "🇮🇱" }
        - { code: "INR", symbol: "₹", name: "Indian Rupee", flag: "🇮🇳" }
        - { code: "JMD", symbol: "J$", name: "Jamaican Dollar", flag: "🇯🇲" }
        - { code: "JPY", symbol: "¥", name: "Japanese Yen", flag: "🇯🇵" }
        - { code: "KES", symbol: "KSh", name: "Kenyan Shilling", flag: "🇰🇪" }
        - { code: "KRW", symbol: "₩", name: "South Korean Won", flag: "🇰🇷" }
        - { code: "LBP", symbol: "£", name: "Lebanese Pound", flag: "🇱🇧" }
        - { code: "LKR", symbol: "Rs", name: "Sri Lankan Rupee", flag: "🇱🇰" }
        - { code: "LRD", symbol: "$", name: "Liberian Dollar", flag: "🇱🇷" }
        - { code: "MAD", symbol: "د.م.", name: "Moroccan Dirham", flag: "🇲🇦" }
        - { code: "MXN", symbol: "$", name: "Mexican Peso", flag: "🇲🇽" }
        - { code: "MUR", symbol: "Rs", name: "Mauritian Rupee", flag: "🇲🇺" }
        - { code: "NAD", symbol: "N$", name: "Namibian Dollar", flag: "🇳🇦" }
        - { code: "NGN", symbol: "₦", name: "Nigerian Naira", flag: "🇳🇬" }
        - { code: "NIO", symbol: "C", name: "Nicaraguan Córdoba", flag: "🇳🇮" }
        - { code: "NOK", symbol: "kr", name: "Norwegian krone", flag: "🇳🇴" }
        - { code: "NZD", symbol: "$", name: "New Zealand Dollar", flag: "🇳🇿" }
        - { code: "PEN", symbol: "S/.", name: "Peruvian Nuevo Sol", flag: "🇵🇪" }
        - { code: "PHP", symbol: "₱", name: "Philippine Peso", flag: "🇵🇭" }
        - { code: "PKR", symbol: "₨", name: "Pakistani Rupee", flag: "🇵🇰" }
        - { code: "PLN", symbol: "zł", name: "Polish Zloty", flag: "🇵🇱" }
        - { code: "PYG", symbol: "Gs", name: "Paraguayan Guarani", flag: "🇵🇾" }
        - { code: "RON", symbol: "RON", name: "Romanian Leu", flag: "🇷🇴" }
        - { code: "RSD", symbol: "RSD", name: "Serbian Dinar", flag: "🇷🇸" }
        - { code: "SEK", symbol: "kr", name: "Swedish krona", flag: "🇸🇪" }
        - { code: "SRD", symbol: "SRD", name: "Surinamese Dollar", flag: "🇸🇷" }
        - { code: "THB", symbol: "฿", name: "Thai Baht", flag: "🇹🇭" }
        - { code: "TRY", symbol: "₺", name: "Turkish Lira", flag: "🇹🇷" }
        - { code: "TTD", symbol: "TT$", name: "Trinidad and Tobago Dollar", flag: "🇹🇹" }
        - { code: "TZS", symbol: "TSh", name: "Tanzanian Shilling", flag: "🇹🇿" }
        - { code: "UAH", symbol: "₴", name: "Ukrainian Hryvnia", flag: "🇺🇦" }
        - { code: "UGX", symbol: "USh", name: "Ugandan Shilling", flag: "🇺🇬" }
        - { code: "UYU", symbol: "$U", name: "Uruguayan Peso", flag: "🇺🇾" }
        - { code: "VES", symbol: "Bs.", name: "Venezuelan Bolívar", flag: "🇻🇪" }
        - { code: "VND", symbol: "₫", name: "Vietnamese Dong", flag: "🇻🇳" }
        - { code: "XAF", symbol: "FCFA", name: "CFA Franc BEAC", flag: "" }
        - { code: "XOF", symbol: "CFA", name: "CFA Franc BCEAO", flag: "" }
        - { code: "XPF", symbol: "F", name: "CFP Franc", flag: "🇵🇫" }
        - { code: "ZAR", symbol: "R", name: "South African Rand", flag: "🇿🇦" }
        - { code: "ZMW", symbol: "K", name: "Zambian Kwacha", flag: "🇿🇲" }
  history:
    tracing:
      otelExporterOtlpEndpoint: http://${otel_exporter_otlp_endpoint}
      otelServiceName: ${price_history_tracing_service_name}
    postgresqlHost: ${price_history_pg_host}

redis:
  auth:
    existingSecret: "galoy-redis-pw"
    existingSecretPasswordKey: "redis-password"

oathkeeper:
  secret:
    enabled: false
  oathkeeper:
    config:
      authenticators:
        cookie_session:
          config:
            check_session_url: ${oathkeeper_admin_cookie_session_url}
      tracing:
        service_name: ${oathkeeper_tracing_service_name}
        provider: otel
        providers:
          otlp:
            server_url: ${otel_exporter_otlp_endpoint}
            insecure: true
    accessRules: |
      - id: anonymous-rest-auth
        upstream:
          url: http://api:4002
        match:
          url: "<(http|https)>://<[a-zA-Z0-9-.:]+>/auth/<.*>"
          methods:
            - GET
            - POST
            - OPTIONS
        authenticators:
          - handler: jwt
            config:
              trusted_issuers:
                - https://firebaseappcheck.googleapis.com/72279297366
              target_audience:
                - projects/72279297366
              jwks_urls:
                - https://firebaseappcheck.googleapis.com/v1beta/jwks
              token_from:
                header: Appcheck
          - handler: anonymous
        authorizer:
          handler: allow
        mutators:
          - handler: header
            config:
              headers:
                X-Appcheck-Jti: "{{ print .Extra.jti }}"
      - id: galoy-router
        upstream:
          url: http://galoy-router:80
        match:
          url: "<(http|https)>://<[a-zA-Z0-9-.:]+>/graphql"
          methods:
            - POST
            - GET
            - OPTIONS
        authenticators:
          - handler: bearer_token
            config:
              check_session_url: http://galoy-kratos-public:80/sessions/whoami
              preserve_path: true
              preserve_query: true
              subject_from: identity.id
              extra_from: "@this"
          - handler: bearer_token
            config:
              token_from:
                header: X-API-KEY
              forward_http_headers:
              - "X-API-KEY"
              check_session_url: "http://api-keys:5397/auth/check"
              force_method: GET
              preserve_path: true
              preserve_query: true
              subject_from: sub
              extra_from: "@this"
          - handler: oauth2_introspection
            config:
              introspection_url: http://galoy-hydra-admin:4445/admin/oauth2/introspect
              token_from:
                header: Oauth2-Token
          - handler: anonymous
        authorizer:
          handler: allow
        mutators:
          - handler: id_token
            config:
              claims: '{"sub": "{{ print .Subject }}", "session_id": "{{ print .Extra.id }}", "expires_at": "{{ print .Extra.expires_at }}", "scope": "{{ print .Extra.scope }}", "client_id": "{{ print .Extra.client_id }}"}'
      - id: admin-backend
        upstream:
          url: http://graphql-admin:4001
          strip_path: /admin
        match:
          url: "<(http|https)>://<.*><[0-9]*>/admin/<.*>"
          methods:
            - GET
            - POST
            - OPTIONS
        authenticators:
          - handler: cookie_session
            config:
              check_session_url: "${oathkeeper_admin_cookie_session_url}"
              preserve_path: true
              preserve_query: true
              subject_from: user.email
              extra_from: "@this"
              force_method: GET
          - handler: oauth2_introspection
            config:
              introspection_url: http://galoy-hydra-admin:4445/admin/oauth2/introspect
              token_from:
                header: Oauth2-Token
        authorizer:
          handler: allow
        mutators:
          - handler: id_token
            config:
              claims: '{"sub": "{{ print .Subject }}", "scope": "{{ print .Extra.scope }}" }'

kratos:
  secret:
    enabled: false
    nameOverride: ${kratos_secret_name}
  ingress:
    public:
      enabled: true
      annotations:
        cert-manager.io/cluster-issuer: letsencrypt-issuer
        nginx.ingress.kubernetes.io/rewrite-target: /$1
      className: nginx
      hosts:
        - host: ${kratos_public_host}
          paths:
            - path: "/.kratos/(.*)"
              pathType: Prefix
      tls:
       - secretName: kratos-public-tls
         hosts:
         - ${kratos_public_host}
  kratos:
    config:
      cookies:
        domain: ${kratos_cookie_domain}

      serve:
        public:
          base_url: https://${kratos_public_host}/.kratos
          cors:
            allowed_origins:
              - https://*.${kratos_cookie_domain}
      selfservice:
        flows:
          settings:
            ui_url: https://dummy/settings
            privileged_session_max_age: 15m
            after:
              profile:
                hooks:
                  - hook: web_hook
                    config:
                      url: http://invalid-because-we-dont-want-profile-to-be-updated
                      method: POST
                      body: base64://ZnVuY3Rpb24oY3R4KSB7CiAgICBpZGVudGl0eV9pZDogaWYgc3RkLm9iamVjdEhhcyhjdHgsICJpZGVudGl0eSIpIHRoZW4gY3R4LmlkZW50aXR5LmlkIGVsc2UgbnVsbCwKICAgIHBob25lOiBpZiBzdGQub2JqZWN0SGFzKGN0eC5pZGVudGl0eS50cmFpdHMsICJwaG9uZSIpIHRoZW4gY3R4LmlkZW50aXR5LnRyYWl0cy5waG9uZSBlbHNlIG51bGwsCiAgICB0cmFuc2llbnRfcGF5bG9hZDogaWYgc3RkLm9iamVjdEhhcyhjdHguZmxvdywgInRyYW5zaWVudF9wYXlsb2FkIikgdGhlbiBjdHguZmxvdy50cmFuc2llbnRfcGF5bG9hZCBlbHNlIG51bGwsCiAgICBzY2hlbWFfaWQ6IGN0eC5pZGVudGl0eS5zY2hlbWFfaWQsCiAgICBmbG93X2lkOiBjdHguZmxvdy5pZCwKICAgIGZsb3dfdHlwZTogY3R4LmZsb3cudHlwZQp9Cg==
                      auth:
                        type: api_key
                        config:
                          name: Authorization
                          value: The-Value-of-My-Key
                          in: header
      courier:
        smtp:
          from_address: noreply@${kratos_email_domain}
          from_name: ${kratos_email_sender_name}
        template_override_path: /conf/courier-templates
        templates:
          recovery_code:
            valid:
              email:
                # Your confirmation code is {{ .RecoveryCode }}
                subject: base64://WW91ciBjb25maXJtYXRpb24gY29kZSBpcyB7eyAuUmVjb3ZlcnlDb2RlIH19
                body:
                  plaintext: base64://${recovery_email_plaintext}
                  html: base64://${recovery_email_html}

      tracing:
        service_name: ${kratos_tracing_service_name}
        provider: otel
        providers:
          otlp:
            server_url: ${otel_exporter_otlp_endpoint}
            insecure: true

      identity:
        schemas:
%{ for kis in kratos_identity_schemas ~}
        - id: ${kis.name}
          url: file:///etc/config/${kis.name}
%{ endfor ~}
        default_schema_id: ${kratos_identity_schemas[0].name}

    identitySchemas:
%{ for kis in kratos_identity_schemas ~}
      ${kis.name}: |
        ${indent(8, kis.schema)}
%{ endfor ~}

hydra:
  secret:
    enabled: false
  hydra:
    automigration:
      enabled: true
    config:
      serve:
        cookies:
          same_site_mode: Lax
          secure: true
          domain: ${hydra_cookie_domain}
      tracing:
        service_name: ${hydra_tracing_service_name}
        provider: otel
        providers:
          otlp:
            server_url: ${otel_exporter_otlp_endpoint}
            insecure: true
      urls:
        self:
          admin: http://galoy-hydra-admin.${galoy_namespace}.svc.cluster.local:4445
          public: https://${hydra_public_host}
        consent: https://${consent_host}/consent
        login: https://${consent_host}/login
        logout: https://${consent_host}/logout

  ingress:
    public:
      enabled: true
      annotations:
        cert-manager.io/cluster-issuer: letsencrypt-issuer
        kubernetes.io/ingress.class: nginx
      className: nginx
      hosts:
        - host: ${hydra_public_host}
          paths:
            - path: /
              pathType: ImplementationSpecific
      tls:
        - secretName: hydra-public-tls
          hosts:
          - ${hydra_public_host}

router:
  extraVolumes:
    - name: supergraph
      configMap:
        name: galoy-router-supergraph
  replicaCount: 2
  router:
    configuration:
      override_subgraph_url:
        circles: ${circles_subgraph_url}
        galoy: "http://api:4002/graphql"
        api-keys: "http://api-keys:5397/graphql"
        notifications: "http://notifications:6684/graphql"
        blink-kyc: ${blink_kyc_subgraph_url}
      cors:
        origins:
          - https://studio.apollographql.com
%{ for origin in extra_cors_allowed_origins ~}
          - "${origin}"
%{ endfor ~}
        match_origins:
          - "^https://([a-z0-9]+[.])*galoy[.]io$"
          - "^https://([a-z0-9]+[.])*bbw[.]sv$"
          - "^https://([a-z0-9]+[.])*blink[.]sv$"
        allow_credentials: true # for cookie auth
      telemetry:
        tracing:
          otlp:
            endpoint: http://${otel_exporter_grpc_endpoint}
            protocol: grpc
          trace_config:
            service_name: ${tracing_prefix}-router
