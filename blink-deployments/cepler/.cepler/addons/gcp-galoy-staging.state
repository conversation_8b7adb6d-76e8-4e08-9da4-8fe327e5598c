---
version: 651
current:
  head_commit: c7c916a71dbb7e685ed9903e6e146643c86ce0ff
  files:
    "{latest}/gcp/galoy-staging/addons/admin-panel-scaling.yml":
      file_hash: 3a74b045f4271dd6db03b1c6c37cc12ad360c87e
      from_commit: 1009ac5ee37965671317990ca6ef5b2727df54f9
      message: "chore: increase admin panel resources (#5016)"
    "{latest}/gcp/galoy-staging/addons/api-dashboard-scaling.yml":
      file_hash: 3a74b045f4271dd6db03b1c6c37cc12ad360c87e
      from_commit: 3b3bb9bcc80a565bbcb0bbae17189cb7bd2897e6
      message: "chore: add api-dashboard scaling"
    "{latest}/gcp/galoy-staging/addons/galoy-pay-scaling.yml":
      file_hash: d62d8352d801294fbd1cdc41412b517bfce62094
      from_commit: 40b78df1e7ccfed8df042efbf9d9ef3478016df2
      message: "chore: add resources for galoy-pay (#3872)"
    "{latest}/gcp/galoy-staging/addons/main.tf":
      file_hash: 88769ba075a592ff8e2038637d1ecd2ec8ea4321
      from_commit: c4ffe1aef34af0d355c76d796d02bb43c59349bc
      message: "chore: add blink team with blinkbtc email to staging admin panel (#7751)"
    "{latest}/gcp/galoy-staging/addons/map-scaling.yml":
      file_hash: d62d8352d801294fbd1cdc41412b517bfce62094
      from_commit: f7d55227296e69a8697e1db813401518cccb41af
      message: "chore: update scaling values (#6255)"
    "{latest}/gcp/galoy-staging/addons/voucher-scaling.yml":
      file_hash: d62d8352d801294fbd1cdc41412b517bfce62094
      from_commit: 66cb08d01b1f59c7d505176e236f13f62e8c7b48
      message: "chore: adding voucher scaling (#6920)"
    "{latest}/gcp/galoy-staging/shared/main.tf":
      file_hash: b9300891f1ba0c7c41a42d2786afd4e8f2a1259a
      from_commit: 2e90110d05d3c4031acda2613a7bf35905512982
      message: "chore: expose unsupported countries from shared module"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/services/addons/admin-panel-values.yml.tmpl":
      file_hash: c63015b9c844600e4477745df7c99c331a59fa7f
      from_commit: bf27abc0bbb5c6637474388e6a69629755220302
      message: "fix: admin panel auth emails injection"
    "{latest}/modules/services/addons/admin-panel.tf":
      file_hash: a377280552287635c930a899aa766083f0e8ee7f
      from_commit: ca1c66a5613adeeb9488b594eaf9dd718d20cefc
      message: "chore: remove github oauth from admin panel (#6472)"
    "{latest}/modules/services/addons/api-dashboard-values.yml.tmpl":
      file_hash: db79ab29514f7901d4bd52ea1c80cd8aef05ba0f
      from_commit: c296b9ef9e5466480db23fa85fb5514b376211d1
      message: "chore: make tld_domain work for dashboard"
    "{latest}/modules/services/addons/api-dashboard.tf":
      file_hash: 6d4366e1ca20c3852915f00c1513d0ca5fa08bd9
      from_commit: ed478d7271238c1c6059b087a58a26942fe91f87
      message: "chore: updated hydra redirect uri (#6614)"
    "{latest}/modules/services/addons/galoy-pay-values.yml.tmpl":
      file_hash: f2a36d35e55752d4a669bbeaa8ff14ddfff09bae
      from_commit: 2aa972212ce06b6bf737a9ada6eebfb70919f9fc
      message: "chore(pay): adding otel variables (#6842)"
    "{latest}/modules/services/addons/legacy_domain_ingress.tf":
      file_hash: fb2145e3f556eef211b47c8b324c57e0401eaff4
      from_commit: 4c35d2af39c09064e641d6850fd0fd958e767be2
      message: "chore: add www host to legacy website redirect"
    "{latest}/modules/services/addons/main.tf":
      file_hash: 7c7b989127234106c1a191d87aac0b16e6269830
      from_commit: 473a7df369daf958181489a5993a8321a29ed6d4
      message: "chore: Upgrade pg provider versions (#7668)"
    "{latest}/modules/services/addons/map-values.yml.tmpl":
      file_hash: a5bc4bc31d683b4d58d3ba2b888bbe0b514add5e
      from_commit: 59d162913288e7be24fa039993f2f39a9c42482f
      message: "chore: disable secret creation in map"
    "{latest}/modules/services/addons/map.tf":
      file_hash: c45a7c88ecff7d48ad8c00694bf48e06177d3e83
      from_commit: a6d9a39615b45c5959ba59c2e853ac74a2a356b0
      message: "chore: redirect `maps.xyz` to `map.xyz` (#6406)"
    "{latest}/modules/services/addons/tld_ingress.tf":
      file_hash: aed5a920efce0d2e6bc451a63701c40648dc026b
      from_commit: 7908ec4d9a8d94e3fa4dc806f2b6f5e60bb9c19c
      message: "chore: add rate limits to lnurl ingress (#7879)"
    "{latest}/modules/services/addons/variables.tf":
      file_hash: 6464fd5f3dd0a7f30fc1e9c59a122c93643917db
      from_commit: 3bc409d9ab95cdb34749dd068b1537cce3638282
      message: "chore: reactivate voucher in prod (#7857)"
    "{latest}/modules/services/addons/vendor/admin-panel/chart/Chart.yaml":
      file_hash: 073b0270d787ac010b79585a48778ad78e54971a
      from_commit: da8a9af483e5edbf71488f6ce4772bed7c167ff9
      message: "chore: bump admin-panel-chart to '520803d6db8810710e4bd943ccfa2291408aa774'"
    "{latest}/modules/services/addons/vendor/admin-panel/chart/templates/_helpers.tpl":
      file_hash: 3be2e919539a24450aadc92df65ade43624b21b0
      from_commit: 81254bd131910c3e6d404407a6120df3c3f37cc7
      message: Add admin panel staging
    "{latest}/modules/services/addons/vendor/admin-panel/chart/templates/deployment.yaml":
      file_hash: f120b7bea22d596a6fe4b6d951bec6d7ad3085f7
      from_commit: e035a012b29214a5d7f7599473571f9bc1082798
      message: "chore: bump admin-panel-chart to '81ba086fa5daaa2722f35a95b795efbd6604f19e'"
    "{latest}/modules/services/addons/vendor/admin-panel/chart/templates/ingress.yaml":
      file_hash: 347de319aa81a2f3d7c87a946fcd7ee0d1b11fbb
      from_commit: 19c66022ff2e77c31411fd1fec335d42e2967493
      message: "chore: bump admin-panel-chart to '73f3b8d6008bff83843942de250b3c0586e0b445'"
    "{latest}/modules/services/addons/vendor/admin-panel/chart/templates/secrets.yaml":
      file_hash: 79269ad67fa17ce80ff6468b5c75808d3bd418e6
      from_commit: e035a012b29214a5d7f7599473571f9bc1082798
      message: "chore: bump admin-panel-chart to '81ba086fa5daaa2722f35a95b795efbd6604f19e'"
    "{latest}/modules/services/addons/vendor/admin-panel/chart/templates/service.yaml":
      file_hash: ec8d8084a97595495f4117b475008e7f291b9a9b
      from_commit: 81254bd131910c3e6d404407a6120df3c3f37cc7
      message: Add admin panel staging
    "{latest}/modules/services/addons/vendor/admin-panel/chart/values.yaml":
      file_hash: e7f2b97af59e68b950b4679b795172f85f9facfe
      from_commit: c7c916a71dbb7e685ed9903e6e146643c86ce0ff
      message: "chore: bump admin-panel-chart to '2f29c0438445c32f2177305b6960df56a9ab8656'"
    "{latest}/modules/services/addons/vendor/admin-panel/ci/tasks/admin-panel-smoketest.sh":
      file_hash: 274d2bf19b66280ca1c380cbcfd4fe9ea8758f60
      from_commit: 7f3f107c2bc9fcafe16c32b2bee9e2f03e6aa185
      message: "chore: bump admin-panel-chart to '6e24240a512fd30a94bf146001142a32c66f278d'"
    "{latest}/modules/services/addons/vendor/admin-panel/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 383021fb0ca6b80ee0298506ca03623d78b3ae24
      message: "Bump admin-panel-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/addons/vendor/admin-panel/git-ref/ref":
      file_hash: 9b58c7d0ba660964b4acf343cdf9b95dea06fafc
      from_commit: c7c916a71dbb7e685ed9903e6e146643c86ce0ff
      message: "chore: bump admin-panel-chart to '2f29c0438445c32f2177305b6960df56a9ab8656'"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/Chart.yaml":
      file_hash: d0b929eb033580b3eb31b24410d3140326d8eb8e
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/templates/_helpers.tpl":
      file_hash: 1de7a85d9fe1584875e2368a6b367016320ec44c
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/templates/deployment.yaml":
      file_hash: 84e608acc0a433f2325a2213725d9c5fe033fbf8
      from_commit: b1630d6844dee9ad7031326cb5de33198810bf4f
      message: "chore: bump api-dashboard-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/templates/ingress.yaml":
      file_hash: 3a029d200fe00ac38462aea5ef959fd4a1ddc9ca
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/templates/secrets.yaml":
      file_hash: 9a469465861947ab6e49ebd9fceda8f01748ec42
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/templates/service.yaml":
      file_hash: b6defc3438eda3f034c02f760e2c739f796d72ec
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/chart/values.yaml":
      file_hash: 2bffeb0656952566144e5d3252b8a6f2132319e7
      from_commit: 24b262cfbe614b6dd15eb0c3acaf90dfd2c6b3d3
      message: "chore: bump api-dashboard-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/api-dashboard/ci/tasks/api-dashboard-smoketest.sh":
      file_hash: c960899050128136a3c8ac4cbc0e99a55a991d0a
      from_commit: 980d5febf9e8c6a5983dac20d6183b38bf902940
      message: "chore: bump api-dashboard-chart to 'f2ef3da1484109fa7fa1c243cdbd15ec20247cff'"
    "{latest}/modules/services/addons/vendor/api-dashboard/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: bf0aba401ebd0d448cfdc059ef09b4361c4a1303
      message: "chore: add api-dashboard to vendir"
    "{latest}/modules/services/addons/vendor/api-dashboard/git-ref/ref":
      file_hash: 50cd83a68b675ad81422f8535c3963b9da5b4a14
      from_commit: 24b262cfbe614b6dd15eb0c3acaf90dfd2c6b3d3
      message: "chore: bump api-dashboard-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/Chart.lock":
      file_hash: 2181bbd95161bf7b19d4395c6b4636134e486d44
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/Chart.yaml":
      file_hash: 873a7ecac3c314753ade83627fbdc7544387d272
      from_commit: 211286108cc074a3f9624592eac164b581158133
      message: "chore: bump galoy-pay-chart to '02f24c554858b8316c864940be3cef168b99fdfd'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/Chart.lock":
      file_hash: 6d8bdb58cb4378961cc56f4f21d92a3cd5881605
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/Chart.yaml":
      file_hash: ba7215b7936b072968b3f60ba54c3d329ff2051b
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/templates/_helpers.tpl":
      file_hash: 7a62849096621d08206a2ca117b10a378908fd90
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/templates/deployment.yaml":
      file_hash: 328911252acdbd670d8216e6c696681069f91a99
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/templates/secret.yaml":
      file_hash: 8dd6ad8a778721bf9f02bfb71102755ac9e81dc0
      from_commit: c24fe4b118bbcb00ecc6871169d7ef3dabcadf6d
      message: "chore: bump galoy-pay-chart to 'ae9a2f4559b1abba37aea76df922dcf2d5ee2463'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/charts/galoy-nostr/values.yaml":
      file_hash: b083cc9385b418b2cb4c5cfc1a80e8686d224822
      from_commit: 806d3d017061a0a58c54282c286db7c7678a1d17
      message: "chore: bump galoy-pay-chart to '3d0939647e675e4f5f74f7d7704755ecdf3ce824'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/templates/_helpers.tpl":
      file_hash: 2f0562ffa91d4d671ef76745cd0edc463e24b222
      from_commit: e430917d1a486a53eb79076791a87fe9a0327d1a
      message: Add galoy pay vendored dir
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/templates/deployment.yaml":
      file_hash: ebf961ca2d5c1122b163e45a9e2642ab82d0f743
      from_commit: d53839ee078f5dbe1674ca56b92208c2efd60320
      message: "chore: bump galoy-pay-chart to '27fdd07399e7ce4605d80bfc27d8961191543672'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/templates/ingress.yaml":
      file_hash: 665ac7831d6fb715f8c6c09b3a384b9130c3a29a
      from_commit: 009df13bf4aef645fb668149d99e94e0f07af28f
      message: "chore: bump galoy-pay-chart to '1cc3cb797bd20a0af01c42028c42b638062bddfc'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/templates/secrets.yaml":
      file_hash: 747ae9a845472fd8f95137d84bee198ca2ed4e5f
      from_commit: 03dd923d076cf94ac4fbea535f64b421b6c42ba8
      message: "chore: bump galoy-pay-chart to '413dc5c4bfefdcadfeca541728160b109d256b4e'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/templates/service.yaml":
      file_hash: 24a61257bbf48d0ff78567e0e5f433739f645c20
      from_commit: 7dfef95fba3f94e78600ced82289df099c891864
      message: "Bump galoy-pay-chart to '5eb774460038358b7c0010e07c35ea2b1a30510d'"
    "{latest}/modules/services/addons/vendor/galoy-pay/chart/values.yaml":
      file_hash: 8a53ffb25a149c18b7c89c4f9c84bb266639db80
      from_commit: abde50d57c6448b58d73cac9a4dc076ab396d9ba
      message: "chore: bump galoy-pay-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/galoy-pay/ci/tasks/galoy-pay-smoketest.sh":
      file_hash: 5e29c74d37ea2371b240cd24634c7c03c1b0d528
      from_commit: 319c67ffa7443878327f3aada76dcd5186479604
      message: "chore: bump galoy-pay-chart to '23a0678bfa0ecd490447bf647f2b8d3664336df0'"
    "{latest}/modules/services/addons/vendor/galoy-pay/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 78b68da8e689ea4395205c0f1f67cffa718f906c
      message: "Bump galoy-pay-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/addons/vendor/galoy-pay/git-ref/ref":
      file_hash: 50cd83a68b675ad81422f8535c3963b9da5b4a14
      from_commit: abde50d57c6448b58d73cac9a4dc076ab396d9ba
      message: "chore: bump galoy-pay-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/map/chart/Chart.yaml":
      file_hash: c8a2a528c42ba0928ccec7ff2d1d37f1814a85f4
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/templates/_helpers.tpl":
      file_hash: 54c51d0b0cb342d04b560488a5409e5638dc2208
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/templates/deployment.yaml":
      file_hash: 9001476a346a333e13cfea0730f02f0da5d87c23
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/templates/ingress.yaml":
      file_hash: 58ed06a0061669d5923b7399b538e1b3ad52a74c
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/templates/secrets.yaml":
      file_hash: c55f29ffa7f702ccdbe072d0ba64b8690d8335cc
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/templates/service.yaml":
      file_hash: c7840297837017763a2bd92db3a205740b32f048
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/chart/values.yaml":
      file_hash: 4260170be0a8308d660faf9788dfdb30a566e004
      from_commit: 5982add3abcbb33f90caf0967afe1826f088e531
      message: "chore: bump map-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/map/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/ci/tasks/map-smoketest.sh":
      file_hash: 96cb8cf358eaf1cc765d31b47518fbc638c66ca9
      from_commit: 5704a2fc04aad91c5c338fee20ee128255715eac
      message: "feat: add map (#6253)"
    "{latest}/modules/services/addons/vendor/map/git-ref/ref":
      file_hash: 50cd83a68b675ad81422f8535c3963b9da5b4a14
      from_commit: 5982add3abcbb33f90caf0967afe1826f088e531
      message: "chore: bump map-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/voucher/chart/Chart.lock":
      file_hash: 0efb1633efccb10fae93f579eff27692deb85a38
      from_commit: 31f44901e087d63cd6fc9eb601909b13a5e677f4
      message: "chore: bump voucher-chart to '0ef4aa7f277f0b1e74855b338b2436ab312163d6'"
    "{latest}/modules/services/addons/vendor/voucher/chart/Chart.yaml":
      file_hash: f371b21c1d8aae4ed4b225390c30e393d8de6973
      from_commit: 31f44901e087d63cd6fc9eb601909b13a5e677f4
      message: "chore: bump voucher-chart to '0ef4aa7f277f0b1e74855b338b2436ab312163d6'"
    "{latest}/modules/services/addons/vendor/voucher/chart/templates/_helpers.tpl":
      file_hash: 56cce4edaf6f92e02f3acd5facd3b474991ccde7
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/chart/templates/deployment.yaml":
      file_hash: a9f0e7f8f3f2588786963be09450c6d6a289ccce
      from_commit: 144fb40d279812b1564f90875d31eec9cfb73e78
      message: "chore: bump voucher-chart to '8a8cb86b4119002a058f3a7a21ccac81167c4647'"
    "{latest}/modules/services/addons/vendor/voucher/chart/templates/ingress.yaml":
      file_hash: 0481a25bc9dea051786ca76768e10dd0065a4db7
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/chart/templates/secrets.yaml":
      file_hash: 9c9cb8cf162f7930848d212752caa2d9b4ea6ca5
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/chart/templates/service.yaml":
      file_hash: 7413815bdde0b24cd4d1d99641f28e467a1a6d75
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/chart/values.yaml":
      file_hash: 423fc49c6dcc05fd3cb955afe8f97e8f36341cfa
      from_commit: f418936e0c2e06e85f71091cc9cdfc50c3838868
      message: "chore: bump voucher-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/voucher/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/ci/tasks/voucher-smoketest.sh":
      file_hash: 64260a1d9d0683fc10e7be0a249138ec66d96483
      from_commit: 7475f5d0a6392566e086e5b5d0c5cbe7f2e0035b
      message: "feat: adding blink voucher (#6898)"
    "{latest}/modules/services/addons/vendor/voucher/git-ref/ref":
      file_hash: 50cd83a68b675ad81422f8535c3963b9da5b4a14
      from_commit: f418936e0c2e06e85f71091cc9cdfc50c3838868
      message: "chore: bump voucher-chart to '06221a57a68708c983d2c6cdafd1e402a8474837'"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/Chart.yaml":
      file_hash: 70f8f0fc72f10f7ea765cf0d42cfc0991301b122
      from_commit: 54ee9e550797c65fd1f453694ff568b34e0ac0de
      message: "chore: bump web-wallet-chart to '61c647552e398af816b140718f3c8863ec1e11c0'"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/templates/_helpers.tpl":
      file_hash: 54b7af45e68d24b51e1b974c425862afe77cd224
      from_commit: a9848777977ac60b485becc07bbe07d617a1b825
      message: "chore: add missing web wallet chart"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/templates/deployment.yaml":
      file_hash: c7e52554c66838da1b569ec2d997b226fecac010
      from_commit: d2ce9afc8a3836c1fc92637d928b9dc27c20e0b2
      message: "chore: bump web-wallet-chart to '04246aed89f10d2296be323a14803ac380e71f20'"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/templates/ingress.yaml":
      file_hash: 2ee837cabd999470a1695c2bf973fb8bb0fed22a
      from_commit: 25c33938578a9accea0d323c2b2f7a4f9e016482
      message: "chore: bump web-wallet-chart to '73f3b8d6008bff83843942de250b3c0586e0b445'"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/templates/secrets.yaml":
      file_hash: e12a74ce32048c21262d15814578518ca7ceb90b
      from_commit: a9848777977ac60b485becc07bbe07d617a1b825
      message: "chore: add missing web wallet chart"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/templates/service.yaml":
      file_hash: f1188d946a4643bd58e78e11e248b40bbd1674dc
      from_commit: 71435a901ee15287b24000910507d98a8af02751
      message: "Bump web-wallet-chart to '6617eda1cb268a7e942351a500721f5cb5d22309'"
    "{latest}/modules/services/addons/vendor/web-wallet/chart/values.yaml":
      file_hash: 495507c70c91e139273b0415efc9c59c6bf73a9a
      from_commit: 8d4c1729c1271fa885baa7607c810735827675a0
      message: "chore: bump web-wallet-chart to '71434bbc94312188a5221bee4e5ddcfc35fd216d'"
    "{latest}/modules/services/addons/vendor/web-wallet/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: a9848777977ac60b485becc07bbe07d617a1b825
      message: "chore: add missing web wallet chart"
    "{latest}/modules/services/addons/vendor/web-wallet/ci/tasks/web-wallet-smoketest.sh":
      file_hash: 268f12a7dde480b1719158cd4a9ae84e8af64129
      from_commit: b60c9182cbbd50b6dbec786309d598eab56d9a70
      message: "chore: bump web-wallet-chart to 'fcb4d77e4bbff80f5d8d553d75e58bdf9b96c064'"
    "{latest}/modules/services/addons/vendor/web-wallet/git-ref/ref":
      file_hash: 72991caf5586005a2b6bcdb935953f23d72c90bb
      from_commit: 54ee9e550797c65fd1f453694ff568b34e0ac0de
      message: "chore: bump web-wallet-chart to '61c647552e398af816b140718f3c8863ec1e11c0'"
    "{latest}/modules/services/addons/voucher-values.yml.tmpl":
      file_hash: 8f07afc798e24ab6fb7e98c7ba4a1c7444f1044d
      from_commit: bd240e5033f794a59f9ca7a6a16c43f587a0284f
      message: "chore: adding voucher platform fees in ppm (#7296)"
    "{latest}/modules/services/addons/voucher.tf":
      file_hash: bcbfea5df7ad9ff9f78a595e2ae01f32647c1c52
      from_commit: 3bc409d9ab95cdb34749dd068b1537cce3638282
      message: "chore: reactivate voucher in prod (#7857)"

