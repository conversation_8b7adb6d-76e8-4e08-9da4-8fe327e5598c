{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT created_at, id FROM bria_payout_queues WHERE (COALESCE((created_at, id) < ($3, $2), $2 IS NULL)) ORDER BY created_at DESC, id DESC LIMIT $1) SELECT i.id AS \"entity_id: PayoutQueueId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_payout_queue_events e ON i.id = e.id ORDER BY i.created_at desc, i.id desc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: PayoutQueueId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, false, false]}, "hash": "6f7f25169f95d9a7cdf0e3e24adadc2b6970ff87da1c03aa878efac164a36cf9"}