<!-- omit in toc -->
# Repo that holds the state of all blink deployments

- [Find On Call information](#find-on-call-information)
- [Gain Environment Access](#gain-environment-access)
  - [GCP Access](#gcp-access)
  - [Access to bastion host (staging)](#access-to-bastion-host-staging)
  - [Authenticating inside bastion host (staging)](#authenticating-inside-bastion-host-staging)
  - [Executing commands remotely in pods from bastion (staging)](#executing-commands-remotely-in-pods-from-bastion-staging)
  - [Access to secrets (staging)](#access-to-secrets-staging)
    - [Login details for accounts setup in staging for signet](#login-details-for-accounts-setup-in-staging-for-signet)
    - [Specter-password](#specter-password)
  - [Get access to database (staging)](#get-access-to-database-staging)
  - [How to run a CronJob manually (staging)](#how-to-run-a-cronjob-manually-staging)
  - [Access Redis](#access-redis)
  - [Local access to Prometheus](#local-access-to-prometheus)
  - [Read-only access to LND](#read-only-access-to-lnd)
  - [Bastion access to LND](#bastion-access-to-lnd)
  - [Local access to LND via bastion](#local-access-to-lnd-via-bastion)
  - [Balance of Satoshis in the bastion](#balance-of-satoshis-in-the-bastion)
  - [Looping out on BBW](#looping-out-on-bbw)
  - [Fan out when there are only a few UTXOs available](#fan-out-when-there-are-only-a-few-utxos-available)
- [Mobile development](#mobile-development)
  - [Browserstack](#browserstack)
  - [mobile e2e test](#mobile-e2e-test)
- [Environment Deployment Procedure](#environment-deployment-procedure)
- [Debugging on Concourse](#debugging-on-concourse)
- [Testing on Staging](#testing-on-staging)
  - [Global OTP](#global-otp)
  - [Retrieve API Endpoints](#retrieve-api-endpoints)
- [Kratos](#kratos)
  - [Get basic informations](#get-basic-informations)
  - [Scripts](#scripts)
    - [To query for all expired user sessions](#to-query-for-all-expired-user-sessions)
    - [To extend session expiration dates](#to-extend-session-expiration-dates)
- [Stalled job queues](#stalled-job-queues)
- [Manual fix for the `getHeldInvoicesCount_value != 0` error of the exporter](#manual-fix-for-the-getheldinvoicescount_value--0-error-of-the-exporter)
- [Swap or change user's phone number or email](#swap-or-change-users-phone-number-or-email)
- [Add a new Oauth2 client](#add-a-new-oauth2-client)
  - [List clients](#list-clients)
  - [Create client](#create-client)
  - [Update a client](#update-a-client)
  - [Delete a client (careful here)](#delete-a-client-careful-here)
  - [Oauth2 Client Terraform Configuration Examples](#oauth2-client-terraform-configuration-examples)
    - [Blink Dashboard](#blink-dashboard)
    - [Blink PoS](#blink-pos)
    - [With a Refresh Token](#with-a-refresh-token)

# Find On Call information
See the [oncall.md](./oncall.md).

---
# Gain Environment Access
## GCP Access
Follow this [sample pr](https://github.com/GaloyMoney/blink-deployments/pull/1028/files) to give yourself access to `galoy-staging` GCP project.

## Access to bastion host (staging)
Sometimes we need to access the bastion host in staging in order to interrogate the staging environment.  To do this follow the below steps:
1. Make sure you have logged in to your username</span>@</span>galoy.io Gmail account and activated 2FA in the Google account settings. First time will either need to be logged in on a phone to the Google account or can use a security key (like Yubikey / Trezor / Ledger with the [trezor-agent](https://github.com/romanz/trezor-agent)). Once one 2FA method is added can add a Google Authenticator compatible app as well.
1. Follow the instructions to install the gcloud SDK for your system [here](https://cloud.google.com/sdk/docs/install).
1. Once installed run:
    ```
    gcloud init
    ```
     this will log you in and then ask you to select a project.
2. Select `galoy-staging` project.
3. Run:
    ```
    gcloud compute os-login ssh-keys add --key-file=$HOME/.ssh/id_rsa.pub
    ```
    - If you haven't generated a key yet then follow the instructions [here](https://docs.github.com/en/github-ae@latest/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent).
    - to not have to specify the default key every time copy it to the [default location expected by gcloud](https://cloud.google.com/sdk/gcloud/reference/compute/ssh):
        ```
        cp $HOME/.ssh/id_rsa.pub ~/.ssh/google_compute_engine.pub
        cp $HOME/.ssh/id_rsa ~/.ssh/google_compute_engine
        ```
4. To log in to `staging` run:
    ```
    gcloud compute ssh galoy-staging-bastion --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging
    ```
    Use your preferred 2FA option. You are now on the bastion host.

## Authenticating inside bastion host (staging)
Before you can perform operations against our pods you will first need to be authenticated. These steps only need to be done once after each bastion re-deploy. To do this:
1. Ensure you are inside the bastion host using the steps above.
1. Run:
    ```
    gcloud auth login
    ```
    Copy the `gcloud auth login --remote-bootstrap="https://..."` command to a new terminal and copy-paste the output back. You are now logged in to the `[galoy-staging]` project.
2. Run `kauth` (`alias kauth='gcloud container clusters get-credentials galoy-staging-cluster --zone us-east1 --project galoy-staging'`):
    ```
    kauth
    ```

## Executing commands remotely in pods from bastion (staging)
This is useful for executing things within a container in a given pod. To do this:
1. To see all available namespaces run (`k` is an alias set for `kubectl`):
    ```
    k get namespaces
    ```

1. Select an appropriate namespace and then run:
    ```
    k -n <namespace> get pods
    ```
     to see all available pods.

   e.g.:
    ```
    k -n galoy-staging-bitcoin get pods
    ```

1. Select an appropriate pod and then run:
    ```
    k -n <namespace> exec <pod> -- <command>
    ```
    to execute commands inside the pod.

   e.g.:
   ```
   k -n galoy-staging-bitcoin exec bitcoind-0 -- bitcoin-cli -getinfo
   ```

1. Bonus: open a shell directly into the container in a pod with:
    ```
    k -n <namespace> exec -it <pod> -- sh
    ```

   e.g.:
   ```
   k -n galoy-staging-bitcoin exec -it bitcoind-0 -- sh
   ```

## Access to secrets (staging)
Sometimes we need access to secrets stored in kubernetes running in staging. To do the follow the following steps.
1. [Follow the steps](#access-to-bastion-host) above to get access to bastion host and [authenticate](#authenticating-inside-bastion-host-staging).

### Login details for accounts setup in staging for signet
1. To get a list of the secret names in the environment run:
    ```
    k -n galoy-staging-galoy get secrets
    ```
    In this case we want test account login details.
2. Run:
    ```
    k -n galoy-staging-galoy get secrets test-accounts -o json | jq -r '.data.json' | base64 --decode
    ```
    The response is in json format and is also encoded so we need to use jq and base64 decode it.

### Specter-password
* Run:
  ```
  env="galoy-staging"
  k -n ${env}-bitcoin get secrets specter-password -o jsonpath='{.data.password}' | base64 -d
  ```

## Get access to database (staging)
The example below is for staging (run `k get ns` to get proper namespaces for other environments).
1. [Follow the steps](#access-to-bastion-host-staging) above to get access to bastion host and [authenticate](#authenticating-inside-bastion-host-staging).
1. Next instead of the simple ssh connection use the next command to do port forward to your local machine:
    ```
    gcloud compute ssh --ssh-flag="-L 27018:localhost:27018" galoy-staging-bastion --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging
    ```
1. To get database password run:
   ```
   k get secret -n galoy-staging-galoy galoy-mongodb -o jsonpath="{.data.mongodb-password}" | base64 -d
   ```
1. To forward mongodb traffic through 27018 port in bastion (already redirected to your local machine - step 1) run:
    ```
    k -n galoy-staging-galoy port-forward galoy-mongodb-0 27018:27017
    ```
1. In your local machine, connect to staging mongodb instance using your prefered MongoDB GUI/shell e.g. [MongoDB Compass](https://www.mongodb.com/try/download/compass) :
    ```
    Connection details:
      host: 127.0.0.1
      port: 27018 (bastion is shared by all developers, please use your own port number to avoid conflicts)
      database: galoy (default value)
      username: testGaloy (default value)
      password: <value from step 2>

   Connection String URI:
      **********************************************************************************************************************
    ```

## How to run a CronJob manually (staging)
1. [Follow the steps](#access-to-bastion-host) above to get access to bastion host.
1. Run a CronJob manually:
    ```
    k -n galoy-staging-galoy create job --from=cronjob/<jobname> <jobname>-manual
    ```
1. Get the CronJob pod name:
    ```
    k -n galoy-staging-galoy get pods
    ```
1. Check the logs:
    ```
    k -n galoy-staging-galoy logs <CronJob pod name>
    ```

1. Example to run our cron server job:
    ```
    k -n galoy-staging-galoy create job --from=cronjob/galoy-cronjob cronjob-manual
    ```

1. when you're done, delete the job with:
    ```
    k -n galoy-staging-galoy delete job cronjob-manual
    ```


## Access Redis

To get the existing `keys` or delete all the `login_*` keys (or other patterns like `phone_code`):

1. Exec into a redis node:
    ```
    k -n galoy-staging-galoy exec galoy-redis-node-2 -c redis -it -- bash
    ```

2. List all keys using redis-cli:
    ```
    REDISCLI_AUTH=${REDIS_PASSWORD} redis-cli KEYS "*"
    ```

3. To delete all the keys starting with `login_*`:
    ```
    REDISCLI_AUTH=${REDIS_PASSWORD} redis-cli KEYS "login_*" | REDISCLI_AUTH=${REDIS_PASSWORD} xargs -i redis-cli del {}
    ```

## Local access to Prometheus
1. [Follow the steps](#access-to-bastion-host-staging) above to get access to bastion host and [authenticate](#authenticating-inside-bastion-host-staging).
1. Next instead of the simple ssh connection use the next command to forward the port 9090 to your local machine port 9091:
    ```
    gcloud compute ssh --ssh-flag="-L 9091:127.0.0.1:9090" galoy-staging-bastion --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging
    ```
1. When authenticated forward the port from k8s to the host:
    ```
    k -n galoy-staging-monitoring port-forward monitoring-prometheus-server-0 9090:9090
    ```

1. Open http://localhost:9091/ in a browser of your local machine to view Prometheus.

## Read-only access to LND
1. [Follow the steps](#access-to-bastion-host-staging) above to get access to bastion host and [authenticate](#authenticating-inside-bastion-host-staging).
2. Obtain the `read-only.macaroon` and `tls.cert` from someone with admin access to the instance.
3. To access the LND GRPC port locally on your desktop:
    ```
    gcloud compute ssh galoy-staging-bastion --ssh-key-file=$HOME/.ssh/id_rsa  --project=galoy-staging -- -NL 10009:********:10009
    ```
    The  `lnd_internal_ip` address in the bastion to forward from is:
    * `********` for lnd1
    * `********` for lnd2

## Bastion access to LND
Will use the staging environment for this example.

1. you need to get a macaroon and the tls.cert onto the bastion. See above [how to open access ssh access to a pod](#executing-commands-remotely-in-pods-from-bastion-staging)
    - create the default path under your user (can use any path, but then will need to specify it every time when calling lncli)
    ```
    mkdir -p ~/.lnd/data/chain/bitcoin/signet/
    ```
    - copy the tls.cert from the pod to the bastion
    ```
    k -n galoy-staging-bitcoin exec lnd1-0 -c lnd -- cat /root/.lnd/tls.cert > ~/.lnd/tls.cert
    ```
    - copy the admin.macaroon from the pod to the bastion
    ```
    k exec -n galoy-staging-bitcoin lnd1-0 -c lnd -- cat /root/.lnd/data/chain/bitcoin/signet/admin.macaroon > ~/.lnd/data/chain/bitcoin/signet/admin.macaroon
    ```
1. Run lncli using the default paths created above. The LND RPC is exposed internally on the IP address ********.
    ```
    lncli -n signet --rpcserver ********:10009 getinfo
    ```

## Local access to LND via bastion
Note: this should ***only be done for staging***. Credentials will be exposed on your local system.

1. Copy the macaroon and tls cert from the prior steps to a folder on your local machine with `scp`
    ```bash
    $ mkdir -p temp-lnd-staging-creds
    $ cd temp-lnd-staging-creds
    $ gcloud compute scp galoy-staging-bastion:.lnd/tls.cert . --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging
    $ gcloud compute scp galoy-staging-bastion:.lnd/data/chain/bitcoin/signet/admin.macaroon . --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging
    ```
1. Port forward the respective lnd port from the bastion
    ```bash
    $ gcloud compute ssh galoy-staging-bastion --ssh-key-file=$HOME/.ssh/id_rsa --project=galoy-staging -- -NL 10009:********:10009
    ```
1. Optional, check that port is open locally (can check it was closed before the previous step as well)
    ```bash
    $ nmap -p 10009 localhost
    ```
1. Alias lncli locally
    ```bash
    alias ln_cli="lncli --macaroonpath admin.macaroon --tlscertpath tls.cert"
    ```
1. Execute aliased lncli command to test connection
    ```bash
    ln_cli getinfo
    ```

## [Balance of Satoshis](https://github.com/alexbosworth/balanceofsatoshis#balance-of-satoshis) in the bastion
1. [Follow the steps](#access-to-bastion-host-staging) above to get access to bastion host and [authenticate](#authenticating-inside-bastion-host-staging).
2. You can use the `lnd_internal_ip` addresses to connect:
* lnd1: `********:10009`
* lnd2: `********:10009`

or need to forward the port from the pod to the bastion to connect to both LND-s:

*  port forward lnd1 to 10011
     ```
     k -n galoy-staging-bitcoin port-forward lnd1-0 10011:10009 &
     ```
* port forward lnd2 to 10012
     ```
     k -n galoy-staging-bitcoin port-forward lnd2-0 10012:10009 &
     ```
     `&` runs the command in the background. Bring forward with: `fg` to close the process with `CTRL`+`C` or run the commands in `tmux` to keep the ports open.

3. Authenticate bos for both lnd1 and lnd2 on the forwarded ports:
    ```
    namespace="galoy-staging-bitcoin"
    network="signet"

    for i in 1 2; do

        mkdir -p ~/.bos/lnd${i}

        MACAROON=$(kubectl exec -n ${namespace} lnd${i}-0 -c lnd -- base64 /root/.lnd/data/chain/bitcoin/${network}/admin.macaroon | tr -d '\n\r')

        CERT=$(kubectl exec -n ${namespace} lnd${i}-0 -c lnd -- base64 /root/.lnd/tls.cert | tr -d '\n')

        echo "{ \"cert\": \"$CERT\", \"macaroon\": \"$MACAROON\", \"socket\": \"10.1.1.${i}:10009\"}" > ~/.bos/lnd${i}/credentials.json

    done
    ```

4. Example usage:
    ```
    bos peers --node lnd1
    bos peers --node lnd2
    ```
 - all commands:
    ```
    bos --help
    ```
-  more info on the specific command:
    ```
    bos peers --help
    ```

## Looping out on BBW

* If the onchain balance is depleted, and until [loop automation](https://github.com/GaloyMoney/galoy/pull/1396) is deployed, the following script can be used to do the loop out manually:

    ```
    while true; do if (( $(kubectl -n galoy-bbw-bitcoin exec lnd1-0 -c lnd -- lncli walletbalance | jq -r .total_balance) < 250000000 )); then kubectl -n galoy-bbw-bitcoin exec lnd2-loop-0 -- loop out $[$RANDOM * 625 + 10000000] --addr $(k -n galoy-bbw-bitcoin exec lnd1-0 -c lnd -- lncli newaddress p2wkh | jq -r .address) —channel $(kubectl -n galoy-bbw-bitcoin exec lnd2-0 -c lnd -- lncli listchannels | jq -r '.channels[] | select(.remote_pubkey |= "0325bb9bda523a85dc834b190289b7e25e8d92615ab2f2abffbe97983f0bb12ffb") | {chan_id} | join(" ")' | sed -e ":a" -e "N" -e "$!ba" -e "s/\n/,/g") -f; fi; echo "sleep"; sleep 3600; done
    ```

    Note: this script assumes:
    - lnd2 is the primary lightning node, and lnd1 is the primary onchain node.
    - the script currently loop out 20m/sats per 20 min period, for 9 periods - so it will loop out 1.8 btc over 3 hours. the value can be changed accordingly to have faster/slower loop out.
    - it requires the session on bbw to stay open. `tmux` doesn't work because when the session is closed the authentication to gcloud is automatically revoked

* deezy.io script:
    ```
    namespace=galoy-bbw-bitcoin
    lndin=lnd1-0
    lndout=lnd2-0
    while true; do
      totalbalance=$(kubectl -n $namespace exec $lndin -c lnd --\
      lncli walletbalance | jq -r .total_balance)
      if (( totalbalance < 250000000 )); then
        liquidityfeeppm=$(curl -sS https://api.deezy.io/v1/swap/info | jq .liquidity_fee_ppm)
        if ((liquidityfeeppm < 1500)); then
          newaddress=$(kubectl -n $namespace exec $lndin -c lnd --\
          lncli newaddress p2wkh | jq .address)
          invoice=$(curl -sS -X POST --header "Content-Type: application/json" \
          https://api.deezy.io/v1/swap -d \
          "{\"amount_sats\": $(( RANDOM * 625 + 10000000 )),\
            \"on_chain_address\":$newaddress,\
            \"on_chain_sats_per_vbyte\": 1}" | jq -r .bolt11_invoice)
          echo $invoice
          kubectl -n $namespace exec $lndout -c lnd -- lncli payinvoice $invoice -f
        fi
      fi
      echo "sleep"
      sleep $(( 1800 + RANDOM % 3600 ))
    done
    ```

## Fan out when there are only a few UTXOs available
* requires [bastion access to LND](#bastion-access-to-lnd)
* edit to make as many parts and amounts needed (the leftover will be returned as change to the same lnd wallet):
    ```
    lncli --rpcserver ********:10009 sendmany "{
      \"$(lncli --rpcserver ********:10009 newaddress p2wkh|jq .address -r)\": 50000000,
      \"$(lncli --rpcserver ********:10009 newaddress p2wkh|jq .address -r)\": 50000000,
      \"$(lncli --rpcserver ********:10009 newaddress p2wkh|jq .address -r)\": ********,
      \"$(lncli --rpcserver ********:10009 newaddress p2wkh|jq .address -r)\": ********,
      \"$(lncli --rpcserver ********:10009 newaddress p2wkh|jq .address -r)\": ********
    }"  --sat_per_vbyte 10 --label fanout
    ```

---
# Mobile development
## Browserstack
  We qualified for a sponsored plan with browserstack as we are an open source project.  This gives us access to test our mobile apps on a range of devices and OS versions and also to test our web apps on multiple browsers and OS versions.  To get access to browsertstack you can use the following [link to sign up](https://accounts.browserstack.com/jointeam/********************************).  If you need access to specific products which aren't available to you by default then send a message to one of the developer channels and an admin should be able to upgrade your product access.

## mobile e2e test

The tests run on staging. We rotate the tokens for the test accounts so that we can run the tests on several devices simultaneously. One token serves as the sender wallet token (GALOY_TOKEN_2) and the other, the recipient wallet token (GALOY_TEST_TOKENS). The recipient wallet token is an array of tokens (39) that maps to the individual test accounts.

Below are the tokens used:
```
export GALOY_TEST_TOKENS=5zSUJVuSUIuzS6fcm8AkvagOQ3nTwiVT,oxeqL6bxyPSXMQVxx3emY0Vb8FXy5Mzq,9kdOsdS95d00lud3rlvug99dYP9yT34P,H8UBVkGs1ZL8QoYljzQpo9e4p01EPqYQ,MODWpZPSpeoHvD602SPRwq4tlTvz0iho,SAL57cFTKiBHtUM4ry7lCrpCWMtC5O1w,YLnRJwIk0rJ10tkisimDo9ljsowAhr25,IKNpHpFOrXLD6WZchXJwVn4glrT2Dh99,HfBnwdO3r5h9WHn0E3svUlyJRCAU54WR,p3TfgAesSQ1OI32IP7Qyytn6GGinGXcY,bmhls9s0FW762li42v0hWAdzcwrM9mKY,FHz2nkoMHdXrItjyNrWxWTEZqOZP0uW8,NrLz0aM1Mp3HhLf2qKi9Up6TJdbizoyb,kF90GdmDFYHy05BHkNbgexeNKeAmnssV,B9pP2ScXqJwU5qAYYr82DBSVKl4UlrLh,WzAgqV9n4iAyiPdrjpTECJ8dhU0v3Pl9,wtHbGfDHsAMGKYjxKKpN1EX9xWivqRey,8y2pwIuyTYzyM2Z0jkTfR2S2qYWa7I8v,9ZDxtofWIcI1jO99YEK0MhQxkXMakcjf,6FwFX3yYOHEDavBgZ0FxmkufA3wNbxWP,tv65t4L1z3Qd8jzWFV4I6eZILEFAU1Xd,5u5nu5QuVFOYn4dmfcmMXRz3CKGN7yCN,rRDGZT8YejR325bx3q9nvJmvJXO12b6Y,OVfnFbDxxeWoNr2E5MUat4oYfBBA837q,997ElCEtCn53kIzlKRq2ZFyHwrPgLV08,RfmBtS0emS91FWusBJOvrqirnfJxzvUx,4fVbZ17lGmAKailFYju6tLbw8F6Col7e,da8kW4yx6RBlvBbFgk5gr1PFyPXpcRAa,GnvBnxj2ilSAZN2FC9aU9ZmTQPeHyUOh,MU7CFNw1d6lqblJszAocmlv7xnvlOrz1,ImhEVP2zzepmMtg4GfiFXfP0BOFCGMhL,AE5ruXs0q1UVUOj2GxeSH369DDrHu1D7,4azSr9O95qlfdgqBIog4fT47El7lgLsE,YAjLc6SlKTtrCuWDt2jHIS84Gt7121iO,z7RnvwWOvQcmHe1H0VHsHtGaqEqYubR5,mVBOweUXxocdJ19GfTxC15gwPnObr3UF,PLusE96GbTpcP6CvkW4oleUHjmyxJ6wm,SyZyQqeKfUyheJjOYXscx5GetoJRbUpD,PBdw03R1erpmZbOnSUBmsbkNchgr3rBG,kAtmLq4T6x0C45b1DZW2CunUT43L3eHb,LrET4eI4akZobIhTed6gKNtoUAYP5GX5

export GALOY_TOKEN_2=SSjglz2fRrMGrerPOWP0yP2avg5kob4T
```

If staging has been reset, the code above might change. To get the new code for the huawei and google accounts, for example:

```
k -n galoy-staging-galoy get secrets test-accounts -o json | jq -r '.data.json' | base64 --decode | jq -c '.[]' | grep huawei
k -n galoy-staging-galoy get secrets test-accounts -o json | jq -r '.data.json' | base64 --decode | jq -c '.[]' | grep google
```

---
# Environment Deployment Procedure
For the steps to follow when setting up a new environment see the [deployment.md](./deployment.md).

# Debugging on Concourse
Find information on debugging failed builds on concourse in [debugging.md](./debugging.md).

# Testing on Staging

## Global OTP
OTP to access any account on Staging is `403370`.

## Retrieve API Endpoints

To retrieve the staging API endpoints for testing, run the following commands.

- Follow the instructions documented in [access](./README.md#access-to-bastion-host-staging) and [authenticate](./README.md#authenticating-inside-bastion-host-staging) to log in to staging bastion.

- Retrieve a list of the staging API endpoints and copy the endpoint particular to your test

```bash
k get ingress --all-namespaces
```

# Kratos

Kratos is used for Authentication. https://www.ory.sh/docs/kratos/ory-kratos-intro

## Get basic informations

1. list pods:
`k -n galoy-staging-galoy get pod`

2. port-forward one of the kratos node on the admin port:
`k -n galoy-staging-galoy port-forward galoy-kratos-59b7bdbccf-64mgs 4434 &`

3. set admin url
`export KRATOS_ADMIN_URL=http://localhost:4434`

4. execute your kratos command
`kratos list identities --format json`

## Scripts

### To query for all expired user sessions
```sql
SELECT id FROM public.sessions
WHERE expires_at <= (SELECT NOW()) and active = true
```
### To extend session expiration dates
```sql
UPDATE public.sessions s
SET expires_at = (SELECT NOW() + '5 days')
WHERE s.id in (
   SELECT id FROM public.sessions
   WHERE expires_at <= (SELECT NOW()) and active = true
);
```

# Stalled job queues
Here is how to deal with stalled job queues in projects using sqlxmq (Bria, Stablesats)

You have found this because you got an alert regarding a stalled queue?

For bria there is a [honeycomb dashboard](https://ui.honeycomb.io/galoy/board/nMLLRH6E1ZW/galoy-bbw-bria) that should show very clearly which jobs cut out.

Usually when this is observed it is a blip and it all fixes itself within <10 minutes. In rarer cases you may need to restart the pods.

But sometimes (observed 2x times up to now) the queues have really gotten into a theoretically impossible stalled state that they do not recover from.
This should never happen but has been observed - root cause unknown.
If Justin is around - let him know.
Otherwise if its critical (like payouts in bria not being processes)
and Justin is not available here are the steps to rectify:
- login to the environment
- get the postgresql creds:
  `PG_CON=$(k -n galoy-bbw-bitcoin get secret bria -o json | jq -r '.data["pg-con"]' | base64 --decode)`
- Login to postgres:
  `psql $PG_CON`
- Find the stalled queue:
  `select * from mq_msgs where attempts = 0;`
If this query doesn't return any rows then it is not the weird impossible state that needs this intervention but something else is going on (did you try pod restart yet?).

If a row did get returned we are in the truely stalled state that wont self recover:

- Copy the IDs and delete them from 2 tables:
  `delete from mq_payloads where id = "<uuid>";`
  `delete from mq_msgs where id = "<uuid>";`
This should be all that is required.
And up to now things have always sorted themselves out after this.
If in doubt you can also kill the pods and let them come up again too - after the DB operation.

# Manual fix for the `getHeldInvoicesCount_value != 0` error of the exporter

* set the alias while logged in to the bastion
  ```
  env=galoy-staging
  network=signet
  alias lnd2='k -n ${env}-bitcoin exec lnd2-0 --container lnd -- lncli -n ${network}'
  ```
* list the accepted invoices in the past two days
  ```
  timestamp_2_days_ago=$(date -d '20 days ago' +%s)
  lnd2 listinvoices --creation_date_start ${timestamp_2_days_ago} | jq '.invoices[] | select(.state == "ACCEPTED")'
  ```
* alternative command to validate
  ```
  lnd2 listchannels | jq '[.channels[] | {channel_id, capacity, local_balance, remote_balance, commit_fee, pending_htlcs, incoming_balance: [(.pending_htlcs[]? | select(.incoming == true).amount | tonumber)] | add, outgoing_balance: [(.pending_htlcs[]? | select(.incoming == false).amount | tonumber)] | add}] | sort_by(.pending_htlcs | length)'
  ```
* Validate in mongo if `paid` and `processingCompleted` are `true` in `invoiceusers` but there is no record in `medici_transactions`
  * should not return records
    ```
    db.getCollection('medici_transactions').find({hash:"<INVOICE HASH>"})
    ```
  * should return an `entity` with `paid` and `processingCompleted` as `true`
    ```
    db.getCollection('invoiceusers').find({_id:"<INVOICE HASH>"})
    ```
* if you want to be sure please validate with lookupinvoice:
  ```
  lnd2 lookupinvoice <INVOICE HASH>
  ```

* After confirm was processed but not registered in medici edit paid and processingCompleted as false in invoiceusers and run cron manually ([link in this readme](#how-to-run-a-cronjob-manually-staging))

# Swap or change user's phone number or email

* Get the user's account id and new phone number. If the new phone number is already in use, it needs to have zero balance.
* Log into the bastion and create a new pod using
```
kubectl run debug --image=ubuntu --tty -i -- bash
```

* Install curl
```
apt update && apt install -y curl
```
* Create a new client and get the client id and secret
```
HYDRA_ADMIN_API="http://galoy-hydra-admin:4445"
curl -L -s -X POST $HYDRA_ADMIN_API/admin/clients \
-H 'Content-Type: application/json' \
-d '{ "grant_types": ["client_credentials"], "scope": "editor" }'
```
* From outside the bastion, get the token using the client id and secret
```
client_id="client_id"
client_secret="client_secret"
HYDRA_PUBLIC_API="https://oauth.blink.sv"
curl -sv -X POST $HYDRA_PUBLIC_API/oauth2/token \
-H 'Content-Type: application/x-www-form-urlencoded' \
-u "$client_id:$client_secret" \
-d "grant_type=client_credentials&scope=editor"
```
* Use token to execute the user phone number update
```
curl -s \
-X POST \
-H 'Oauth2-Token: '"$token" \
-H 'Content-Type: application/json' \
-d '{"query":"mutation UserUpdatePhone($input: UserUpdatePhoneInput!) {\n  userUpdatePhone(input: $input) {\n    errors {\n      __typename\n      message\n      code\n      path\n    }\n    accountDetails {\n      id\n    }\n  }\n}","variables":{"input":{"phone":"+12345","accountId":"abcd-3562-pqr-b5c6-xyz"}}}' \
'https://admin-api.blink.sv/graphql'
```

* Use token to execute the user email update
```
curl -s \
-X POST \
-H "Oauth2-Token: $token" \
-H "Content-Type: application/json" \
-d '{"query":"mutation UserUpdateEmail($input: UserUpdateEmailInput!) {\n  userUpdateEmail(input: $input) {\n    errors {\n      __typename\n      message\n      code\n      path\n    }\n    accountDetails {\n      id\n    }\n  }\n}","variables":{"input":{"email":"<EMAIL>","accountId":"abcd-3562-pqr-b5c6-xyz"}}}' \
'https://admin-api.blink.sv/graphql'
```

# Add a new Oauth2 client

* The client's procedure and implementation details are described in: https://dev.blink.sv/api/oauth2
* Get the Callback URL (multiple URLs are supported) to be used for the client and the scopes needed (would need to be all three: `read`, `receive` and `write` for the user to be able to grant full access to the app on login with Blink)

* Exec into the hydra pod
```
ENV=galoy-staging
POD_NAME=$(kubectl get pods --namespace ${ENV}-galoy -l app.kubernetes.io/name=hydra -o jsonpath='{.items[0].metadata.name}')

kubectl exec --stdin --tty $POD_NAME --namespace ${ENV}-galoy -- /bin/sh
```

* Use the Hydra CLI: https://www.ory.sh/docs/hydra/cli/hydra-create-client

## List clients
```
hydra list clients --endpoint http://127.0.0.1:4445/
```

## Create client
```
CLIENT_NAME=<the name of the new client>
CALLBACK_URL=<https://callback URL to be used for the client (use a comma separated list for multiple)>

# create a new OAuth2 client with refresh token support
hydra create oauth2-client \
  --endpoint http://127.0.0.1:4445/ \
  --name "$CLIENT_NAME" \
  --redirect-uri "$CALLBACK_URL" \
  --grant-type authorization_code,refresh_token \
  --response-type code,id_token \
  --scope read,receive,write,offline
```

Record the:
 - `CLIENT ID`
 - `CLIENT SECRET`
and share them to the client on a secure channel eg. keybase or encrypted with PGP.

Note: When creating a client with refresh token support:
- Include `refresh_token` in grant types
- Add the `offline` scope
- Inform the client they need to request the `offline` scope during authorization

## Update a client
* get the current config
```
hydra list clients --endpoint http://127.0.0.1:4445/
```
```
CLIENT_ID=<client-id-to-update>
```
```
hydra get client $CLIENT_ID --format yaml --endpoint http://127.0.0.1:4445/
```
* include or the previous details
```
CLIENT_NAME=<the name of the client>
CALLBACK_URL=<https://callback URL to be used for the client (use a comma separated list for multiple)>
```
```
hydra update oauth2-client $CLIENT_ID \
  --endpoint http://127.0.0.1:4445/ \
  --name "$CLIENT_NAME" \
  --redirect-uri "$CALLBACK_URL" \
  --grant-type authorization_code,refresh_token \
  --response-type code,id_token \
  --scope read,receive,write,offline
```

## Delete a client (careful here)
```
CLIENT_ID=<the CLIENT ID of the client to be deleted>
hydra delete client $CLIENT_ID --endpoint http://127.0.0.1:4445/
```

## Oauth2 Client Terraform Configuration Examples
Below are the Terraform configurations for two existing Blink integrations, showcasing how to set up OAuth2 clients for different applications using the [hydra_oauth2_client Terraform resource](https://registry.terraform.io/providers/svrakitin/hydra/latest/docs/resources/oauth2_client):

### Blink Dashboard
* try it at [dashboard.blink.sv](https://dashboard.blink.sv)
```hcl
resource "hydra_oauth2_client" "api_dashboard" {
  client_name                = "Blink Api Dashboard"
  grant_types                = ["authorization_code"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read", "write"]
  redirect_uris              = [local.api_dashboard_hydra_redirect_uri]
  skip_consent               = true
}
```

### Blink PoS
* try it at [pay.blink.sv](https://pay.blink.sv)
```hcl
resource "hydra_oauth2_client" "galoy_pay" {
  client_name                = "Blink POS"
  grant_types                = ["authorization_code"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read"]
  redirect_uris              = [for host in local.galoy_pay_hosts : "https://${host}/api/auth/callback/blink"]
  skip_consent               = true
}
```

### With a Refresh Token
* to be implemented
```hcl
resource "hydra_oauth2_client" "your_application" {
  client_name                = "Your Application Name"
  grant_types                = ["authorization_code", "refresh_token"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read", "receive", "write"]
  redirect_uris              = ["https://yourapp.com/callback"]
  skip_consent               = true
}
```
