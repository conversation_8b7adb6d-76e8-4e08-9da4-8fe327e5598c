{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT account_id, id FROM bria_profiles WHERE ((account_id = $1) AND (COALESCE(id > $3, true))) ORDER BY id ASC LIMIT $2) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.id asc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "91c6e611103d99e1d312ba702c47d63d05384431c34951c1070988e4f1ad8c39"}