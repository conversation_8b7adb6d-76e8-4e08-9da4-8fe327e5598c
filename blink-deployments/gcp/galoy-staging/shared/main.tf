locals {
  name_prefix         = "galoy-staging"
  gcp_project         = "galoy-staging"
  gcp_region          = "us-east1"
  root_domain         = "staging.blink.sv"
  cluster_name        = "${local.name_prefix}-cluster"
  bitcoin_network     = "signet"
  galoy_instance_name = "Blink Staging"

  unsupported_countries = [
    "RU", # Russia
    "CU", # Cuba
    "IR", # Iran
    "KP", # North Korea
    "SY"  # Syria
  ]
}

output "galoy_instance_name" {
  value = local.galoy_instance_name
}

output "name_prefix" {
  value = local.name_prefix
}

output "gcp_project" {
  value = local.gcp_project
}

output "gcp_region" {
  value = local.gcp_region
}

output "cluster_name" {
  value = local.cluster_name
}

output "root_domain" {
  value = local.root_domain
}

output "bitcoin_network" {
  value = local.bitcoin_network
}

output "unsupported_countries" {
  value = local.unsupported_countries
}
