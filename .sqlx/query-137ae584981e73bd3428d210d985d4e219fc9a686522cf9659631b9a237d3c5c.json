{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT id FROM bria_profiles WHERE name = $1) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false]}, "hash": "137ae584981e73bd3428d210d985d4e219fc9a686522cf9659631b9a237d3c5c"}