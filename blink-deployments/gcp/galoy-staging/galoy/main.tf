module "shared" {
  source = "../shared"
}

variable "secrets" {
  sensitive = true
}

variable "smoketest_kubeconfig" {
  sensitive = true
}

module "galoy" {
  source = "../../../modules/galoy/"

  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  lightning_address_domain = "pay.staging.blink.sv"
  backups_bucket_name      = "blink-staging-backups"
  funder_user_name         = "BitcoinBeachMarketing"

  lnd_priority = "lnd2"

  quizzes_allow_phone_countries = []
  quizzes_deny_phone_countries  = []
  quizzes_deny_asns             = []

  accounts_deny_ip_countries    = []
  accounts_deny_phone_countries = []
  accounts_enable_phone_check   = false

  email_domain     = "staging.blink.sv"
  secondary_domain = "staging.bbw.sv"

  secrets                = var.secrets
  smoketest_kubeconfig   = var.smoketest_kubeconfig
  bitcoin_network        = module.shared.bitcoin_network
  name_prefix            = module.shared.name_prefix
  galoy_instance_name    = module.shared.galoy_instance_name
  root_domain            = module.shared.root_domain
  unsupported_countries  = module.shared.unsupported_countries
  ip_recording_enabled   = true
  proxy_checking_enabled = true

  unsecure_default_login_code_enabled = true

  extra_cors_allowed_origins = ["http://localhost:3000", "https://hoppscotch.io"]

  api_keys_prefix  = "galoy_staging"
  api_keys_pg_tier = "db-custom-1-3840"

  notifications_pg_tier = "db-custom-1-3840"

  mongodb_ssd_size = "9Gi"

  openai_assistant_id = "asst_SctirorHgva5DjfRWrSx1ysv"

  twilio_messaging_welcome_content_sid = "HX9f49a8ac20ca40100092fa583a7e5f4f"
}

output "postgresql_creds" {
  value     = module.galoy.postgresql_creds
  sensitive = true
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/galoy"
  }
}
