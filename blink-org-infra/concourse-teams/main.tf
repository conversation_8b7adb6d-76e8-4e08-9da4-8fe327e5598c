locals {
  galoy_devs = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:k9ert",
    "user:github:openoms",
    "user:github:dolcalmi",
  ]
  galoy_non_tech = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:pretyflaco",
  ]
  galoy_automation = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:k9ert",
    "user:github:openoms",
  ]
  galoy_bbw = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:openoms",
  ]
  galoy_reporting = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:k9ert",
    "user:github:openoms",
  ]
  galoy_mobile = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:github:k9ert",
    "user:github:openoms",
  ]
  volcano_prod = [
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
    "user:oidc:<EMAIL>",
  ]
}

provider "concourse" {
  target = "galoy"
}

resource "concourse_team" "dev" {
  team_name = "dev"

  owners = local.galoy_automation

  members = local.galoy_devs

  viewers = concat(local.galoy_non_tech)
}

resource "concourse_team" "galoy" {
  team_name = "galoy"

  owners = local.galoy_bbw

  members = ["user:github:dolcalmi", "user:github:k9ert"]

  viewers = concat(local.galoy_devs, local.galoy_non_tech)
}

resource "concourse_team" "cepler" {
  team_name = "cepler"

  owners = toset(["user:oidc:<EMAIL>"])

  viewers = toset(local.galoy_automation)
}

resource "concourse_team" "galoy_reporting" {
  team_name = "galoy-reporting"

  owners = toset(["user:oidc:<EMAIL>"])

  members = toset(local.galoy_reporting)
}

resource "concourse_team" "galoy_mobile" {
  team_name = "galoy-mobile"

  owners = ["user:github:dolcalmi"]

  members = local.galoy_mobile

  viewers = concat(local.galoy_devs, local.galoy_non_tech)
}

resource "concourse_team" "volcano_prod" {
  team_name = "volcano-prod"

  owners = local.volcano_prod

  viewers = concat(local.galoy_devs, local.galoy_non_tech)
}

terraform {
  backend "gcs" {
    bucket = "galoy-org-tf-state"
    prefix = "galoy-org/concourse-teams"
  }
  required_providers {
    concourse = {
      # using the terraform registry because opentofu doesn't have the PGP key uploaded
      source  = "registry.terraform.io/terraform-provider-concourse/concourse"
      version = "8.0.1"
    }
  }
}
