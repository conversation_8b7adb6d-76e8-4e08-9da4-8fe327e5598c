mongodb:
  resources:
    requests:
      cpu: 1200m
      memory: 3500Mi
    limits:
      cpu: 2500m
      memory: 6000Mi
  extraFlags:
    - "--wiredTigerCacheSizeGB=1.35"
    - "--maxConns=2000"

galoy:
  api:
    resources:
      requests:
        cpu: 500m
        memory: 600Mi
      limits:
        cpu: 1500m
        memory: 1500Mi
  trigger:
    resources:
      requests:
        cpu: 750m
        memory: 1200Mi
      limits:
        cpu: 1500m
        memory: 2000Mi
  exporter:
    resources:
      requests:
        cpu: 50m
        memory: 150Mi
      limits:
        cpu: 150m
        memory: 300Mi
  mongoBackupCron:
    resources:
      requests:
        cpu: 2000m
        memory: 1000Mi
      limits:
        cpu: 3000m
        memory: 1500Mi
  galoyCron:
    resources:
      requests:
        cpu: 1000m
        memory: 600Mi
      limits:
        cpu: 1500m
        memory: 1200Mi
  mongodbMigrationJob:
    resources:
      requests:
        cpu: 150m
        memory: 50Mi
      limits:
        cpu: 300m
        memory: 150Mi
  notifications:
    serverDeployment:
      replicas: 2
      resources:
        requests:
          cpu: 50m
          memory: 50Mi
        limits:
          cpu: 100m
          memory: 250Mi
    jobsDeployment:
      replicas: 2
      resources:
        requests:
          cpu: 1250m
          memory: 1500Mi
        limits:
          cpu: 2000m
          memory: 3000Mi
