# Default values for galoy-deps.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

strimzi-kafka-operator:
  enabled: true
  nameOverride: kafka
  watchNamespaces: []
  kafka:
    listener:
      type: cluster-ip
    resources: {}
  zookeeper:
    resources: {}
  resources: {}

kubemonkey:
  enabled: true
  fullnameOverride: kubemonkey
  resources: {}

opentelemetry-collector:
  image:
    repository: "otel/opentelemetry-collector-k8s"
  service:
    enabled: true
  extraEnvs:
  - name: K8S_NODE_NAME
    valueFrom:
      fieldRef:
        fieldPath: spec.nodeName
  enabled: true
  clusterRole:
    create: true
    rules:
    - apiGroups: [""]
      resources: ["nodes/stats"]
      verbs: ["get"]
    - apiGroups: [""]
      resources: ["events"]
      verbs: ["get", "list", "watch"]
  presets:
    kubernetesEvents:
      enabled: true
  resources: {}
  mode: daemonset
  config:
    exporters:
      debug: {}
      otlp:
        endpoint: "api.honeycomb.io:443"
        headers:
          "x-honeycomb-team": ${HONEYCOMB_API_KEY}
          "x-honeycomb-dataset": ${HONEYCOMB_DATASET}
    extensions:
      health_check: {}
    processors:
      batch: {}
      tail_sampling:
        policies:
        - name: status_code
          type: status_code
          status_code:
            status_codes: [ERROR]
        - name: probabilistic
          type: probabilistic
          probabilistic: { sampling_percentage: 100 }
      attributes:
        actions:
        - key: graphql.variables.input.code
          action: update
          value: "<redacted>"
        - key: code.function.params.code
          action: update
          value: "<redacted>"
        - key: code.function.params.token
          action: update
          value: "<redacted>"
        - key: code.function.params.cookie
          action: update
          value: "<redacted>"
        - key: code.function.params.authToken
          action: update
          value: "<redacted>"
        - key: code.function.params.totpCode
          action: update
          value: "<redacted>"
        - key: code.function.params.body
          action: update
          value: "<redacted>"
        - key: code.function.params.macaroon
          action: update
          value: "<redacted>"
        - key: code.function.params.cert
          action: update
          value: "<redacted>"
        - key: code.function.params.secret
          action: update
          value: "<redacted>"
        - key: code.function.params.rawHeaders
          action: update
          value: "<redacted>"
        - key: code.function.params.key
          action: update
          value: "<redacted>"
        - key: code.function.params.value
          action: update
          value: "<redacted>"
        - key: args
          action: update
          value: "<redacted>"
        - key: graphql.variables.input.jwt
          action: update
          value: "<redacted>"
        - key: code.function.params.jwt
          action: update
          value: "<redacted>"
        - key: code.function.params.password
          action: update
          value: "<redacted>"
        - key: graphql.variables.input.totpCode
          action: update
          value: "<redacted>"
        - key: graphql.variables.input.authToken
          action: update
          value: "<redacted>"
      # Default memory limiter configuration for the collector based on k8s resource limits.
      memory_limiter:
        # check_interval is the time between measurements of memory usage.
        check_interval: 5s
        # By default limit_mib is set to 80% of ".Values.resources.limits.memory"
        limit_percentage: 80
        # By default spike_limit_mib is set to 25% of ".Values.resources.limits.memory"
        spike_limit_percentage: 25
      resourcedetection:
        detectors: [env, gcp]
        timeout: 5s
        override: false
      k8sattributes:
        passthrough: true
      filter/ottl:
        error_mode: ignore
        traces:
          span:
            - 'kind.string != "Server" and IsMatch(resource.attributes["service.name"], ".*-kratos")'
            - 'name == "query_planning" and IsMatch(resource.attributes["service.name"], ".*-router")'
          spanevent:
            - 'name == "" and IsMatch(resource.attributes["service.name"], ".*-router")'
        metrics:
          metric:
            - 'resource.attributes["k8s.namespace.name"] == "kube-system"'
    receivers:
      kubeletstats:
        collection_interval: 60s
        auth_type: "serviceAccount"
        endpoint: "https://${env:K8S_NODE_NAME}:10250"
        insecure_skip_verify: true
      k8s_events:
        # No additional configuration needed for k8s_events
      jaeger:
        protocols:
          grpc:
            endpoint: 0.0.0.0:14250
          thrift_http:
            endpoint: 0.0.0.0:14268
          thrift_compact:
            endpoint: 0.0.0.0:6831
          thrift_binary:
            endpoint: 0.0.0.0:6832
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
    service:
      extensions:
        - health_check
      pipelines:
        logs:
          exporters:
            - debug
            - otlp
          processors:
            - memory_limiter
            - resourcedetection
            - attributes
            - k8sattributes
            - batch
          receivers:
            - otlp
            - k8s_events
        metrics:
          exporters:
            - otlp
            - debug
          processors:
            - filter/ottl
            - memory_limiter
            - batch
          receivers:
            - otlp
            - kubeletstats
        traces:
          exporters:
            - debug
            - otlp
          processors:
            - filter/ottl
            - memory_limiter
            - resourcedetection
            - attributes
            - k8sattributes
            - tail_sampling
            - batch
          receivers:
            - jaeger
            - otlp
  ports:
    otlp:
      enabled: true
      containerPort: 4317
      servicePort: 4317
      hostPort: 4317
      protocol: TCP
    jaeger-thrift-b:
      enabled: true
      containerPort: 6832
      servicePort: 6832
      hostPort: 6832
      protocol: UDP
    jaeger-thrift:
      enabled: true
      containerPort: 14268
      servicePort: 14268
      hostPort: 14268
      protocol: TCP

cert-manager:
  enabled: true
  installCRDs: true
  resources: {}

ingress-nginx:
  enabled: true
  controller:
    resources: {}
    replicaCount: 2
    ingressClassResource:
      default: true
      watchIngressWithoutClass: true
    config:
      enable-opentracing: true
      jaeger-propagation-format: "w3c"
      log-format-upstream: "$remote_addr - $remote_user [$time_local] \"$request\" $status $body_bytes_sent \"$http_referer\" \"$http_user_agent\" $request_length $request_time [$proxy_upstream_name] [$proxy_alternative_upstream_name] $upstream_addr $upstream_response_length $upstream_response_time $upstream_status $req_id"
    service:
      externalTrafficPolicy: Local
      # service type should be NodePort when deploying locally
      type: LoadBalancer
    metrics:
      enabled: true
      service:
        annotations:
          prometheus.io/scrape: "true"
          prometheus.io/port: "10254"
