#!/bin/bash

cat <<EOF > supergraph-config.yaml
federation_version: =2.3.2
subgraphs:
  galoy:
    routing_url: url
    schema:
      file: ./supergraph-galoy/modules/galoy/vendor/galoy/chart/apollo-router/supergraph.graphql
  circles:
    routing_url: url
    schema:
      file: ./supergraph-blink-addons/modules/services/blink-addons/vendor/circles/chart/subgraph/schema.graphql
EOF

rover supergraph compose --config supergraph-config.yaml --elv2-license accept \
  --output blink-deployments-repo/modules/galoy/supergraph.graphql

pushd blink-deployments-repo

if [[ -z $(git config --global user.email) ]]; then
  git config --global user.email "<EMAIL>"
fi
if [[ -z $(git config --global user.name) ]]; then
  git config --global user.name "blinkbitcoinbot"
fi

(
  cd $(git rev-parse --show-toplevel)
  git add modules/galoy/supergraph.graphql
  git status
  # TODO: do we need a ref in the commit msg?
  git commit -m "chore: update supergraph"
)

exit 1
