interface Account {
  callbackEndpoints: [CallbackEndpoint!]!
  callbackPortalUrl: String!
  csvTransactions(walletIds: [WalletId!]!): String!
  defaultWallet: PublicWallet!
  defaultWalletId: WalletId! @deprecated(reason: "Shifting property to 'defaultWallet.id'")
  displayCurrency: DisplayCurrency!
  id: ID!
  invoices(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
    walletIds: [WalletId]
  ): InvoiceConnection
  level: AccountLevel!
  limits: AccountLimits!
  notificationSettings: NotificationSettings!
  pendingIncomingTransactions(walletIds: [WalletId]): [Transaction!]!
  realtimePrice: RealtimePrice!
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
    walletIds: [WalletId]
  ): TransactionConnection
  walletById(walletId: WalletId!): Wallet!
  wallets: [Wallet!]!
}

type AccountDeletePayload {
  errors: [Error!]!
  success: Boolean!
}

input AccountDisableNotificationCategoryInput {
  category: NotificationCategory!
  channel: NotificationChannel
}

input AccountDisableNotificationChannelInput {
  channel: NotificationChannel!
}

input AccountEnableNotificationCategoryInput {
  category: NotificationCategory!
  channel: NotificationChannel
}

input AccountEnableNotificationChannelInput {
  channel: NotificationChannel!
}

enum AccountLevel {
  ONE
  THREE
  TWO
  ZERO
}

interface AccountLimit {
  """The rolling time interval in seconds that the limits would apply for."""
  interval: Seconds

  """
  The amount of cents remaining below the limit for the current 24 hour period.
  """
  remainingLimit: CentAmount

  """The current maximum limit for a given 24 hour period."""
  totalLimit: CentAmount!
}

type AccountLimits {
  """
  Limits for converting between currencies among a account's own wallets.
  """
  convert: [AccountLimit!]!

  """Limits for sending to other internal accounts."""
  internalSend: [AccountLimit!]!

  """Limits for withdrawing to external onchain or lightning destinations."""
  withdrawal: [AccountLimit!]!
}

input AccountUpdateDefaultWalletIdInput {
  walletId: WalletId!
}

type AccountUpdateDefaultWalletIdPayload {
  account: ConsumerAccount
  errors: [Error!]!
}

input AccountUpdateDisplayCurrencyInput {
  currency: DisplayCurrency!
}

type AccountUpdateDisplayCurrencyPayload {
  account: ConsumerAccount
  errors: [Error!]!
}

type AccountUpdateNotificationSettingsPayload {
  account: ConsumerAccount
  errors: [Error!]!
}

"""An Opaque Bearer token"""
scalar AuthToken

type AuthTokenPayload {
  authToken: AuthToken
  errors: [Error!]!
  totpRequired: Boolean
}

type Authorization {
  scopes: [Scope!]!
}

"""
A wallet belonging to an account which contains a BTC balance and a list of transactions.
"""
type BTCWallet implements Wallet {
  accountId: ID!

  """A balance stored in BTC."""
  balance: SignedAmount!
  id: ID!
  invoiceByPaymentHash(paymentHash: PaymentHash!): Invoice!

  """A list of all invoices associated with walletIds optionally passed."""
  invoices(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): InvoiceConnection

  """An unconfirmed incoming onchain balance."""
  pendingIncomingBalance: SignedAmount!
  pendingIncomingTransactions: [Transaction!]!
  pendingIncomingTransactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!
  ): [Transaction!]!
  transactionById(transactionId: ID!): Transaction!

  """A list of BTC transactions associated with this wallet."""
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection
  transactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!

    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection
  transactionsByPaymentHash(paymentHash: PaymentHash!): [Transaction!]!
  transactionsByPaymentRequest(paymentRequest: LnPaymentRequest!): [Transaction!]!
  walletCurrency: WalletCurrency!
}

type BuildInformation {
  commitHash: String
  helmRevision: Int
}

type CallbackEndpoint {
  id: EndpointId!
  url: EndpointUrl!
}

input CallbackEndpointAddInput {
  """callback endpoint to be called"""
  url: EndpointUrl!
}

type CallbackEndpointAddPayload {
  errors: [Error!]!
  id: EndpointId
}

input CallbackEndpointDeleteInput {
  id: EndpointId!
}

type CaptchaCreateChallengePayload {
  errors: [Error!]!
  result: CaptchaCreateChallengeResult
}

type CaptchaCreateChallengeResult {
  challengeCode: String!
  failbackMode: Boolean!
  id: String!
  newCaptcha: Boolean!
}

input CaptchaRequestAuthCodeInput {
  challengeCode: String!
  channel: PhoneCodeChannelType
  phone: Phone!
  secCode: String!
  validationCode: String!
}

"""(Positive) Cent amount (1/100 of a dollar)"""
scalar CentAmount

type CentAmountPayload {
  amount: CentAmount
  errors: [Error!]!
}

type ConsumerAccount implements Account {
  callbackEndpoints: [CallbackEndpoint!]!
  callbackPortalUrl: String!

  """
  return CSV stream, base64 encoded, of the list of transactions in the wallet
  """
  csvTransactions(walletIds: [WalletId!]!): String!
  defaultWallet: PublicWallet!
  defaultWalletId: WalletId!
  displayCurrency: DisplayCurrency!
  id: ID!

  """A list of all invoices associated with walletIds optionally passed."""
  invoices(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
    walletIds: [WalletId]
  ): InvoiceConnection
  level: AccountLevel!
  limits: AccountLimits!
  notificationSettings: NotificationSettings!
  pendingIncomingTransactions(walletIds: [WalletId]): [Transaction!]!

  """List the quiz questions of the consumer account"""
  quiz: [Quiz!]!
  realtimePrice: RealtimePrice!

  """
  A list of all transactions associated with walletIds optionally passed.
  """
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
    walletIds: [WalletId]
  ): TransactionConnection
  walletById(walletId: WalletId!): Wallet!
  wallets: [Wallet!]!
}

"""
An alias name that a user can set for a wallet (with which they have transactions)
"""
scalar ContactAlias

type Coordinates {
  latitude: Float!
  longitude: Float!
}

type Country {
  id: CountryCode!
  supportedAuthChannels: [PhoneCodeChannelType!]!
}

"""A CCA2 country code (ex US, FR, etc)"""
scalar CountryCode

type Currency {
  flag: String!
  fractionDigits: Int!
  id: ID!
  name: String!
  symbol: String!
}

type CurrencyConversionEstimation {
  """Amount in satoshis."""
  btcSatAmount: SatAmount!
  id: ID!

  """
  Unix timestamp (number of seconds elapsed since January 1, 1970 00:00:00 UTC)
  """
  timestamp: Timestamp!

  """Amount in USD cents."""
  usdCentAmount: CentAmount!
}

type DepositFeesInformation {
  minBankFee: String!

  """below this amount minBankFee will be charged"""
  minBankFeeThreshold: String!

  """ratio to charge as basis points above minBankFeeThreshold amount"""
  ratio: String!
}

input DeviceNotificationTokenCreateInput {
  deviceToken: String!
}

"""Display currency of an account"""
scalar DisplayCurrency

type Email {
  address: EmailAddress
  verified: Boolean
}

"""Email address"""
scalar EmailAddress

"""
An id to be passed between registrationInitiate and registrationValidate for confirming email
"""
scalar EmailRegistrationId

scalar EndpointId

"""Url that will be fetched on events for the account"""
scalar EndpointUrl

interface Error {
  code: String
  message: String!
  path: [String]
}

enum ExchangeCurrencyUnit {
  BTCSAT
  USDCENT
}

"""Feedback shared with our user"""
scalar Feedback

input FeedbackSubmitInput {
  feedback: Feedback!
}

type FeesInformation {
  deposit: DepositFeesInformation!
}

"""
Provides global settings for the application which might have an impact for the user.
"""
type Globals {
  buildInformation: BuildInformation!
  feesInformation: FeesInformation!

  """
  The domain name for lightning addresses accepted by this Galoy instance
  """
  lightningAddressDomain: String!
  lightningAddressDomainAliases: [String!]!

  """
  Which network (mainnet, testnet, regtest, signet) this instance is running on.
  """
  network: Network!

  """
  A list of public keys for the running lightning nodes.
  This can be used to know if an invoice belongs to one of our nodes.
  """
  nodesIds: [String!]!

  """A list of countries and their supported auth channels"""
  supportedCountries: [Country!]!
}

type GraphQLApplicationError implements Error {
  code: String
  message: String!
  path: [String]
}

"""Hex-encoded string of 32 bytes"""
scalar Hex32Bytes

union InitiationVia = InitiationViaIntraLedger | InitiationViaLn | InitiationViaOnChain

type InitiationViaIntraLedger {
  counterPartyUsername: Username
  counterPartyWalletId: WalletId
}

type InitiationViaLn {
  paymentHash: PaymentHash!

  """Bolt11 invoice"""
  paymentRequest: LnPaymentRequest!
}

type InitiationViaOnChain {
  address: OnChainAddress!
}

input IntraLedgerPaymentSendInput {
  """Amount in satoshis."""
  amount: SatAmount!

  """Optional memo to be attached to the payment."""
  memo: Memo
  recipientWalletId: WalletId!

  """The wallet ID of the sender."""
  walletId: WalletId!
}

type IntraLedgerUpdate {
  amount: SatAmount! @deprecated(reason: "Deprecated in favor of transaction")
  displayCurrencyPerSat: Float! @deprecated(reason: "Deprecated in favor of transaction")
  transaction: Transaction!
  txNotificationType: TxNotificationType!
  usdPerSat: Float! @deprecated(reason: "updated over displayCurrencyPerSat")
  walletId: WalletId! @deprecated(reason: "Deprecated in favor of transaction")
}

input IntraLedgerUsdPaymentSendInput {
  """Amount in cents."""
  amount: CentAmount!

  """Optional memo to be attached to the payment."""
  memo: Memo
  recipientWalletId: WalletId!

  """The wallet ID of the sender."""
  walletId: WalletId!
}

"""A lightning invoice."""
interface Invoice {
  createdAt: Timestamp!

  """The unique external id set for the invoice."""
  externalId: TxExternalId!

  """The payment hash of the lightning invoice."""
  paymentHash: PaymentHash!

  """The bolt11 invoice to be paid."""
  paymentRequest: LnPaymentRequest!

  """
  The payment secret of the lightning invoice. This is not the preimage of the payment hash.
  """
  paymentSecret: LnPaymentSecret!

  """The payment status of the invoice."""
  paymentStatus: InvoicePaymentStatus!
}

"""A connection to a list of items."""
type InvoiceConnection {
  """A list of edges."""
  edges: [InvoiceEdge!]

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""An edge in a connection."""
type InvoiceEdge {
  """A cursor for use in pagination"""
  cursor: String!

  """The item at the end of the edge"""
  node: Invoice!
}

enum InvoicePaymentStatus {
  EXPIRED
  PAID
  PENDING
}

scalar Language

input LnAddressPaymentSendInput {
  """Amount in satoshis."""
  amount: SatAmount!

  """Lightning address to send to."""
  lnAddress: String!

  """Wallet ID to send bitcoin from."""
  walletId: WalletId!
}

type LnInvoice implements Invoice {
  createdAt: Timestamp!
  externalId: TxExternalId!
  paymentHash: PaymentHash!
  paymentRequest: LnPaymentRequest!
  paymentSecret: LnPaymentSecret!
  paymentStatus: InvoicePaymentStatus!
  satoshis: SatAmount!
}

input LnInvoiceCancelInput {
  paymentHash: PaymentHash!

  """Wallet ID for a wallet associated with the current account."""
  walletId: WalletId!
}

input LnInvoiceCreateInput {
  """Amount in satoshis."""
  amount: SatAmount!

  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """Optional memo for the lightning invoice."""
  memo: Memo

  """Wallet ID for a BTC wallet belonging to the current account."""
  walletId: WalletId!
}

input LnInvoiceCreateOnBehalfOfRecipientInput {
  """Amount in satoshis."""
  amount: SatAmount!
  descriptionHash: Hex32Bytes

  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """Optional memo for the lightning invoice."""
  memo: Memo

  """Wallet ID for a BTC wallet which belongs to any account."""
  recipientWalletId: WalletId!
}

input LnInvoiceFeeProbeInput {
  paymentRequest: LnPaymentRequest!
  walletId: WalletId!
}

type LnInvoicePayload {
  errors: [Error!]!
  invoice: LnInvoice
}

input LnInvoicePaymentInput {
  """Optional memo to associate with the lightning invoice."""
  memo: Memo

  """Payment request representing the invoice which is being paid."""
  paymentRequest: LnPaymentRequest!

  """
  Wallet ID with sufficient balance to cover amount of invoice.  Must belong to the account of the current user.
  """
  walletId: WalletId!
}

type LnInvoicePaymentStatus {
  paymentHash: PaymentHash
  paymentRequest: LnPaymentRequest
  status: InvoicePaymentStatus
}

input LnInvoicePaymentStatusByHashInput {
  paymentHash: PaymentHash!
}

input LnInvoicePaymentStatusByPaymentRequestInput {
  paymentRequest: LnPaymentRequest!
}

input LnInvoicePaymentStatusInput {
  paymentRequest: LnPaymentRequest!
}

type LnInvoicePaymentStatusPayload {
  errors: [Error!]!
  paymentHash: PaymentHash
  paymentRequest: LnPaymentRequest
  status: InvoicePaymentStatus
}

type LnNoAmountInvoice implements Invoice {
  createdAt: Timestamp!
  externalId: TxExternalId!
  paymentHash: PaymentHash!
  paymentRequest: LnPaymentRequest!
  paymentSecret: LnPaymentSecret!
  paymentStatus: InvoicePaymentStatus!
}

input LnNoAmountInvoiceCreateInput {
  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """Optional memo for the lightning invoice."""
  memo: Memo

  """
  ID for either a USD or BTC wallet belonging to the account of the current user.
  """
  walletId: WalletId!
}

input LnNoAmountInvoiceCreateOnBehalfOfRecipientInput {
  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """Optional memo for the lightning invoice."""
  memo: Memo

  """
  ID for either a USD or BTC wallet which belongs to the account of any user.
  """
  recipientWalletId: WalletId!
}

input LnNoAmountInvoiceFeeProbeInput {
  amount: SatAmount!
  paymentRequest: LnPaymentRequest!
  walletId: WalletId!
}

type LnNoAmountInvoicePayload {
  errors: [Error!]!
  invoice: LnNoAmountInvoice
}

input LnNoAmountInvoicePaymentInput {
  """Amount to pay in satoshis."""
  amount: SatAmount!

  """Optional memo to associate with the lightning invoice."""
  memo: Memo

  """Payment request representing the invoice which is being paid."""
  paymentRequest: LnPaymentRequest!

  """
  Wallet ID with sufficient balance to cover amount defined in mutation request.  Must belong to the account of the current user.
  """
  walletId: WalletId!
}

input LnNoAmountUsdInvoiceFeeProbeInput {
  amount: CentAmount!
  paymentRequest: LnPaymentRequest!
  walletId: WalletId!
}

input LnNoAmountUsdInvoicePaymentInput {
  """Amount to pay in USD cents."""
  amount: CentAmount!

  """Optional memo to associate with the lightning invoice."""
  memo: Memo

  """Payment request representing the invoice which is being paid."""
  paymentRequest: LnPaymentRequest!

  """
  Wallet ID with sufficient balance to cover amount defined in mutation request.  Must belong to the account of the current user.
  """
  walletId: WalletId!
}

scalar LnPaymentPreImage

"""BOLT11 lightning invoice payment request with the amount included"""
scalar LnPaymentRequest

scalar LnPaymentSecret

type LnUpdate {
  paymentHash: PaymentHash! @deprecated(reason: "Deprecated in favor of transaction")
  status: InvoicePaymentStatus!
  transaction: Transaction!
  walletId: WalletId! @deprecated(reason: "Deprecated in favor of transaction")
}

input LnUsdInvoiceBtcDenominatedCreateOnBehalfOfRecipientInput {
  """Amount in satoshis."""
  amount: SatAmount!
  descriptionHash: Hex32Bytes

  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """
  Optional memo for the lightning invoice. Acts as a note to the recipient.
  """
  memo: Memo

  """Wallet ID for a USD wallet which belongs to the account of any user."""
  recipientWalletId: WalletId!
}

input LnUsdInvoiceCreateInput {
  """Amount in USD cents."""
  amount: CentAmount!

  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """Optional memo for the lightning invoice."""
  memo: Memo

  """Wallet ID for a USD wallet belonging to the current user."""
  walletId: WalletId!
}

input LnUsdInvoiceCreateOnBehalfOfRecipientInput {
  """Amount in USD cents."""
  amount: CentAmount!
  descriptionHash: Hex32Bytes

  """Optional invoice expiration time in minutes."""
  expiresIn: Minutes
  externalId: TxExternalId

  """
  Optional memo for the lightning invoice. Acts as a note to the recipient.
  """
  memo: Memo

  """Wallet ID for a USD wallet which belongs to the account of any user."""
  recipientWalletId: WalletId!
}

input LnUsdInvoiceFeeProbeInput {
  paymentRequest: LnPaymentRequest!
  walletId: WalletId!
}

input LnurlPaymentSendInput {
  """Amount in satoshis."""
  amount: SatAmount!

  """Lnurl string to send to."""
  lnurl: String!

  """Wallet ID to send bitcoin from."""
  walletId: WalletId!
}

type MapInfo {
  coordinates: Coordinates!
  title: String!
}

type MapMarker {
  mapInfo: MapInfo!
  username: Username!
}

"""Text field in a lightning payment transaction"""
scalar Memo

type Merchant {
  """
  GPS coordinates for the merchant that can be used to place the related business on a map
  """
  coordinates: Coordinates!
  createdAt: Timestamp!
  id: ID!
  title: String!

  """The username of the merchant"""
  username: Username!

  """Whether the merchant has been validated"""
  validated: Boolean!
}

input MerchantMapSuggestInput {
  latitude: Float!
  longitude: Float!
  title: String!
  username: Username!
}

type MerchantPayload {
  errors: [Error!]!
  merchant: Merchant
}

"""(Positive) amount of minutes"""
scalar Minutes

type MobileVersions {
  currentSupported: Int!
  minSupported: Int!
  platform: String!
}

type Mutation {
  accountDelete: AccountDeletePayload!
  accountDisableNotificationCategory(input: AccountDisableNotificationCategoryInput!): AccountUpdateNotificationSettingsPayload!
  accountDisableNotificationChannel(input: AccountDisableNotificationChannelInput!): AccountUpdateNotificationSettingsPayload!
  accountEnableNotificationCategory(input: AccountEnableNotificationCategoryInput!): AccountUpdateNotificationSettingsPayload!
  accountEnableNotificationChannel(input: AccountEnableNotificationChannelInput!): AccountUpdateNotificationSettingsPayload!
  accountUpdateDefaultWalletId(input: AccountUpdateDefaultWalletIdInput!): AccountUpdateDefaultWalletIdPayload!
  accountUpdateDisplayCurrency(input: AccountUpdateDisplayCurrencyInput!): AccountUpdateDisplayCurrencyPayload!
  callbackEndpointAdd(input: CallbackEndpointAddInput!): CallbackEndpointAddPayload!
  callbackEndpointDelete(input: CallbackEndpointDeleteInput!): SuccessPayload!
  captchaCreateChallenge: CaptchaCreateChallengePayload!
  captchaRequestAuthCode(input: CaptchaRequestAuthCodeInput!): SuccessPayload!
  deviceNotificationTokenCreate(input: DeviceNotificationTokenCreateInput!): SuccessPayload!
  feedbackSubmit(input: FeedbackSubmitInput!): SuccessPayload!

  """
  Actions a payment which is internal to the ledger e.g. it does
  not use onchain/lightning. Returns payment status (success,
  failed, pending, already_paid).
  """
  intraLedgerPaymentSend(input: IntraLedgerPaymentSendInput!): PaymentSendPayload!

  """
  Actions a payment which is internal to the ledger e.g. it does
  not use onchain/lightning. Returns payment status (success,
  failed, pending, already_paid).
  """
  intraLedgerUsdPaymentSend(input: IntraLedgerUsdPaymentSendInput!): PaymentSendPayload!

  """Sends a payment to a lightning address."""
  lnAddressPaymentSend(input: LnAddressPaymentSendInput!): PaymentSendPayload!

  """Cancel an unpaid lightning invoice for an associated wallet."""
  lnInvoiceCancel(input: LnInvoiceCancelInput!): SuccessPayload!

  """
  Returns a lightning invoice for an associated wallet.
  When invoice is paid the value will be credited to a BTC wallet.
  Expires after 'expiresIn' or 24 hours.
  """
  lnInvoiceCreate(input: LnInvoiceCreateInput!): LnInvoicePayload!

  """
  Returns a lightning invoice for an associated wallet.
  When invoice is paid the value will be credited to a BTC wallet.
  Expires after 'expiresIn' or 24 hours.
  """
  lnInvoiceCreateOnBehalfOfRecipient(input: LnInvoiceCreateOnBehalfOfRecipientInput!): LnInvoicePayload!
  lnInvoiceFeeProbe(input: LnInvoiceFeeProbeInput!): SatAmountPayload!

  """
  Pay a lightning invoice using a balance from a wallet which is owned by the account of the current user.
  Provided wallet can be USD or BTC and must have sufficient balance to cover amount in lightning invoice.
  Returns payment status (success, failed, pending, already_paid).
  """
  lnInvoicePaymentSend(input: LnInvoicePaymentInput!): PaymentSendPayload!

  """
  Returns a lightning invoice for an associated wallet.
  Can be used to receive any supported currency value (currently USD or BTC).
  Expires after 'expiresIn' or 24 hours for BTC invoices or 5 minutes for USD invoices.
  """
  lnNoAmountInvoiceCreate(input: LnNoAmountInvoiceCreateInput!): LnNoAmountInvoicePayload!

  """
  Returns a lightning invoice for an associated wallet.
  Can be used to receive any supported currency value (currently USD or BTC).
  Expires after 'expiresIn' or 24 hours for BTC invoices or 5 minutes for USD invoices.
  """
  lnNoAmountInvoiceCreateOnBehalfOfRecipient(input: LnNoAmountInvoiceCreateOnBehalfOfRecipientInput!): LnNoAmountInvoicePayload!
  lnNoAmountInvoiceFeeProbe(input: LnNoAmountInvoiceFeeProbeInput!): SatAmountPayload!

  """
  Pay a lightning invoice using a balance from a wallet which is owned by the account of the current user.
  Provided wallet must be BTC and must have sufficient balance to cover amount specified in mutation request.
  Returns payment status (success, failed, pending, already_paid).
  """
  lnNoAmountInvoicePaymentSend(input: LnNoAmountInvoicePaymentInput!): PaymentSendPayload!
  lnNoAmountUsdInvoiceFeeProbe(input: LnNoAmountUsdInvoiceFeeProbeInput!): CentAmountPayload!

  """
  Pay a lightning invoice using a balance from a wallet which is owned by the account of the current user.
  Provided wallet must be USD and have sufficient balance to cover amount specified in mutation request.
  Returns payment status (success, failed, pending, already_paid).
  """
  lnNoAmountUsdInvoicePaymentSend(input: LnNoAmountUsdInvoicePaymentInput!): PaymentSendPayload!

  """
  Returns a lightning invoice denominated in satoshis for an associated wallet.
  When invoice is paid the equivalent value at invoice creation will be credited to a USD wallet.
  Expires after 'expiresIn' or 5 minutes (short expiry time because there is a USD/BTC exchange rate
    associated with the amount).
  """
  lnUsdInvoiceBtcDenominatedCreateOnBehalfOfRecipient(input: LnUsdInvoiceBtcDenominatedCreateOnBehalfOfRecipientInput!): LnInvoicePayload!

  """
  Returns a lightning invoice denominated in satoshis for an associated wallet.
  When invoice is paid the equivalent value at invoice creation will be credited to a USD wallet.
  Expires after 'expiresIn' or 5 minutes (short expiry time because there is a USD/BTC exchange rate
  associated with the amount).
  """
  lnUsdInvoiceCreate(input: LnUsdInvoiceCreateInput!): LnInvoicePayload!

  """
  Returns a lightning invoice denominated in satoshis for an associated wallet.
  When invoice is paid the equivalent value at invoice creation will be credited to a USD wallet.
  Expires after 'expiresIn' or 5 minutes (short expiry time because there is a USD/BTC exchange rate
    associated with the amount).
  """
  lnUsdInvoiceCreateOnBehalfOfRecipient(input: LnUsdInvoiceCreateOnBehalfOfRecipientInput!): LnInvoicePayload!
  lnUsdInvoiceFeeProbe(input: LnUsdInvoiceFeeProbeInput!): SatAmountPayload!

  """Sends a payment to a lightning address."""
  lnurlPaymentSend(input: LnurlPaymentSendInput!): PaymentSendPayload!
  merchantMapSuggest(input: MerchantMapSuggestInput!): MerchantPayload!
  onChainAddressCreate(input: OnChainAddressCreateInput!): OnChainAddressPayload!
  onChainAddressCurrent(input: OnChainAddressCurrentInput!): OnChainAddressPayload!
  onChainPaymentSend(input: OnChainPaymentSendInput!): PaymentSendPayload!
  onChainPaymentSendAll(input: OnChainPaymentSendAllInput!): PaymentSendPayload!
  onChainUsdPaymentSend(input: OnChainUsdPaymentSendInput!): PaymentSendPayload!
  onChainUsdPaymentSendAsBtcDenominated(input: OnChainUsdPaymentSendAsBtcDenominatedInput!): PaymentSendPayload!
  quizClaim(input: QuizClaimInput!): QuizClaimPayload!
  supportChatMessageAdd(input: SupportChatMessageAddInput!): SupportChatMessageAddPayload!
  supportChatReset: SuccessPayload!
  userContactUpdateAlias(input: UserContactUpdateAliasInput!): UserContactUpdateAliasPayload! @deprecated(reason: "will be moved to AccountContact")
  userEmailDelete: UserEmailDeletePayload!
  userEmailRegistrationInitiate(input: UserEmailRegistrationInitiateInput!): UserEmailRegistrationInitiatePayload!
  userEmailRegistrationValidate(input: UserEmailRegistrationValidateInput!): UserEmailRegistrationValidatePayload!
  userLogin(input: UserLoginInput!): AuthTokenPayload!
  userLoginUpgrade(input: UserLoginUpgradeInput!): UpgradePayload!
  userLogout(input: UserLogoutInput): SuccessPayload!
  userPhoneDelete: UserPhoneDeletePayload!
  userPhoneRegistrationInitiate(input: UserPhoneRegistrationInitiateInput!): SuccessPayload!
  userPhoneRegistrationValidate(input: UserPhoneRegistrationValidateInput!): UserPhoneRegistrationValidatePayload!
  userTotpDelete: UserTotpDeletePayload!
  userTotpRegistrationInitiate: UserTotpRegistrationInitiatePayload!
  userTotpRegistrationValidate(input: UserTotpRegistrationValidateInput!): UserTotpRegistrationValidatePayload!
  userUpdateLanguage(input: UserUpdateLanguageInput!): UserUpdateLanguagePayload!
  userUpdateUsername(input: UserUpdateUsernameInput!): UserUpdateUsernamePayload! @deprecated(reason: "Username will be moved to @Handle in Accounts. Also SetUsername naming should be used instead of UpdateUsername to reflect the idempotency of Handles")
}

type MyUpdatesPayload {
  errors: [Error!]!
  me: User
  update: UserUpdate
}

enum Network {
  mainnet
  regtest
  signet
  testnet
}

scalar NotificationCategory

enum NotificationChannel {
  PUSH
}

type NotificationChannelSettings {
  disabledCategories: [NotificationCategory!]!
  enabled: Boolean!
}

type NotificationSettings {
  push: NotificationChannelSettings!
}

"""An address for an on-chain bitcoin destination"""
scalar OnChainAddress

input OnChainAddressCreateInput {
  walletId: WalletId!
}

input OnChainAddressCurrentInput {
  walletId: WalletId!
}

type OnChainAddressPayload {
  address: OnChainAddress
  errors: [Error!]!
}

input OnChainPaymentSendAllInput {
  address: OnChainAddress!
  memo: Memo
  speed: PayoutSpeed! = FAST
  walletId: WalletId!
}

input OnChainPaymentSendInput {
  address: OnChainAddress!
  amount: SatAmount!
  memo: Memo
  speed: PayoutSpeed! = FAST
  walletId: WalletId!
}

type OnChainTxFee {
  amount: SatAmount!
}

scalar OnChainTxHash

type OnChainUpdate {
  amount: SatAmount! @deprecated(reason: "Deprecated in favor of transaction")
  displayCurrencyPerSat: Float! @deprecated(reason: "Deprecated in favor of transaction")
  transaction: Transaction!
  txHash: OnChainTxHash! @deprecated(reason: "Deprecated in favor of transaction")
  txNotificationType: TxNotificationType!
  usdPerSat: Float! @deprecated(reason: "updated over displayCurrencyPerSat")
  walletId: WalletId! @deprecated(reason: "Deprecated in favor of transaction")
}

input OnChainUsdPaymentSendAsBtcDenominatedInput {
  address: OnChainAddress!
  amount: SatAmount!
  memo: Memo
  speed: PayoutSpeed! = FAST
  walletId: WalletId!
}

input OnChainUsdPaymentSendInput {
  address: OnChainAddress!
  amount: CentAmount!
  memo: Memo
  speed: PayoutSpeed! = FAST
  walletId: WalletId!
}

type OnChainUsdTxFee {
  amount: CentAmount!
}

type OneDayAccountLimit implements AccountLimit {
  """
  The rolling time interval value in seconds for the current 24 hour period.
  """
  interval: Seconds

  """
  The amount of cents remaining below the limit for the current 24 hour period.
  """
  remainingLimit: CentAmount

  """The current maximum limit for a given 24 hour period."""
  totalLimit: CentAmount!
}

"""An authentication code valid for a single use"""
scalar OneTimeAuthCode

"""Information about pagination in a connection."""
type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: String

  """When paginating forwards, are there more items?"""
  hasNextPage: Boolean!

  """When paginating backwards, are there more items?"""
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: String
}

scalar PaymentHash

type PaymentSendPayload {
  errors: [Error!]!
  status: PaymentSendResult
  transaction: Transaction
}

enum PaymentSendResult {
  ALREADY_PAID
  FAILURE
  PENDING
  SUCCESS
}

enum PayoutSpeed {
  FAST
}

"""Phone number which includes country code"""
scalar Phone

enum PhoneCodeChannelType {
  SMS
  TELEGRAM
  WHATSAPP
}

"""
Price amount expressed in base/offset. To calculate, use: `base / 10^offset`
"""
type Price {
  base: SafeInt!
  currencyUnit: String!
  formattedAmount: String!
  offset: Int!
}

"""The range for the X axis in the BTC price graph"""
enum PriceGraphRange {
  FIVE_YEARS
  ONE_DAY
  ONE_MONTH
  ONE_WEEK
  ONE_YEAR
}

input PriceInput {
  amount: SatAmount!
  amountCurrencyUnit: ExchangeCurrencyUnit!
  priceCurrencyUnit: ExchangeCurrencyUnit!
}

interface PriceInterface {
  base: SafeInt!
  currencyUnit: String! @deprecated(reason: "Deprecated due to type renaming")
  offset: Int!
}

"""Price of 1 sat in base/offset. To calculate, use: `base / 10^offset`"""
type PriceOfOneSatInMinorUnit implements PriceInterface {
  base: SafeInt!
  currencyUnit: String! @deprecated(reason: "Deprecated due to type renaming")
  offset: Int!
}

"""
Price of 1 sat or 1 usd cent in base/offset. To calculate, use: `base / 10^offset`
"""
type PriceOfOneSettlementMinorUnitInDisplayMinorUnit implements PriceInterface {
  base: SafeInt!
  currencyUnit: String! @deprecated(reason: "Deprecated due to type renaming")
  formattedAmount: String! @deprecated(reason: "Deprecated please use `base / 10^offset`")
  offset: Int!
}

"""
Price of 1 usd cent in base/offset. To calculate, use: `base / 10^offset`
"""
type PriceOfOneUsdCentInMinorUnit implements PriceInterface {
  base: SafeInt!
  currencyUnit: String! @deprecated(reason: "Deprecated due to type renaming")
  offset: Int!
}

type PricePayload {
  errors: [Error!]!
  price: Price
}

type PricePoint {
  price: Price!

  """
  Unix timestamp (number of seconds elapsed since January 1, 1970 00:00:00 UTC)
  """
  timestamp: Timestamp!
}

"""
A public view of a generic wallet which stores value in one of our supported currencies.
"""
type PublicWallet {
  currency: WalletCurrency!
  id: ID!
  walletCurrency: WalletCurrency! @deprecated(reason: "Shifting property to 'currency'")
}

type Query {
  accountDefaultWallet(username: Username!, walletCurrency: WalletCurrency): PublicWallet!

  """Retrieve the list of scopes permitted for the user's token or API key"""
  authorization: Authorization!
  btcPriceList(range: PriceGraphRange!): [PricePoint]
  businessMapMarkers: [MapMarker!]!

  """Returns an estimated conversion rate for the given amount and currency"""
  currencyConversionEstimation(
    """Amount in major unit."""
    amount: Float!

    """Currency of the amount to be converted."""
    currency: DisplayCurrency!
  ): CurrencyConversionEstimation!
  currencyList: [Currency!]!
  globals: Globals
  lnInvoicePaymentStatus(input: LnInvoicePaymentStatusInput!): LnInvoicePaymentStatusPayload! @deprecated(reason: "Deprecated in favor of lnInvoicePaymentStatusByPaymentRequest")
  lnInvoicePaymentStatusByHash(input: LnInvoicePaymentStatusByHashInput!): LnInvoicePaymentStatus!
  lnInvoicePaymentStatusByPaymentRequest(input: LnInvoicePaymentStatusByPaymentRequestInput!): LnInvoicePaymentStatus!
  me: User
  mobileVersions: [MobileVersions]
  onChainTxFee(address: OnChainAddress!, amount: SatAmount!, speed: PayoutSpeed! = FAST, walletId: WalletId!): OnChainTxFee!
  onChainUsdTxFee(address: OnChainAddress!, amount: CentAmount!, speed: PayoutSpeed! = FAST, walletId: WalletId!): OnChainUsdTxFee!
  onChainUsdTxFeeAsBtcDenominated(address: OnChainAddress!, amount: SatAmount!, speed: PayoutSpeed! = FAST, walletId: WalletId!): OnChainUsdTxFee!

  """
  Returns 1 Sat and 1 Usd Cent price for the given currency in minor unit
  """
  realtimePrice(currency: DisplayCurrency = "USD"): RealtimePrice!
  userDefaultWalletId(username: Username!): WalletId! @deprecated(reason: "will be migrated to AccountDefaultWalletId")
  usernameAvailable(username: Username!): Boolean
}

type Quiz {
  """The reward in Satoshis for the quiz question"""
  amount: SatAmount!
  completed: Boolean!
  id: ID!
  notBefore: Timestamp
}

input QuizClaimInput {
  id: ID!
}

type QuizClaimPayload {
  errors: [Error!]!
  quizzes: [Quiz!]!
}

type RealtimePrice {
  btcSatPrice: PriceOfOneSatInMinorUnit!
  denominatorCurrency: DisplayCurrency! @deprecated(reason: "Deprecated in favor of denominatorCurrencyDetails")
  denominatorCurrencyDetails: Currency!
  id: ID!

  """
  Unix timestamp (number of seconds elapsed since January 1, 1970 00:00:00 UTC)
  """
  timestamp: Timestamp!
  usdCentPrice: PriceOfOneUsdCentInMinorUnit!
}

input RealtimePriceInput {
  currency: DisplayCurrency = "USD"
}

type RealtimePricePayload {
  errors: [Error!]!
  realtimePrice: RealtimePrice
}

"""
Non-fractional signed whole numeric value between -(2^53) + 1 and 2^53 - 1
"""
scalar SafeInt

"""(Positive) Satoshi amount"""
scalar SatAmount

type SatAmountPayload {
  amount: SatAmount
  errors: [Error!]!
}

enum Scope {
  READ
  RECEIVE
  WRITE
}

"""(Positive) amount of seconds"""
scalar Seconds

union SettlementVia = SettlementViaIntraLedger | SettlementViaLn | SettlementViaOnChain

type SettlementViaIntraLedger {
  """
  Settlement destination: Could be null if the payee does not have a username
  """
  counterPartyUsername: Username
  counterPartyWalletId: WalletId
  preImage: LnPaymentPreImage
}

type SettlementViaLn {
  paymentSecret: LnPaymentSecret @deprecated(reason: "Shifting property to 'preImage' to improve granularity of the LnPaymentSecret type")
  preImage: LnPaymentPreImage
}

type SettlementViaOnChain {
  arrivalInMempoolEstimatedAt: Timestamp
  transactionHash: OnChainTxHash
  vout: Int
}

"""An amount (of a currency) that can be negative (e.g. in a transaction)"""
scalar SignedAmount

"""
A string amount (of a currency) that can be negative (e.g. in a transaction)
"""
scalar SignedDisplayMajorAmount

type Subscription {
  lnInvoicePaymentStatus(input: LnInvoicePaymentStatusInput!): LnInvoicePaymentStatusPayload! @deprecated(reason: "Deprecated in favor of lnInvoicePaymentStatusByPaymentRequest")
  lnInvoicePaymentStatusByHash(input: LnInvoicePaymentStatusByHashInput!): LnInvoicePaymentStatusPayload!
  lnInvoicePaymentStatusByPaymentRequest(input: LnInvoicePaymentStatusByPaymentRequestInput!): LnInvoicePaymentStatusPayload!
  myUpdates: MyUpdatesPayload!
  price(input: PriceInput!): PricePayload!

  """Returns the price of 1 satoshi"""
  realtimePrice(input: RealtimePriceInput!): RealtimePricePayload!
}

type SuccessPayload {
  errors: [Error!]!
  success: Boolean
}

input SupportChatMessageAddInput {
  message: String!
}

type SupportChatMessageAddPayload {
  errors: [Error!]!
  supportMessage: [SupportMessage]
}

type SupportMessage {
  id: ID!
  message: String!
  role: SupportRole!
  timestamp: Timestamp!
}

enum SupportRole {
  ASSISTANT
  USER
}

"""
Timestamp field, serialized as Unix time (the number of seconds since the Unix epoch)
"""
scalar Timestamp

"""A time-based one-time password"""
scalar TotpCode

"""An id to be passed between set and verify for confirming totp"""
scalar TotpRegistrationId

"""A secret to generate time-based one-time password"""
scalar TotpSecret

"""
Give details about an individual transaction.
Galoy have a smart routing system which is automatically
settling intraledger when both the payer and payee use the same wallet
therefore it's possible the transactions is being initiated onchain
or with lightning but settled intraledger.
"""
type Transaction {
  createdAt: Timestamp!
  direction: TxDirection!
  externalId: TxExternalId
  id: ID!

  """From which protocol the payment has been initiated."""
  initiationVia: InitiationVia!
  memo: Memo

  """Amount of the settlement currency sent or received."""
  settlementAmount: SignedAmount!

  """Wallet currency for transaction."""
  settlementCurrency: WalletCurrency!
  settlementDisplayAmount: SignedDisplayMajorAmount!
  settlementDisplayCurrency: DisplayCurrency!
  settlementDisplayFee: SignedDisplayMajorAmount!
  settlementFee: SignedAmount!

  """Price in WALLETCURRENCY/SETTLEMENTUNIT at time of settlement."""
  settlementPrice: PriceOfOneSettlementMinorUnitInDisplayMinorUnit!

  """To which protocol the payment has settled on."""
  settlementVia: SettlementVia!
  status: TxStatus!
}

"""A connection to a list of items."""
type TransactionConnection {
  """A list of edges."""
  edges: [TransactionEdge!]

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""An edge in a connection."""
type TransactionEdge {
  """A cursor for use in pagination"""
  cursor: String!

  """The item at the end of the edge"""
  node: Transaction!
}

enum TxDirection {
  RECEIVE
  SEND
}

"""
An external reference id that can be optionally added for transactions.
"""
scalar TxExternalId

enum TxNotificationType {
  IntraLedgerPayment
  IntraLedgerReceipt
  LigtningReceipt
  OnchainPayment
  OnchainReceipt
  OnchainReceiptPending
}

enum TxStatus {
  FAILURE
  PENDING
  SUCCESS
}

type UpgradePayload {
  authToken: AuthToken
  errors: [Error!]!
  success: Boolean!
}

"""
A wallet belonging to an account which contains a USD balance and a list of transactions.
"""
type UsdWallet implements Wallet {
  accountId: ID!
  balance: SignedAmount!
  id: ID!
  invoiceByPaymentHash(paymentHash: PaymentHash!): Invoice!

  """A list of all invoices associated with walletIds optionally passed."""
  invoices(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): InvoiceConnection

  """An unconfirmed incoming onchain balance."""
  pendingIncomingBalance: SignedAmount!
  pendingIncomingTransactions: [Transaction!]!
  pendingIncomingTransactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!
  ): [Transaction!]!
  transactionById(transactionId: ID!): Transaction!
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection
  transactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!

    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection
  transactionsByPaymentHash(paymentHash: PaymentHash!): [Transaction!]!
  transactionsByPaymentRequest(paymentRequest: LnPaymentRequest!): [Transaction!]!
  walletCurrency: WalletCurrency!
}

type User {
  """
  Get single contact details.
  Can include the transactions associated with the contact.
  """
  contactByUsername(username: Username!): UserContact! @deprecated(reason: "will be moved to Accounts")

  """
  Get full list of contacts.
  Can include the transactions associated with each contact.
  """
  contacts: [UserContact!]! @deprecated(reason: "will be moved to account")
  createdAt: Timestamp!
  defaultAccount: Account!

  """Email address"""
  email: Email
  id: ID!

  """
  Preferred language for user.
  When value is 'default' the intent is to use preferred language from OS settings.
  """
  language: Language!

  """Phone number with international calling code."""
  phone: Phone
  supportChat: [SupportMessage!]!

  """Whether TOTP is enabled for this user."""
  totpEnabled: Boolean!

  """Optional immutable user friendly identifier."""
  username: Username @deprecated(reason: "will be moved to @Handle in Account and Wallet")
}

type UserContact {
  """
  Alias the user can set for this contact.
  Only the user can see the alias attached to their contact.
  """
  alias: ContactAlias
  id: Username!

  """Paginated list of transactions sent to/from this contact."""
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection
  transactionsCount: Int!

  """Actual identifier of the contact."""
  username: Username!
}

input UserContactUpdateAliasInput {
  alias: ContactAlias!
  username: Username!
}

type UserContactUpdateAliasPayload {
  contact: UserContact
  errors: [Error!]!
}

type UserEmailDeletePayload {
  errors: [Error!]!
  me: User
}

input UserEmailRegistrationInitiateInput {
  email: EmailAddress!
}

type UserEmailRegistrationInitiatePayload {
  emailRegistrationId: EmailRegistrationId
  errors: [Error!]!
  me: User
}

input UserEmailRegistrationValidateInput {
  code: OneTimeAuthCode!
  emailRegistrationId: EmailRegistrationId!
}

type UserEmailRegistrationValidatePayload {
  errors: [Error!]!
  me: User
}

input UserLoginInput {
  code: OneTimeAuthCode!
  phone: Phone!
}

input UserLoginUpgradeInput {
  code: OneTimeAuthCode!
  phone: Phone!
}

input UserLogoutInput {
  deviceToken: String!
}

type UserPhoneDeletePayload {
  errors: [Error!]!
  me: User
}

input UserPhoneRegistrationInitiateInput {
  channel: PhoneCodeChannelType
  phone: Phone!
}

input UserPhoneRegistrationValidateInput {
  code: OneTimeAuthCode!
  phone: Phone!
}

type UserPhoneRegistrationValidatePayload {
  errors: [Error!]!
  me: User
}

type UserTotpDeletePayload {
  errors: [Error!]!
  me: User
}

type UserTotpRegistrationInitiatePayload {
  errors: [Error!]!
  totpRegistrationId: TotpRegistrationId
  totpSecret: TotpSecret
}

input UserTotpRegistrationValidateInput {
  authToken: AuthToken
  totpCode: TotpCode!
  totpRegistrationId: TotpRegistrationId!
}

type UserTotpRegistrationValidatePayload {
  errors: [Error!]!
  me: User
}

union UserUpdate = IntraLedgerUpdate | LnUpdate | OnChainUpdate | Price | RealtimePrice

input UserUpdateLanguageInput {
  language: Language!
}

type UserUpdateLanguagePayload {
  errors: [Error!]!
  user: User
}

input UserUpdateUsernameInput {
  username: Username!
}

type UserUpdateUsernamePayload {
  errors: [Error!]!
  user: User
}

"""Unique identifier of a user"""
scalar Username

"""
A generic wallet which stores value in one of our supported currencies.
"""
interface Wallet {
  accountId: ID!
  balance: SignedAmount!
  id: ID!
  invoiceByPaymentHash(
    """
    The lightning invoice with the matching paymentHash belonging to this wallet.
    """
    paymentHash: PaymentHash!
  ): Invoice!
  invoices(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): InvoiceConnection
  pendingIncomingBalance: SignedAmount!

  """
  Pending incoming OnChain transactions. When transactions
  are confirmed they will receive a new id and be found in the transactions
  list. Transactions are ordered anti-chronologically,
  ie: the newest transaction will be first
  """
  pendingIncomingTransactions: [Transaction!]!

  """
  Pending incoming OnChain transactions. When transactions
  are confirmed they will receive a new id and be found in the transactions
  list. Transactions are ordered anti-chronologically,
  ie: the newest transaction will be first
  """
  pendingIncomingTransactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!
  ): [Transaction!]!
  transactionById(transactionId: ID!): Transaction!

  """
  Transactions are ordered anti-chronologically,
  ie: the newest transaction will be first
  """
  transactions(
    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection

  """
  Transactions are ordered anti-chronologically,
  ie: the newest transaction will be first
  """
  transactionsByAddress(
    """Returns the items that include this address."""
    address: OnChainAddress!

    """Returns the items in the list that come after the specified cursor."""
    after: String

    """Returns the items in the list that come before the specified cursor."""
    before: String

    """Returns the first n items from the list."""
    first: Int

    """Returns the last n items from the list."""
    last: Int
  ): TransactionConnection

  """
  Returns the transactions that include this paymentHash. This should be a list of size one for a received lightning payment. This can be more that one transaction for a sent lightning payment.
  """
  transactionsByPaymentHash(
    """The payment hash of the lightning invoice paid in this transaction."""
    paymentHash: PaymentHash!
  ): [Transaction!]!

  """Returns the transactions that include this paymentRequest."""
  transactionsByPaymentRequest(
    """Lightning invoice paid in this transaction."""
    paymentRequest: LnPaymentRequest!
  ): [Transaction!]!
  walletCurrency: WalletCurrency!
}

enum WalletCurrency {
  BTC
  USD
}

"""Unique identifier of a wallet"""
scalar WalletId
