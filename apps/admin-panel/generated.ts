// this file is autogenerated by codegen
/* eslint-disable */
import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** Unique identifier of an account */
  AccountId: { input: string; output: string; }
  /** A CCA2 country code (ex US, FR, etc) */
  CountryCode: { input: string; output: string; }
  /** Display currency of an account */
  DisplayCurrency: { input: string; output: string; }
  /** Email address */
  EmailAddress: { input: string; output: string; }
  /** Url that will be fetched on events for the account */
  ExternalUrl: { input: string; output: string; }
  Language: { input: string; output: string; }
  LnPaymentPreImage: { input: string; output: string; }
  /** BOLT11 lightning invoice payment request with the amount included */
  LnPaymentRequest: { input: string; output: string; }
  LnPaymentSecret: { input: string; output: string; }
  LnPubkey: { input: string; output: string; }
  /** Text field in a lightning payment transaction */
  Memo: { input: string; output: string; }
  /** An address for an on-chain bitcoin destination */
  OnChainAddress: { input: string; output: string; }
  OnChainTxHash: { input: string; output: string; }
  PaymentHash: { input: string; output: string; }
  /** Phone number which includes country code */
  Phone: { input: string; output: string; }
  /** Non-fractional signed whole numeric value between -(2^53) + 1 and 2^53 - 1 */
  SafeInt: { input: number; output: number; }
  /** (Positive) Satoshi amount */
  SatAmount: { input: number; output: number; }
  /** An amount (of a currency) that can be negative (e.g. in a transaction) */
  SignedAmount: { input: number; output: number; }
  /** A string amount (of a currency) that can be negative (e.g. in a transaction) */
  SignedDisplayMajorAmount: { input: string; output: string; }
  /** Timestamp field, serialized as Unix time (the number of seconds since the Unix epoch) */
  Timestamp: { input: number; output: number; }
  /** An external reference id that can be optionally added for transactions. */
  TxExternalId: { input: string; output: string; }
  /** Unique identifier of a user */
  Username: { input: string; output: string; }
  /** Unique identifier of a wallet */
  WalletId: { input: string; output: string; }
};

export type AccountDetailPayload = {
  readonly __typename: 'AccountDetailPayload';
  readonly accountDetails?: Maybe<AuditedAccount>;
  readonly errors: ReadonlyArray<Error>;
};

export const AccountLevel = {
  One: 'ONE',
  Three: 'THREE',
  Two: 'TWO',
  Zero: 'ZERO'
} as const;

export type AccountLevel = typeof AccountLevel[keyof typeof AccountLevel];
export const AccountStatus = {
  Active: 'ACTIVE',
  Closed: 'CLOSED',
  Invited: 'INVITED',
  Locked: 'LOCKED',
  New: 'NEW',
  Pending: 'PENDING'
} as const;

export type AccountStatus = typeof AccountStatus[keyof typeof AccountStatus];
export type AccountUpdateLevelInput = {
  readonly accountId: Scalars['AccountId']['input'];
  readonly level: AccountLevel;
};

export type AccountUpdateStatusInput = {
  readonly accountId: Scalars['AccountId']['input'];
  readonly comment?: InputMaybe<Scalars['String']['input']>;
  readonly status: AccountStatus;
};

/** Accounts are core to the Galoy architecture. they have users, and own wallets */
export type AuditedAccount = {
  readonly __typename: 'AuditedAccount';
  readonly createdAt: Scalars['Timestamp']['output'];
  readonly id: Scalars['ID']['output'];
  readonly level: AccountLevel;
  readonly merchants: ReadonlyArray<Merchant>;
  readonly owner: AuditedUser;
  readonly status: AccountStatus;
  readonly username?: Maybe<Scalars['Username']['output']>;
  readonly wallets: ReadonlyArray<Wallet>;
};

export type AuditedUser = {
  readonly __typename: 'AuditedUser';
  readonly createdAt: Scalars['Timestamp']['output'];
  /** Email address */
  readonly email?: Maybe<Email>;
  readonly id: Scalars['ID']['output'];
  readonly language: Scalars['Language']['output'];
  readonly phone?: Maybe<Scalars['Phone']['output']>;
};

/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWallet = Wallet & {
  readonly __typename: 'BTCWallet';
  readonly accountId: Scalars['ID']['output'];
  /** A balance stored in BTC. */
  readonly balance: Scalars['SignedAmount']['output'];
  readonly id: Scalars['ID']['output'];
  readonly invoiceByPaymentHash: Invoice;
  /** A list of all invoices associated with walletIds optionally passed. */
  readonly invoices?: Maybe<InvoiceConnection>;
  /** An unconfirmed incoming onchain balance. */
  readonly pendingIncomingBalance: Scalars['SignedAmount']['output'];
  readonly pendingIncomingTransactions: ReadonlyArray<Transaction>;
  readonly pendingIncomingTransactionsByAddress: ReadonlyArray<Transaction>;
  readonly transactionById: Transaction;
  /** A list of BTC transactions associated with this wallet. */
  readonly transactions?: Maybe<TransactionConnection>;
  readonly transactionsByAddress?: Maybe<TransactionConnection>;
  readonly transactionsByPaymentHash: ReadonlyArray<Transaction>;
  readonly transactionsByPaymentRequest: ReadonlyArray<Transaction>;
  readonly walletCurrency: WalletCurrency;
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletInvoiceByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletInvoicesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletPendingIncomingTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletTransactionByIdArgs = {
  transactionId: Scalars['ID']['input'];
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletTransactionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletTransactionsByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A wallet belonging to an account which contains a BTC balance and a list of transactions. */
export type BtcWalletTransactionsByPaymentRequestArgs = {
  paymentRequest: Scalars['LnPaymentRequest']['input'];
};

export type Coordinates = {
  readonly __typename: 'Coordinates';
  readonly latitude: Scalars['Float']['output'];
  readonly longitude: Scalars['Float']['output'];
};

export const DeepLinkAction = {
  SetDefaultAccountModal: 'SET_DEFAULT_ACCOUNT_MODAL',
  SetLnAddressModal: 'SET_LN_ADDRESS_MODAL',
  UpgradeAccountModal: 'UPGRADE_ACCOUNT_MODAL'
} as const;

export type DeepLinkAction = typeof DeepLinkAction[keyof typeof DeepLinkAction];
export const DeepLinkScreen = {
  Chat: 'CHAT',
  Circles: 'CIRCLES',
  Convert: 'CONVERT',
  Earn: 'EARN',
  Home: 'HOME',
  Map: 'MAP',
  People: 'PEOPLE',
  Price: 'PRICE',
  Receive: 'RECEIVE',
  ScanQr: 'SCAN_QR',
  Settings: 'SETTINGS',
  Settings_2Fa: 'SETTINGS_2FA',
  SettingsAccount: 'SETTINGS_ACCOUNT',
  SettingsDefaultAccount: 'SETTINGS_DEFAULT_ACCOUNT',
  SettingsDisplayCurrency: 'SETTINGS_DISPLAY_CURRENCY',
  SettingsEmail: 'SETTINGS_EMAIL',
  SettingsLanguage: 'SETTINGS_LANGUAGE',
  SettingsNotifications: 'SETTINGS_NOTIFICATIONS',
  SettingsSecurity: 'SETTINGS_SECURITY',
  SettingsTheme: 'SETTINGS_THEME',
  SettingsTxLimits: 'SETTINGS_TX_LIMITS'
} as const;

export type DeepLinkScreen = typeof DeepLinkScreen[keyof typeof DeepLinkScreen];
export type Email = {
  readonly __typename: 'Email';
  readonly address?: Maybe<Scalars['EmailAddress']['output']>;
  readonly verified?: Maybe<Scalars['Boolean']['output']>;
};

export type Error = {
  readonly code?: Maybe<Scalars['String']['output']>;
  readonly message: Scalars['String']['output'];
  readonly path?: Maybe<ReadonlyArray<Maybe<Scalars['String']['output']>>>;
};

export type GraphQlApplicationError = Error & {
  readonly __typename: 'GraphQLApplicationError';
  readonly code?: Maybe<Scalars['String']['output']>;
  readonly message: Scalars['String']['output'];
  readonly path?: Maybe<ReadonlyArray<Maybe<Scalars['String']['output']>>>;
};

export type InitiationVia = InitiationViaIntraLedger | InitiationViaLn | InitiationViaOnChain;

export type InitiationViaIntraLedger = {
  readonly __typename: 'InitiationViaIntraLedger';
  readonly counterPartyUsername?: Maybe<Scalars['Username']['output']>;
  readonly counterPartyWalletId?: Maybe<Scalars['WalletId']['output']>;
};

export type InitiationViaLn = {
  readonly __typename: 'InitiationViaLn';
  readonly paymentHash: Scalars['PaymentHash']['output'];
  /** Bolt11 invoice */
  readonly paymentRequest: Scalars['LnPaymentRequest']['output'];
};

export type InitiationViaOnChain = {
  readonly __typename: 'InitiationViaOnChain';
  readonly address: Scalars['OnChainAddress']['output'];
};

/** A lightning invoice. */
export type Invoice = {
  readonly createdAt: Scalars['Timestamp']['output'];
  /** The unique external id set for the invoice. */
  readonly externalId: Scalars['TxExternalId']['output'];
  /** The payment hash of the lightning invoice. */
  readonly paymentHash: Scalars['PaymentHash']['output'];
  /** The bolt11 invoice to be paid. */
  readonly paymentRequest: Scalars['LnPaymentRequest']['output'];
  /** The payment secret of the lightning invoice. This is not the preimage of the payment hash. */
  readonly paymentSecret: Scalars['LnPaymentSecret']['output'];
  /** The payment status of the invoice. */
  readonly paymentStatus: InvoicePaymentStatus;
};

/** A connection to a list of items. */
export type InvoiceConnection = {
  readonly __typename: 'InvoiceConnection';
  /** A list of edges. */
  readonly edges?: Maybe<ReadonlyArray<InvoiceEdge>>;
  /** Information to aid in pagination. */
  readonly pageInfo: PageInfo;
};

/** An edge in a connection. */
export type InvoiceEdge = {
  readonly __typename: 'InvoiceEdge';
  /** A cursor for use in pagination */
  readonly cursor: Scalars['String']['output'];
  /** The item at the end of the edge */
  readonly node: Invoice;
};

export const InvoicePaymentStatus = {
  Expired: 'EXPIRED',
  Paid: 'PAID',
  Pending: 'PENDING'
} as const;

export type InvoicePaymentStatus = typeof InvoicePaymentStatus[keyof typeof InvoicePaymentStatus];
export type LightningInvoice = {
  readonly __typename: 'LightningInvoice';
  readonly confirmedAt?: Maybe<Scalars['Timestamp']['output']>;
  readonly createdAt: Scalars['Timestamp']['output'];
  readonly description: Scalars['String']['output'];
  readonly expiresAt?: Maybe<Scalars['Timestamp']['output']>;
  readonly isSettled: Scalars['Boolean']['output'];
  readonly received: Scalars['SatAmount']['output'];
  readonly request?: Maybe<Scalars['LnPaymentRequest']['output']>;
  readonly secretPreImage: Scalars['LnPaymentPreImage']['output'];
};

export type LightningPayment = {
  readonly __typename: 'LightningPayment';
  readonly amount?: Maybe<Scalars['SatAmount']['output']>;
  readonly confirmedAt?: Maybe<Scalars['Timestamp']['output']>;
  readonly createdAt?: Maybe<Scalars['Timestamp']['output']>;
  readonly destination?: Maybe<Scalars['LnPubkey']['output']>;
  readonly request?: Maybe<Scalars['LnPaymentRequest']['output']>;
  readonly revealedPreImage?: Maybe<Scalars['LnPaymentPreImage']['output']>;
  readonly roundedUpFee?: Maybe<Scalars['SatAmount']['output']>;
  readonly status?: Maybe<LnPaymentStatus>;
};

export const LnPaymentStatus = {
  Failed: 'FAILED',
  Pending: 'PENDING',
  Settled: 'SETTLED'
} as const;

export type LnPaymentStatus = typeof LnPaymentStatus[keyof typeof LnPaymentStatus];
export type LocalizedNotificationContentInput = {
  readonly body: Scalars['String']['input'];
  readonly language: Scalars['Language']['input'];
  readonly title: Scalars['String']['input'];
};

export type MarketingNotificationTriggerInput = {
  readonly icon?: InputMaybe<NotificationIcon>;
  readonly localizedNotificationContents: ReadonlyArray<LocalizedNotificationContentInput>;
  readonly openDeepLink?: InputMaybe<OpenDeepLinkInput>;
  readonly openExternalUrl?: InputMaybe<OpenExternalUrlInput>;
  readonly phoneCountryCodesFilter?: InputMaybe<ReadonlyArray<Scalars['CountryCode']['input']>>;
  readonly shouldAddToBulletin: Scalars['Boolean']['input'];
  readonly shouldAddToHistory: Scalars['Boolean']['input'];
  readonly shouldSendPush: Scalars['Boolean']['input'];
  readonly userIdsFilter?: InputMaybe<ReadonlyArray<Scalars['ID']['input']>>;
};

export type Merchant = {
  readonly __typename: 'Merchant';
  /** GPS coordinates for the merchant that can be used to place the related business on a map */
  readonly coordinates: Coordinates;
  readonly createdAt: Scalars['Timestamp']['output'];
  readonly id: Scalars['ID']['output'];
  readonly title: Scalars['String']['output'];
  /** The username of the merchant */
  readonly username: Scalars['Username']['output'];
  /** Whether the merchant has been validated */
  readonly validated: Scalars['Boolean']['output'];
};

export type MerchantMapDeleteInput = {
  readonly id: Scalars['ID']['input'];
};

export type MerchantMapValidateInput = {
  readonly id: Scalars['ID']['input'];
};

export type MerchantPayload = {
  readonly __typename: 'MerchantPayload';
  readonly errors: ReadonlyArray<Error>;
  readonly merchant?: Maybe<Merchant>;
};

export type Mutation = {
  readonly __typename: 'Mutation';
  readonly accountUpdateLevel: AccountDetailPayload;
  readonly accountUpdateStatus: AccountDetailPayload;
  readonly marketingNotificationTrigger: SuccessPayload;
  readonly merchantMapDelete: MerchantPayload;
  readonly merchantMapValidate: MerchantPayload;
  readonly userUpdateEmail: AccountDetailPayload;
  readonly userUpdatePhone: AccountDetailPayload;
};


export type MutationAccountUpdateLevelArgs = {
  input: AccountUpdateLevelInput;
};


export type MutationAccountUpdateStatusArgs = {
  input: AccountUpdateStatusInput;
};


export type MutationMarketingNotificationTriggerArgs = {
  input: MarketingNotificationTriggerInput;
};


export type MutationMerchantMapDeleteArgs = {
  input: MerchantMapDeleteInput;
};


export type MutationMerchantMapValidateArgs = {
  input: MerchantMapValidateInput;
};


export type MutationUserUpdateEmailArgs = {
  input: UserUpdateEmailInput;
};


export type MutationUserUpdatePhoneArgs = {
  input: UserUpdatePhoneInput;
};

export const NotificationIcon = {
  ArrowLeft: 'ARROW_LEFT',
  ArrowRight: 'ARROW_RIGHT',
  Backspace: 'BACKSPACE',
  Bank: 'BANK',
  Bell: 'BELL',
  Bitcoin: 'BITCOIN',
  Book: 'BOOK',
  BtcBook: 'BTC_BOOK',
  CaretDown: 'CARET_DOWN',
  CaretLeft: 'CARET_LEFT',
  CaretRight: 'CARET_RIGHT',
  CaretUp: 'CARET_UP',
  Check: 'CHECK',
  CheckCircle: 'CHECK_CIRCLE',
  Close: 'CLOSE',
  CloseCrossWithBackground: 'CLOSE_CROSS_WITH_BACKGROUND',
  Coins: 'COINS',
  CopyPaste: 'COPY_PASTE',
  Dollar: 'DOLLAR',
  Eye: 'EYE',
  EyeSlash: 'EYE_SLASH',
  Filter: 'FILTER',
  Globe: 'GLOBE',
  Graph: 'GRAPH',
  Image: 'IMAGE',
  Info: 'INFO',
  Lightning: 'LIGHTNING',
  Link: 'LINK',
  Loading: 'LOADING',
  MagnifyingGlass: 'MAGNIFYING_GLASS',
  Map: 'MAP',
  Menu: 'MENU',
  Note: 'NOTE',
  PaymentError: 'PAYMENT_ERROR',
  PaymentPending: 'PAYMENT_PENDING',
  PaymentSuccess: 'PAYMENT_SUCCESS',
  Pencil: 'PENCIL',
  People: 'PEOPLE',
  QrCode: 'QR_CODE',
  Question: 'QUESTION',
  Rank: 'RANK',
  Receive: 'RECEIVE',
  Refresh: 'REFRESH',
  Send: 'SEND',
  Settings: 'SETTINGS',
  Share: 'SHARE',
  Transfer: 'TRANSFER',
  User: 'USER',
  Video: 'VIDEO',
  Warning: 'WARNING',
  WarningWithBackground: 'WARNING_WITH_BACKGROUND'
} as const;

export type NotificationIcon = typeof NotificationIcon[keyof typeof NotificationIcon];
export type OpenDeepLinkInput = {
  readonly action?: InputMaybe<DeepLinkAction>;
  readonly screen?: InputMaybe<DeepLinkScreen>;
};

export type OpenExternalUrlInput = {
  readonly url: Scalars['ExternalUrl']['input'];
};

/** Information about pagination in a connection. */
export type PageInfo = {
  readonly __typename: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  readonly endCursor?: Maybe<Scalars['String']['output']>;
  /** When paginating forwards, are there more items? */
  readonly hasNextPage: Scalars['Boolean']['output'];
  /** When paginating backwards, are there more items? */
  readonly hasPreviousPage: Scalars['Boolean']['output'];
  /** When paginating backwards, the cursor to continue. */
  readonly startCursor?: Maybe<Scalars['String']['output']>;
};

export type PriceInterface = {
  readonly base: Scalars['SafeInt']['output'];
  /** @deprecated Deprecated due to type renaming */
  readonly currencyUnit: Scalars['String']['output'];
  readonly offset: Scalars['Int']['output'];
};

/** Price of 1 sat or 1 usd cent in base/offset. To calculate, use: `base / 10^offset` */
export type PriceOfOneSettlementMinorUnitInDisplayMinorUnit = PriceInterface & {
  readonly __typename: 'PriceOfOneSettlementMinorUnitInDisplayMinorUnit';
  readonly base: Scalars['SafeInt']['output'];
  /** @deprecated Deprecated due to type renaming */
  readonly currencyUnit: Scalars['String']['output'];
  /** @deprecated Deprecated please use `base / 10^offset` */
  readonly formattedAmount: Scalars['String']['output'];
  readonly offset: Scalars['Int']['output'];
};

export type Query = {
  readonly __typename: 'Query';
  readonly accountDetailsByAccountId: AuditedAccount;
  readonly accountDetailsByEmail: AuditedAccount;
  readonly accountDetailsByUserId: AuditedAccount;
  readonly accountDetailsByUserPhone: AuditedAccount;
  readonly accountDetailsByUsername: AuditedAccount;
  readonly allLevels: ReadonlyArray<AccountLevel>;
  readonly filteredUserCount: Scalars['Int']['output'];
  readonly lightningInvoice: LightningInvoice;
  readonly lightningPayment: LightningPayment;
  readonly merchantsPendingApproval: ReadonlyArray<Merchant>;
  readonly transactionById?: Maybe<Transaction>;
  readonly transactionsByHash?: Maybe<ReadonlyArray<Maybe<Transaction>>>;
  readonly transactionsByPaymentRequest?: Maybe<ReadonlyArray<Maybe<Transaction>>>;
  readonly wallet: Wallet;
};


export type QueryAccountDetailsByAccountIdArgs = {
  accountId: Scalars['ID']['input'];
};


export type QueryAccountDetailsByEmailArgs = {
  email: Scalars['EmailAddress']['input'];
};


export type QueryAccountDetailsByUserIdArgs = {
  userId: Scalars['ID']['input'];
};


export type QueryAccountDetailsByUserPhoneArgs = {
  phone: Scalars['Phone']['input'];
};


export type QueryAccountDetailsByUsernameArgs = {
  username: Scalars['Username']['input'];
};


export type QueryFilteredUserCountArgs = {
  phoneCountryCodesFilter?: InputMaybe<ReadonlyArray<Scalars['CountryCode']['input']>>;
  userIdsFilter?: InputMaybe<ReadonlyArray<Scalars['ID']['input']>>;
};


export type QueryLightningInvoiceArgs = {
  hash: Scalars['PaymentHash']['input'];
};


export type QueryLightningPaymentArgs = {
  hash: Scalars['PaymentHash']['input'];
};


export type QueryTransactionByIdArgs = {
  id: Scalars['ID']['input'];
};


export type QueryTransactionsByHashArgs = {
  hash: Scalars['PaymentHash']['input'];
};


export type QueryTransactionsByPaymentRequestArgs = {
  paymentRequest: Scalars['LnPaymentRequest']['input'];
};


export type QueryWalletArgs = {
  walletId: Scalars['WalletId']['input'];
};

export type SettlementVia = SettlementViaIntraLedger | SettlementViaLn | SettlementViaOnChain;

export type SettlementViaIntraLedger = {
  readonly __typename: 'SettlementViaIntraLedger';
  /** Settlement destination: Could be null if the payee does not have a username */
  readonly counterPartyUsername?: Maybe<Scalars['Username']['output']>;
  readonly counterPartyWalletId?: Maybe<Scalars['WalletId']['output']>;
  readonly preImage?: Maybe<Scalars['LnPaymentPreImage']['output']>;
};

export type SettlementViaLn = {
  readonly __typename: 'SettlementViaLn';
  /** @deprecated Shifting property to 'preImage' to improve granularity of the LnPaymentSecret type */
  readonly paymentSecret?: Maybe<Scalars['LnPaymentSecret']['output']>;
  readonly preImage?: Maybe<Scalars['LnPaymentPreImage']['output']>;
};

export type SettlementViaOnChain = {
  readonly __typename: 'SettlementViaOnChain';
  readonly arrivalInMempoolEstimatedAt?: Maybe<Scalars['Timestamp']['output']>;
  readonly transactionHash?: Maybe<Scalars['OnChainTxHash']['output']>;
  readonly vout?: Maybe<Scalars['Int']['output']>;
};

export type SuccessPayload = {
  readonly __typename: 'SuccessPayload';
  readonly errors: ReadonlyArray<Error>;
  readonly success?: Maybe<Scalars['Boolean']['output']>;
};

/**
 * Give details about an individual transaction.
 * Galoy have a smart routing system which is automatically
 * settling intraledger when both the payer and payee use the same wallet
 * therefore it's possible the transactions is being initiated onchain
 * or with lightning but settled intraledger.
 */
export type Transaction = {
  readonly __typename: 'Transaction';
  readonly createdAt: Scalars['Timestamp']['output'];
  readonly direction: TxDirection;
  readonly externalId?: Maybe<Scalars['TxExternalId']['output']>;
  readonly id: Scalars['ID']['output'];
  /** From which protocol the payment has been initiated. */
  readonly initiationVia: InitiationVia;
  readonly memo?: Maybe<Scalars['Memo']['output']>;
  /** Amount of the settlement currency sent or received. */
  readonly settlementAmount: Scalars['SignedAmount']['output'];
  /** Wallet currency for transaction. */
  readonly settlementCurrency: WalletCurrency;
  readonly settlementDisplayAmount: Scalars['SignedDisplayMajorAmount']['output'];
  readonly settlementDisplayCurrency: Scalars['DisplayCurrency']['output'];
  readonly settlementDisplayFee: Scalars['SignedDisplayMajorAmount']['output'];
  readonly settlementFee: Scalars['SignedAmount']['output'];
  /** Price in WALLETCURRENCY/SETTLEMENTUNIT at time of settlement. */
  readonly settlementPrice: PriceOfOneSettlementMinorUnitInDisplayMinorUnit;
  /** To which protocol the payment has settled on. */
  readonly settlementVia: SettlementVia;
  readonly status: TxStatus;
};

/** A connection to a list of items. */
export type TransactionConnection = {
  readonly __typename: 'TransactionConnection';
  /** A list of edges. */
  readonly edges?: Maybe<ReadonlyArray<TransactionEdge>>;
  /** Information to aid in pagination. */
  readonly pageInfo: PageInfo;
};

/** An edge in a connection. */
export type TransactionEdge = {
  readonly __typename: 'TransactionEdge';
  /** A cursor for use in pagination */
  readonly cursor: Scalars['String']['output'];
  /** The item at the end of the edge */
  readonly node: Transaction;
};

export const TxDirection = {
  Receive: 'RECEIVE',
  Send: 'SEND'
} as const;

export type TxDirection = typeof TxDirection[keyof typeof TxDirection];
export const TxStatus = {
  Failure: 'FAILURE',
  Pending: 'PENDING',
  Success: 'SUCCESS'
} as const;

export type TxStatus = typeof TxStatus[keyof typeof TxStatus];
/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWallet = Wallet & {
  readonly __typename: 'UsdWallet';
  readonly accountId: Scalars['ID']['output'];
  readonly balance: Scalars['SignedAmount']['output'];
  readonly id: Scalars['ID']['output'];
  readonly invoiceByPaymentHash: Invoice;
  /** A list of all invoices associated with walletIds optionally passed. */
  readonly invoices?: Maybe<InvoiceConnection>;
  /** An unconfirmed incoming onchain balance. */
  readonly pendingIncomingBalance: Scalars['SignedAmount']['output'];
  readonly pendingIncomingTransactions: ReadonlyArray<Transaction>;
  readonly pendingIncomingTransactionsByAddress: ReadonlyArray<Transaction>;
  readonly transactionById: Transaction;
  readonly transactions?: Maybe<TransactionConnection>;
  readonly transactionsByAddress?: Maybe<TransactionConnection>;
  readonly transactionsByPaymentHash: ReadonlyArray<Transaction>;
  readonly transactionsByPaymentRequest: ReadonlyArray<Transaction>;
  readonly walletCurrency: WalletCurrency;
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletInvoiceByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletInvoicesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletPendingIncomingTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletTransactionByIdArgs = {
  transactionId: Scalars['ID']['input'];
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletTransactionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletTransactionsByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A wallet belonging to an account which contains a USD balance and a list of transactions. */
export type UsdWalletTransactionsByPaymentRequestArgs = {
  paymentRequest: Scalars['LnPaymentRequest']['input'];
};

export type UserUpdateEmailInput = {
  readonly accountId: Scalars['AccountId']['input'];
  readonly email: Scalars['EmailAddress']['input'];
};

export type UserUpdatePhoneInput = {
  readonly accountId: Scalars['AccountId']['input'];
  readonly phone: Scalars['Phone']['input'];
};

/** A generic wallet which stores value in one of our supported currencies. */
export type Wallet = {
  readonly accountId: Scalars['ID']['output'];
  readonly balance: Scalars['SignedAmount']['output'];
  readonly id: Scalars['ID']['output'];
  readonly invoiceByPaymentHash: Invoice;
  readonly invoices?: Maybe<InvoiceConnection>;
  readonly pendingIncomingBalance: Scalars['SignedAmount']['output'];
  /**
   * Pending incoming OnChain transactions. When transactions
   * are confirmed they will receive a new id and be found in the transactions
   * list. Transactions are ordered anti-chronologically,
   * ie: the newest transaction will be first
   */
  readonly pendingIncomingTransactions: ReadonlyArray<Transaction>;
  /**
   * Pending incoming OnChain transactions. When transactions
   * are confirmed they will receive a new id and be found in the transactions
   * list. Transactions are ordered anti-chronologically,
   * ie: the newest transaction will be first
   */
  readonly pendingIncomingTransactionsByAddress: ReadonlyArray<Transaction>;
  readonly transactionById: Transaction;
  /**
   * Transactions are ordered anti-chronologically,
   * ie: the newest transaction will be first
   */
  readonly transactions?: Maybe<TransactionConnection>;
  /**
   * Transactions are ordered anti-chronologically,
   * ie: the newest transaction will be first
   */
  readonly transactionsByAddress?: Maybe<TransactionConnection>;
  /** Returns the transactions that include this paymentHash. This should be a list of size one for a received lightning payment. This can be more that one transaction for a sent lightning payment. */
  readonly transactionsByPaymentHash: ReadonlyArray<Transaction>;
  /** Returns the transactions that include this paymentRequest. */
  readonly transactionsByPaymentRequest: ReadonlyArray<Transaction>;
  readonly walletCurrency: WalletCurrency;
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletInvoiceByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletInvoicesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletPendingIncomingTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletTransactionByIdArgs = {
  transactionId: Scalars['ID']['input'];
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletTransactionsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletTransactionsByAddressArgs = {
  address: Scalars['OnChainAddress']['input'];
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletTransactionsByPaymentHashArgs = {
  paymentHash: Scalars['PaymentHash']['input'];
};


/** A generic wallet which stores value in one of our supported currencies. */
export type WalletTransactionsByPaymentRequestArgs = {
  paymentRequest: Scalars['LnPaymentRequest']['input'];
};

export const WalletCurrency = {
  Btc: 'BTC',
  Usd: 'USD'
} as const;

export type WalletCurrency = typeof WalletCurrency[keyof typeof WalletCurrency];
export type AccountDetailsByUserPhoneQueryVariables = Exact<{
  phone: Scalars['Phone']['input'];
}>;


export type AccountDetailsByUserPhoneQuery = { readonly __typename: 'Query', readonly accountDetailsByUserPhone: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } };

export type AccountDetailsByAccountIdQueryVariables = Exact<{
  accountId: Scalars['ID']['input'];
}>;


export type AccountDetailsByAccountIdQuery = { readonly __typename: 'Query', readonly accountDetailsByAccountId: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } };

export type AccountDetailsByEmailQueryVariables = Exact<{
  email: Scalars['EmailAddress']['input'];
}>;


export type AccountDetailsByEmailQuery = { readonly __typename: 'Query', readonly accountDetailsByEmail: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } };

export type MerchantMapDeleteMutationVariables = Exact<{
  input: MerchantMapDeleteInput;
}>;


export type MerchantMapDeleteMutation = { readonly __typename: 'Mutation', readonly merchantMapDelete: { readonly __typename: 'MerchantPayload', readonly errors: ReadonlyArray<{ readonly __typename: 'GraphQLApplicationError', readonly message: string }> } };

export type MerchantMapValidateMutationVariables = Exact<{
  input: MerchantMapValidateInput;
}>;


export type MerchantMapValidateMutation = { readonly __typename: 'Mutation', readonly merchantMapValidate: { readonly __typename: 'MerchantPayload', readonly errors: ReadonlyArray<{ readonly __typename: 'GraphQLApplicationError', readonly message: string }> } };

export type AccountUpdateLevelMutationVariables = Exact<{
  input: AccountUpdateLevelInput;
}>;


export type AccountUpdateLevelMutation = { readonly __typename: 'Mutation', readonly accountUpdateLevel: { readonly __typename: 'AccountDetailPayload', readonly errors: ReadonlyArray<{ readonly __typename: 'GraphQLApplicationError', readonly message: string }>, readonly accountDetails?: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } | null } };

export type AccountUpdateStatusMutationVariables = Exact<{
  input: AccountUpdateStatusInput;
}>;


export type AccountUpdateStatusMutation = { readonly __typename: 'Mutation', readonly accountUpdateStatus: { readonly __typename: 'AccountDetailPayload', readonly errors: ReadonlyArray<{ readonly __typename: 'GraphQLApplicationError', readonly message: string }>, readonly accountDetails?: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } | null } };

export type AccountDetailsByUsernameQueryVariables = Exact<{
  username: Scalars['Username']['input'];
}>;


export type AccountDetailsByUsernameQuery = { readonly __typename: 'Query', readonly accountDetailsByUsername: { readonly __typename: 'AuditedAccount', readonly id: string, readonly username?: string | null, readonly level: AccountLevel, readonly status: AccountStatus, readonly createdAt: number, readonly merchants: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }>, readonly owner: { readonly __typename: 'AuditedUser', readonly id: string, readonly language: string, readonly phone?: string | null, readonly createdAt: number, readonly email?: { readonly __typename: 'Email', readonly address?: string | null, readonly verified?: boolean | null } | null }, readonly wallets: ReadonlyArray<{ readonly __typename: 'BTCWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number } | { readonly __typename: 'UsdWallet', readonly id: string, readonly walletCurrency: WalletCurrency, readonly accountId: string, readonly balance: number, readonly pendingIncomingBalance: number }> } };

export type LightningInvoiceQueryVariables = Exact<{
  hash: Scalars['PaymentHash']['input'];
}>;


export type LightningInvoiceQuery = { readonly __typename: 'Query', readonly lightningInvoice: { readonly __typename: 'LightningInvoice', readonly createdAt: number, readonly confirmedAt?: number | null, readonly description: string, readonly expiresAt?: number | null, readonly isSettled: boolean, readonly received: number, readonly request?: string | null, readonly secretPreImage: string } };

export type LightningPaymentQueryVariables = Exact<{
  hash: Scalars['PaymentHash']['input'];
}>;


export type LightningPaymentQuery = { readonly __typename: 'Query', readonly lightningPayment: { readonly __typename: 'LightningPayment', readonly createdAt?: number | null, readonly confirmedAt?: number | null, readonly status?: LnPaymentStatus | null, readonly amount?: number | null, readonly roundedUpFee?: number | null, readonly revealedPreImage?: string | null, readonly request?: string | null, readonly destination?: string | null } };

export type TransactionsByHashQueryVariables = Exact<{
  hash: Scalars['PaymentHash']['input'];
}>;


export type TransactionsByHashQuery = { readonly __typename: 'Query', readonly transactionsByHash?: ReadonlyArray<{ readonly __typename: 'Transaction', readonly id: string, readonly settlementAmount: number, readonly settlementFee: number, readonly direction: TxDirection, readonly status: TxStatus, readonly memo?: string | null, readonly createdAt: number, readonly initiationVia: { readonly __typename: 'InitiationViaIntraLedger', readonly counterPartyWalletId?: string | null, readonly counterPartyUsername?: string | null } | { readonly __typename: 'InitiationViaLn', readonly paymentHash: string } | { readonly __typename: 'InitiationViaOnChain', readonly address: string }, readonly settlementVia: { readonly __typename: 'SettlementViaIntraLedger', readonly counterPartyWalletId?: string | null, readonly counterPartyUsername?: string | null } | { readonly __typename: 'SettlementViaLn', readonly paymentSecret?: string | null } | { readonly __typename: 'SettlementViaOnChain', readonly transactionHash?: string | null }, readonly settlementPrice: { readonly __typename: 'PriceOfOneSettlementMinorUnitInDisplayMinorUnit', readonly base: number, readonly offset: number, readonly currencyUnit: string, readonly formattedAmount: string } } | null> | null };

export type TransactionByIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type TransactionByIdQuery = { readonly __typename: 'Query', readonly transactionById?: { readonly __typename: 'Transaction', readonly id: string, readonly settlementAmount: number, readonly settlementFee: number, readonly direction: TxDirection, readonly status: TxStatus, readonly memo?: string | null, readonly createdAt: number, readonly initiationVia: { readonly __typename: 'InitiationViaIntraLedger', readonly counterPartyWalletId?: string | null, readonly counterPartyUsername?: string | null } | { readonly __typename: 'InitiationViaLn', readonly paymentHash: string } | { readonly __typename: 'InitiationViaOnChain', readonly address: string }, readonly settlementVia: { readonly __typename: 'SettlementViaIntraLedger', readonly counterPartyWalletId?: string | null, readonly counterPartyUsername?: string | null } | { readonly __typename: 'SettlementViaLn', readonly paymentSecret?: string | null } | { readonly __typename: 'SettlementViaOnChain', readonly transactionHash?: string | null }, readonly settlementPrice: { readonly __typename: 'PriceOfOneSettlementMinorUnitInDisplayMinorUnit', readonly base: number, readonly offset: number, readonly currencyUnit: string, readonly formattedAmount: string } } | null };

export type MerchantsPendingApprovalQueryVariables = Exact<{ [key: string]: never; }>;


export type MerchantsPendingApprovalQuery = { readonly __typename: 'Query', readonly merchantsPendingApproval: ReadonlyArray<{ readonly __typename: 'Merchant', readonly id: string, readonly title: string, readonly createdAt: number, readonly validated: boolean, readonly username: string, readonly coordinates: { readonly __typename: 'Coordinates', readonly latitude: number, readonly longitude: number } }> };

export type FilteredUserCountQueryVariables = Exact<{
  phoneCountryCodesFilter?: InputMaybe<ReadonlyArray<Scalars['CountryCode']['input']> | Scalars['CountryCode']['input']>;
  userIdsFilter?: InputMaybe<ReadonlyArray<Scalars['ID']['input']> | Scalars['ID']['input']>;
}>;


export type FilteredUserCountQuery = { readonly __typename: 'Query', readonly filteredUserCount: number };

export type MarketingNotificationTriggerMutationVariables = Exact<{
  input: MarketingNotificationTriggerInput;
}>;


export type MarketingNotificationTriggerMutation = { readonly __typename: 'Mutation', readonly marketingNotificationTrigger: { readonly __typename: 'SuccessPayload', readonly success?: boolean | null, readonly errors: ReadonlyArray<{ readonly __typename: 'GraphQLApplicationError', readonly message: string }> } };


export const AccountDetailsByUserPhoneDocument = gql`
    query accountDetailsByUserPhone($phone: Phone!) {
  accountDetailsByUserPhone(phone: $phone) {
    id
    username
    level
    status
    merchants {
      id
      title
      coordinates {
        latitude
        longitude
      }
      createdAt
      validated
      username
    }
    owner {
      id
      language
      phone
      email {
        address
        verified
      }
      createdAt
    }
    wallets {
      id
      walletCurrency
      accountId
      balance
      pendingIncomingBalance
    }
    createdAt
  }
}
    `;

/**
 * __useAccountDetailsByUserPhoneQuery__
 *
 * To run a query within a React component, call `useAccountDetailsByUserPhoneQuery` and pass it any options that fit your needs.
 * When your component renders, `useAccountDetailsByUserPhoneQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAccountDetailsByUserPhoneQuery({
 *   variables: {
 *      phone: // value for 'phone'
 *   },
 * });
 */
export function useAccountDetailsByUserPhoneQuery(baseOptions: Apollo.QueryHookOptions<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables> & ({ variables: AccountDetailsByUserPhoneQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>(AccountDetailsByUserPhoneDocument, options);
      }
export function useAccountDetailsByUserPhoneLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>(AccountDetailsByUserPhoneDocument, options);
        }
export function useAccountDetailsByUserPhoneSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>(AccountDetailsByUserPhoneDocument, options);
        }
export type AccountDetailsByUserPhoneQueryHookResult = ReturnType<typeof useAccountDetailsByUserPhoneQuery>;
export type AccountDetailsByUserPhoneLazyQueryHookResult = ReturnType<typeof useAccountDetailsByUserPhoneLazyQuery>;
export type AccountDetailsByUserPhoneSuspenseQueryHookResult = ReturnType<typeof useAccountDetailsByUserPhoneSuspenseQuery>;
export type AccountDetailsByUserPhoneQueryResult = Apollo.QueryResult<AccountDetailsByUserPhoneQuery, AccountDetailsByUserPhoneQueryVariables>;
export const AccountDetailsByAccountIdDocument = gql`
    query accountDetailsByAccountId($accountId: ID!) {
  accountDetailsByAccountId(accountId: $accountId) {
    id
    username
    level
    status
    merchants {
      id
      title
      coordinates {
        latitude
        longitude
      }
      createdAt
      validated
      username
    }
    owner {
      id
      language
      phone
      email {
        address
        verified
      }
      createdAt
    }
    wallets {
      id
      walletCurrency
      accountId
      balance
      pendingIncomingBalance
    }
    createdAt
  }
}
    `;

/**
 * __useAccountDetailsByAccountIdQuery__
 *
 * To run a query within a React component, call `useAccountDetailsByAccountIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useAccountDetailsByAccountIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAccountDetailsByAccountIdQuery({
 *   variables: {
 *      accountId: // value for 'accountId'
 *   },
 * });
 */
export function useAccountDetailsByAccountIdQuery(baseOptions: Apollo.QueryHookOptions<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables> & ({ variables: AccountDetailsByAccountIdQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>(AccountDetailsByAccountIdDocument, options);
      }
export function useAccountDetailsByAccountIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>(AccountDetailsByAccountIdDocument, options);
        }
export function useAccountDetailsByAccountIdSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>(AccountDetailsByAccountIdDocument, options);
        }
export type AccountDetailsByAccountIdQueryHookResult = ReturnType<typeof useAccountDetailsByAccountIdQuery>;
export type AccountDetailsByAccountIdLazyQueryHookResult = ReturnType<typeof useAccountDetailsByAccountIdLazyQuery>;
export type AccountDetailsByAccountIdSuspenseQueryHookResult = ReturnType<typeof useAccountDetailsByAccountIdSuspenseQuery>;
export type AccountDetailsByAccountIdQueryResult = Apollo.QueryResult<AccountDetailsByAccountIdQuery, AccountDetailsByAccountIdQueryVariables>;
export const AccountDetailsByEmailDocument = gql`
    query accountDetailsByEmail($email: EmailAddress!) {
  accountDetailsByEmail(email: $email) {
    id
    username
    level
    status
    merchants {
      id
      title
      coordinates {
        latitude
        longitude
      }
      createdAt
      validated
      username
    }
    owner {
      id
      language
      phone
      email {
        address
        verified
      }
      createdAt
    }
    wallets {
      id
      walletCurrency
      accountId
      balance
      pendingIncomingBalance
    }
    createdAt
  }
}
    `;

/**
 * __useAccountDetailsByEmailQuery__
 *
 * To run a query within a React component, call `useAccountDetailsByEmailQuery` and pass it any options that fit your needs.
 * When your component renders, `useAccountDetailsByEmailQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAccountDetailsByEmailQuery({
 *   variables: {
 *      email: // value for 'email'
 *   },
 * });
 */
export function useAccountDetailsByEmailQuery(baseOptions: Apollo.QueryHookOptions<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables> & ({ variables: AccountDetailsByEmailQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>(AccountDetailsByEmailDocument, options);
      }
export function useAccountDetailsByEmailLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>(AccountDetailsByEmailDocument, options);
        }
export function useAccountDetailsByEmailSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>(AccountDetailsByEmailDocument, options);
        }
export type AccountDetailsByEmailQueryHookResult = ReturnType<typeof useAccountDetailsByEmailQuery>;
export type AccountDetailsByEmailLazyQueryHookResult = ReturnType<typeof useAccountDetailsByEmailLazyQuery>;
export type AccountDetailsByEmailSuspenseQueryHookResult = ReturnType<typeof useAccountDetailsByEmailSuspenseQuery>;
export type AccountDetailsByEmailQueryResult = Apollo.QueryResult<AccountDetailsByEmailQuery, AccountDetailsByEmailQueryVariables>;
export const MerchantMapDeleteDocument = gql`
    mutation merchantMapDelete($input: MerchantMapDeleteInput!) {
  merchantMapDelete(input: $input) {
    errors {
      message
    }
  }
}
    `;
export type MerchantMapDeleteMutationFn = Apollo.MutationFunction<MerchantMapDeleteMutation, MerchantMapDeleteMutationVariables>;

/**
 * __useMerchantMapDeleteMutation__
 *
 * To run a mutation, you first call `useMerchantMapDeleteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMerchantMapDeleteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [merchantMapDeleteMutation, { data, loading, error }] = useMerchantMapDeleteMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMerchantMapDeleteMutation(baseOptions?: Apollo.MutationHookOptions<MerchantMapDeleteMutation, MerchantMapDeleteMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MerchantMapDeleteMutation, MerchantMapDeleteMutationVariables>(MerchantMapDeleteDocument, options);
      }
export type MerchantMapDeleteMutationHookResult = ReturnType<typeof useMerchantMapDeleteMutation>;
export type MerchantMapDeleteMutationResult = Apollo.MutationResult<MerchantMapDeleteMutation>;
export type MerchantMapDeleteMutationOptions = Apollo.BaseMutationOptions<MerchantMapDeleteMutation, MerchantMapDeleteMutationVariables>;
export const MerchantMapValidateDocument = gql`
    mutation merchantMapValidate($input: MerchantMapValidateInput!) {
  merchantMapValidate(input: $input) {
    errors {
      message
    }
  }
}
    `;
export type MerchantMapValidateMutationFn = Apollo.MutationFunction<MerchantMapValidateMutation, MerchantMapValidateMutationVariables>;

/**
 * __useMerchantMapValidateMutation__
 *
 * To run a mutation, you first call `useMerchantMapValidateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMerchantMapValidateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [merchantMapValidateMutation, { data, loading, error }] = useMerchantMapValidateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMerchantMapValidateMutation(baseOptions?: Apollo.MutationHookOptions<MerchantMapValidateMutation, MerchantMapValidateMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MerchantMapValidateMutation, MerchantMapValidateMutationVariables>(MerchantMapValidateDocument, options);
      }
export type MerchantMapValidateMutationHookResult = ReturnType<typeof useMerchantMapValidateMutation>;
export type MerchantMapValidateMutationResult = Apollo.MutationResult<MerchantMapValidateMutation>;
export type MerchantMapValidateMutationOptions = Apollo.BaseMutationOptions<MerchantMapValidateMutation, MerchantMapValidateMutationVariables>;
export const AccountUpdateLevelDocument = gql`
    mutation accountUpdateLevel($input: AccountUpdateLevelInput!) {
  accountUpdateLevel(input: $input) {
    errors {
      message
    }
    accountDetails {
      id
      username
      level
      status
      merchants {
        id
        title
        coordinates {
          latitude
          longitude
        }
        createdAt
        validated
        username
      }
      owner {
        id
        language
        phone
        email {
          address
          verified
        }
        createdAt
      }
      wallets {
        id
        walletCurrency
        accountId
        balance
        pendingIncomingBalance
      }
      createdAt
    }
  }
}
    `;
export type AccountUpdateLevelMutationFn = Apollo.MutationFunction<AccountUpdateLevelMutation, AccountUpdateLevelMutationVariables>;

/**
 * __useAccountUpdateLevelMutation__
 *
 * To run a mutation, you first call `useAccountUpdateLevelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAccountUpdateLevelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [accountUpdateLevelMutation, { data, loading, error }] = useAccountUpdateLevelMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAccountUpdateLevelMutation(baseOptions?: Apollo.MutationHookOptions<AccountUpdateLevelMutation, AccountUpdateLevelMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AccountUpdateLevelMutation, AccountUpdateLevelMutationVariables>(AccountUpdateLevelDocument, options);
      }
export type AccountUpdateLevelMutationHookResult = ReturnType<typeof useAccountUpdateLevelMutation>;
export type AccountUpdateLevelMutationResult = Apollo.MutationResult<AccountUpdateLevelMutation>;
export type AccountUpdateLevelMutationOptions = Apollo.BaseMutationOptions<AccountUpdateLevelMutation, AccountUpdateLevelMutationVariables>;
export const AccountUpdateStatusDocument = gql`
    mutation accountUpdateStatus($input: AccountUpdateStatusInput!) {
  accountUpdateStatus(input: $input) {
    errors {
      message
    }
    accountDetails {
      id
      username
      level
      status
      merchants {
        id
        title
        coordinates {
          latitude
          longitude
        }
        createdAt
        validated
        username
      }
      owner {
        id
        language
        phone
        email {
          address
          verified
        }
        createdAt
      }
      wallets {
        id
        walletCurrency
        accountId
        balance
        pendingIncomingBalance
      }
      createdAt
    }
  }
}
    `;
export type AccountUpdateStatusMutationFn = Apollo.MutationFunction<AccountUpdateStatusMutation, AccountUpdateStatusMutationVariables>;

/**
 * __useAccountUpdateStatusMutation__
 *
 * To run a mutation, you first call `useAccountUpdateStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAccountUpdateStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [accountUpdateStatusMutation, { data, loading, error }] = useAccountUpdateStatusMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useAccountUpdateStatusMutation(baseOptions?: Apollo.MutationHookOptions<AccountUpdateStatusMutation, AccountUpdateStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AccountUpdateStatusMutation, AccountUpdateStatusMutationVariables>(AccountUpdateStatusDocument, options);
      }
export type AccountUpdateStatusMutationHookResult = ReturnType<typeof useAccountUpdateStatusMutation>;
export type AccountUpdateStatusMutationResult = Apollo.MutationResult<AccountUpdateStatusMutation>;
export type AccountUpdateStatusMutationOptions = Apollo.BaseMutationOptions<AccountUpdateStatusMutation, AccountUpdateStatusMutationVariables>;
export const AccountDetailsByUsernameDocument = gql`
    query accountDetailsByUsername($username: Username!) {
  accountDetailsByUsername(username: $username) {
    id
    username
    level
    status
    merchants {
      id
      title
      coordinates {
        latitude
        longitude
      }
      createdAt
      validated
      username
    }
    owner {
      id
      language
      phone
      email {
        address
        verified
      }
      createdAt
    }
    wallets {
      id
      walletCurrency
      accountId
      balance
      pendingIncomingBalance
    }
    createdAt
  }
}
    `;

/**
 * __useAccountDetailsByUsernameQuery__
 *
 * To run a query within a React component, call `useAccountDetailsByUsernameQuery` and pass it any options that fit your needs.
 * When your component renders, `useAccountDetailsByUsernameQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useAccountDetailsByUsernameQuery({
 *   variables: {
 *      username: // value for 'username'
 *   },
 * });
 */
export function useAccountDetailsByUsernameQuery(baseOptions: Apollo.QueryHookOptions<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables> & ({ variables: AccountDetailsByUsernameQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>(AccountDetailsByUsernameDocument, options);
      }
export function useAccountDetailsByUsernameLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>(AccountDetailsByUsernameDocument, options);
        }
export function useAccountDetailsByUsernameSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>(AccountDetailsByUsernameDocument, options);
        }
export type AccountDetailsByUsernameQueryHookResult = ReturnType<typeof useAccountDetailsByUsernameQuery>;
export type AccountDetailsByUsernameLazyQueryHookResult = ReturnType<typeof useAccountDetailsByUsernameLazyQuery>;
export type AccountDetailsByUsernameSuspenseQueryHookResult = ReturnType<typeof useAccountDetailsByUsernameSuspenseQuery>;
export type AccountDetailsByUsernameQueryResult = Apollo.QueryResult<AccountDetailsByUsernameQuery, AccountDetailsByUsernameQueryVariables>;
export const LightningInvoiceDocument = gql`
    query lightningInvoice($hash: PaymentHash!) {
  lightningInvoice(hash: $hash) {
    createdAt
    confirmedAt
    description
    expiresAt
    isSettled
    received
    request
    secretPreImage
  }
}
    `;

/**
 * __useLightningInvoiceQuery__
 *
 * To run a query within a React component, call `useLightningInvoiceQuery` and pass it any options that fit your needs.
 * When your component renders, `useLightningInvoiceQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useLightningInvoiceQuery({
 *   variables: {
 *      hash: // value for 'hash'
 *   },
 * });
 */
export function useLightningInvoiceQuery(baseOptions: Apollo.QueryHookOptions<LightningInvoiceQuery, LightningInvoiceQueryVariables> & ({ variables: LightningInvoiceQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<LightningInvoiceQuery, LightningInvoiceQueryVariables>(LightningInvoiceDocument, options);
      }
export function useLightningInvoiceLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<LightningInvoiceQuery, LightningInvoiceQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<LightningInvoiceQuery, LightningInvoiceQueryVariables>(LightningInvoiceDocument, options);
        }
export function useLightningInvoiceSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<LightningInvoiceQuery, LightningInvoiceQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<LightningInvoiceQuery, LightningInvoiceQueryVariables>(LightningInvoiceDocument, options);
        }
export type LightningInvoiceQueryHookResult = ReturnType<typeof useLightningInvoiceQuery>;
export type LightningInvoiceLazyQueryHookResult = ReturnType<typeof useLightningInvoiceLazyQuery>;
export type LightningInvoiceSuspenseQueryHookResult = ReturnType<typeof useLightningInvoiceSuspenseQuery>;
export type LightningInvoiceQueryResult = Apollo.QueryResult<LightningInvoiceQuery, LightningInvoiceQueryVariables>;
export const LightningPaymentDocument = gql`
    query lightningPayment($hash: PaymentHash!) {
  lightningPayment(hash: $hash) {
    createdAt
    confirmedAt
    status
    amount
    roundedUpFee
    revealedPreImage
    request
    destination
  }
}
    `;

/**
 * __useLightningPaymentQuery__
 *
 * To run a query within a React component, call `useLightningPaymentQuery` and pass it any options that fit your needs.
 * When your component renders, `useLightningPaymentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useLightningPaymentQuery({
 *   variables: {
 *      hash: // value for 'hash'
 *   },
 * });
 */
export function useLightningPaymentQuery(baseOptions: Apollo.QueryHookOptions<LightningPaymentQuery, LightningPaymentQueryVariables> & ({ variables: LightningPaymentQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<LightningPaymentQuery, LightningPaymentQueryVariables>(LightningPaymentDocument, options);
      }
export function useLightningPaymentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<LightningPaymentQuery, LightningPaymentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<LightningPaymentQuery, LightningPaymentQueryVariables>(LightningPaymentDocument, options);
        }
export function useLightningPaymentSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<LightningPaymentQuery, LightningPaymentQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<LightningPaymentQuery, LightningPaymentQueryVariables>(LightningPaymentDocument, options);
        }
export type LightningPaymentQueryHookResult = ReturnType<typeof useLightningPaymentQuery>;
export type LightningPaymentLazyQueryHookResult = ReturnType<typeof useLightningPaymentLazyQuery>;
export type LightningPaymentSuspenseQueryHookResult = ReturnType<typeof useLightningPaymentSuspenseQuery>;
export type LightningPaymentQueryResult = Apollo.QueryResult<LightningPaymentQuery, LightningPaymentQueryVariables>;
export const TransactionsByHashDocument = gql`
    query transactionsByHash($hash: PaymentHash!) {
  transactionsByHash(hash: $hash) {
    id
    initiationVia {
      __typename
      ... on InitiationViaIntraLedger {
        counterPartyWalletId
        counterPartyUsername
      }
      ... on InitiationViaLn {
        paymentHash
      }
      ... on InitiationViaOnChain {
        address
      }
    }
    settlementVia {
      __typename
      ... on SettlementViaIntraLedger {
        counterPartyWalletId
        counterPartyUsername
      }
      ... on SettlementViaLn {
        paymentSecret
      }
      ... on SettlementViaOnChain {
        transactionHash
      }
    }
    settlementAmount
    settlementFee
    settlementPrice {
      base
      offset
      currencyUnit
      formattedAmount
    }
    direction
    status
    memo
    createdAt
  }
}
    `;

/**
 * __useTransactionsByHashQuery__
 *
 * To run a query within a React component, call `useTransactionsByHashQuery` and pass it any options that fit your needs.
 * When your component renders, `useTransactionsByHashQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useTransactionsByHashQuery({
 *   variables: {
 *      hash: // value for 'hash'
 *   },
 * });
 */
export function useTransactionsByHashQuery(baseOptions: Apollo.QueryHookOptions<TransactionsByHashQuery, TransactionsByHashQueryVariables> & ({ variables: TransactionsByHashQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<TransactionsByHashQuery, TransactionsByHashQueryVariables>(TransactionsByHashDocument, options);
      }
export function useTransactionsByHashLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<TransactionsByHashQuery, TransactionsByHashQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<TransactionsByHashQuery, TransactionsByHashQueryVariables>(TransactionsByHashDocument, options);
        }
export function useTransactionsByHashSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<TransactionsByHashQuery, TransactionsByHashQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<TransactionsByHashQuery, TransactionsByHashQueryVariables>(TransactionsByHashDocument, options);
        }
export type TransactionsByHashQueryHookResult = ReturnType<typeof useTransactionsByHashQuery>;
export type TransactionsByHashLazyQueryHookResult = ReturnType<typeof useTransactionsByHashLazyQuery>;
export type TransactionsByHashSuspenseQueryHookResult = ReturnType<typeof useTransactionsByHashSuspenseQuery>;
export type TransactionsByHashQueryResult = Apollo.QueryResult<TransactionsByHashQuery, TransactionsByHashQueryVariables>;
export const TransactionByIdDocument = gql`
    query transactionById($id: ID!) {
  transactionById(id: $id) {
    id
    initiationVia {
      __typename
      ... on InitiationViaIntraLedger {
        counterPartyWalletId
        counterPartyUsername
      }
      ... on InitiationViaLn {
        paymentHash
      }
      ... on InitiationViaOnChain {
        address
      }
    }
    settlementVia {
      __typename
      ... on SettlementViaIntraLedger {
        counterPartyWalletId
        counterPartyUsername
      }
      ... on SettlementViaLn {
        paymentSecret
      }
      ... on SettlementViaOnChain {
        transactionHash
      }
    }
    settlementAmount
    settlementFee
    settlementPrice {
      base
      offset
      currencyUnit
      formattedAmount
    }
    direction
    status
    memo
    createdAt
  }
}
    `;

/**
 * __useTransactionByIdQuery__
 *
 * To run a query within a React component, call `useTransactionByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useTransactionByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useTransactionByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useTransactionByIdQuery(baseOptions: Apollo.QueryHookOptions<TransactionByIdQuery, TransactionByIdQueryVariables> & ({ variables: TransactionByIdQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<TransactionByIdQuery, TransactionByIdQueryVariables>(TransactionByIdDocument, options);
      }
export function useTransactionByIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<TransactionByIdQuery, TransactionByIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<TransactionByIdQuery, TransactionByIdQueryVariables>(TransactionByIdDocument, options);
        }
export function useTransactionByIdSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<TransactionByIdQuery, TransactionByIdQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<TransactionByIdQuery, TransactionByIdQueryVariables>(TransactionByIdDocument, options);
        }
export type TransactionByIdQueryHookResult = ReturnType<typeof useTransactionByIdQuery>;
export type TransactionByIdLazyQueryHookResult = ReturnType<typeof useTransactionByIdLazyQuery>;
export type TransactionByIdSuspenseQueryHookResult = ReturnType<typeof useTransactionByIdSuspenseQuery>;
export type TransactionByIdQueryResult = Apollo.QueryResult<TransactionByIdQuery, TransactionByIdQueryVariables>;
export const MerchantsPendingApprovalDocument = gql`
    query merchantsPendingApproval {
  merchantsPendingApproval {
    id
    title
    coordinates {
      latitude
      longitude
    }
    createdAt
    validated
    username
  }
}
    `;

/**
 * __useMerchantsPendingApprovalQuery__
 *
 * To run a query within a React component, call `useMerchantsPendingApprovalQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantsPendingApprovalQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantsPendingApprovalQuery({
 *   variables: {
 *   },
 * });
 */
export function useMerchantsPendingApprovalQuery(baseOptions?: Apollo.QueryHookOptions<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>(MerchantsPendingApprovalDocument, options);
      }
export function useMerchantsPendingApprovalLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>(MerchantsPendingApprovalDocument, options);
        }
export function useMerchantsPendingApprovalSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>(MerchantsPendingApprovalDocument, options);
        }
export type MerchantsPendingApprovalQueryHookResult = ReturnType<typeof useMerchantsPendingApprovalQuery>;
export type MerchantsPendingApprovalLazyQueryHookResult = ReturnType<typeof useMerchantsPendingApprovalLazyQuery>;
export type MerchantsPendingApprovalSuspenseQueryHookResult = ReturnType<typeof useMerchantsPendingApprovalSuspenseQuery>;
export type MerchantsPendingApprovalQueryResult = Apollo.QueryResult<MerchantsPendingApprovalQuery, MerchantsPendingApprovalQueryVariables>;
export const FilteredUserCountDocument = gql`
    query filteredUserCount($phoneCountryCodesFilter: [CountryCode!], $userIdsFilter: [ID!]) {
  filteredUserCount(
    phoneCountryCodesFilter: $phoneCountryCodesFilter
    userIdsFilter: $userIdsFilter
  )
}
    `;

/**
 * __useFilteredUserCountQuery__
 *
 * To run a query within a React component, call `useFilteredUserCountQuery` and pass it any options that fit your needs.
 * When your component renders, `useFilteredUserCountQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFilteredUserCountQuery({
 *   variables: {
 *      phoneCountryCodesFilter: // value for 'phoneCountryCodesFilter'
 *      userIdsFilter: // value for 'userIdsFilter'
 *   },
 * });
 */
export function useFilteredUserCountQuery(baseOptions?: Apollo.QueryHookOptions<FilteredUserCountQuery, FilteredUserCountQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<FilteredUserCountQuery, FilteredUserCountQueryVariables>(FilteredUserCountDocument, options);
      }
export function useFilteredUserCountLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<FilteredUserCountQuery, FilteredUserCountQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<FilteredUserCountQuery, FilteredUserCountQueryVariables>(FilteredUserCountDocument, options);
        }
export function useFilteredUserCountSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<FilteredUserCountQuery, FilteredUserCountQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<FilteredUserCountQuery, FilteredUserCountQueryVariables>(FilteredUserCountDocument, options);
        }
export type FilteredUserCountQueryHookResult = ReturnType<typeof useFilteredUserCountQuery>;
export type FilteredUserCountLazyQueryHookResult = ReturnType<typeof useFilteredUserCountLazyQuery>;
export type FilteredUserCountSuspenseQueryHookResult = ReturnType<typeof useFilteredUserCountSuspenseQuery>;
export type FilteredUserCountQueryResult = Apollo.QueryResult<FilteredUserCountQuery, FilteredUserCountQueryVariables>;
export const MarketingNotificationTriggerDocument = gql`
    mutation marketingNotificationTrigger($input: MarketingNotificationTriggerInput!) {
  marketingNotificationTrigger(input: $input) {
    errors {
      message
    }
    success
  }
}
    `;
export type MarketingNotificationTriggerMutationFn = Apollo.MutationFunction<MarketingNotificationTriggerMutation, MarketingNotificationTriggerMutationVariables>;

/**
 * __useMarketingNotificationTriggerMutation__
 *
 * To run a mutation, you first call `useMarketingNotificationTriggerMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMarketingNotificationTriggerMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [marketingNotificationTriggerMutation, { data, loading, error }] = useMarketingNotificationTriggerMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMarketingNotificationTriggerMutation(baseOptions?: Apollo.MutationHookOptions<MarketingNotificationTriggerMutation, MarketingNotificationTriggerMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MarketingNotificationTriggerMutation, MarketingNotificationTriggerMutationVariables>(MarketingNotificationTriggerDocument, options);
      }
export type MarketingNotificationTriggerMutationHookResult = ReturnType<typeof useMarketingNotificationTriggerMutation>;
export type MarketingNotificationTriggerMutationResult = Apollo.MutationResult<MarketingNotificationTriggerMutation>;
export type MarketingNotificationTriggerMutationOptions = Apollo.BaseMutationOptions<MarketingNotificationTriggerMutation, MarketingNotificationTriggerMutationVariables>;