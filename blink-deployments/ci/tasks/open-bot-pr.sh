#!/bin/bash

set -eu

pushd trigger

IGNORE="--ignore-queue"

trigger=$(cat .git/cepler_trigger)
env=$(cat .git/cepler_environment)

cepler ${IGNORE} -g ../gates-branch/cepler-gates/${DEPLOYMENT}.yml \
  check -e ${env} > ../cepler-check-out 2>&1

cat <<EOF >> ../body.md
# Propagate '${DEPLOYMENT}' changes to '${env}'

A diff of all changes that will be rolled out: $(cat ../repo/deployments-diff-url)

EOF

for file in ../repo/src-changes/*; do
    # Extract the base name of the file to get the app name
    app_name=$(basename $file)

    # Convert hyphens to spaces and make the first letter of each word uppercase for the title
    formatted_app_name=$(echo $app_name |  sed 's/-/ /g' | sed 's/\b\(.\)/\u\1/g')

    if [[ -f $file ]]; then
        cat <<EOF >> ../body.md
## Changes to $formatted_app_name source code
$(cat $file)

EOF
    fi
done

cat <<EOF >> ../body.md
## Verify locally
To check locally run:
$(echo '```')
$ git fetch origin
$ cepler ${IGNORE} -c ${CEPLER_CONF} -g ${CEPLER_GATES} --gates-branch ${BOT_BRANCH} \\
  check -e ${env}
$(cat ../cepler-check-out)
$ cepler ${IGNORE} -c ${CEPLER_CONF} -g ${CEPLER_GATES} --gates-branch ${BOT_BRANCH} \\
  prepare -e ${env}
$(echo '```')
EOF

export GH_TOKEN="$(ghtoken generate -b "${GH_APP_PRIVATE_KEY}" -i "${GH_APP_ID}" | jq -r '.token')"

is_pr_blocked=$(
  gh pr view "${BOT_BRANCH}" --json labels,state |\
  jq -r 'if .state == "OPEN" and ([.labels[] | .name == "do-not-merge"] | any) then "true" else "false" end'
)

gh pr close ${BOT_BRANCH} || true
gh pr create \
  --title bump-${env}-${DEPLOYMENT}-${trigger:0:7} \
  --body-file ../body.md \
  --base ${BRANCH} \
  --head ${BOT_BRANCH} \
  --label blinkbitcoinbot \
  --label ${env##gcp-} \
  --label ${DEPLOYMENT} \
  $(if [[ "$is_pr_blocked" == "true" ]]; then echo "--label do-not-merge"; fi)
