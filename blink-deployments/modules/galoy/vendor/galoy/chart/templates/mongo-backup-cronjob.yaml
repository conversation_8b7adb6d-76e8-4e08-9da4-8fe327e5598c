apiVersion: batch/v1
kind: CronJob

metadata:
  name: {{ template "galoy.mongoBackupCron.jobname" . }}
  labels:
    app: {{ template "galoy.mongoBackupCron.jobname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm

spec:
  schedule: "0 * * * *" # Fire once every hour

  jobTemplate:
    spec:
      activeDeadlineSeconds: 840

      template:
        metadata:
          labels:
            app: {{ template "galoy.mongoBackupCron.jobname" . }}
        spec:
          restartPolicy: OnFailure

          containers:
          - name: {{ template "galoy.mongoBackupCron.jobname" . }}
            image: "{{ .Values.galoy.images.mongoBackup.repository }}@{{ .Values.galoy.images.mongoBackup.digest }}"
            command:
            - "/bin/sh"
            - "-c"
            - "/var/backup.sh"
            resources:
              {{ toYaml .Values.galoy.mongoBackupCron.resources | nindent 14 }}
            env:
            - name: NETWORK
              value: {{ .Values.galoy.network }}

{{ include "galoy.mongodb.env" . | indent 12 }}
            - name: MONGODB_PORT
              value: "27017"
            - name: MONGODB_DB
              value: "galoy"

            {{ if .Values.galoy.trigger.backups.gcs.enabled }}
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: "/var/secret/cloud.google.com/gcs-sa-key.json"
            {{ end }}

            {{ if .Values.galoy.trigger.backups.s3.enabled }}
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.galoy.trigger.backups.s3.accessKeyExistingSecret.name | quote }}
                  key: {{ .Values.galoy.trigger.backups.s3.accessKeyExistingSecret.key | quote }}
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.galoy.trigger.backups.s3.secretKeyExistingSecret.name | quote }}
                  key: {{ .Values.galoy.trigger.backups.s3.secretKeyExistingSecret.key | quote }}
            - name: AWS_REGION
              value: {{ .Values.galoy.trigger.backups.s3.region | quote }}
            {{ end }}

            volumeMounts:
            - name: mongo-backup-configmap
              mountPath: "/var/backup.sh"
              subPath: backup.sh

            {{ if .Values.galoy.trigger.backups.gcs.enabled }}
            - name: service-account
              mountPath: "/var/secret/cloud.google.com"
            {{ end }}

          volumes:
          - name: mongo-backup-configmap
            configMap:
              name: mongo-backup-configmap
              defaultMode: 484

          {{ if .Values.galoy.trigger.backups.gcs.enabled }}
          - name: service-account
            secret:
              secretName: {{ .Values.galoy.trigger.backups.gcs.serviceAccountExistingSecret.name | quote }}
              items:
              - key: {{ .Values.galoy.trigger.backups.gcs.serviceAccountExistingSecret.key | quote }}
                path: "gcs-sa-key.json"
          {{ end }}
