{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT created_at, id FROM bria_profiles WHERE (COALESCE((created_at, id) < ($3, $2), $2 IS NULL)) ORDER BY created_at DESC, id DESC LIMIT $1) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.created_at desc, i.id desc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, false, false]}, "hash": "b1287f6ecc702a96f4836b30b71e0029f8f09034a5a6b7c79af6764cb271de75"}