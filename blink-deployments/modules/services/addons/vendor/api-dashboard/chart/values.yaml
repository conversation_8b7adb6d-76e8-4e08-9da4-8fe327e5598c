secrets:
  create: true
  nextAuthSecret: ""
  clientSecret: ""
apiDashboard:
  hydraPublic: "http://galoy-hydra-public.galoy-dev-galoy.svc.cluster.local:4444"
  coreUrl: "http://galoy-oathkeeper-proxy.galoy-dev-galoy.svc.cluster.local:4455/graphql"
  nextAuthUrl: ""
  clientId: ""
  otelExporterOtlpEndpoint: http://localhost:4318
  tracingServiceName: "dashboard"
image:
  repository: us.gcr.io/galoy-org/galoy-dashboard
  digest: "sha256:a4464745554f68add067bcc8a164aa8f066947e59ced5b17b3cad5f218cf98e8" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=62cd3f3;app=dashboard;monorepo_subdir=apps/dashboard;
ingress:
  enabled: false
service:
  port: 3000
  type: ClusterIP
resources: {}
