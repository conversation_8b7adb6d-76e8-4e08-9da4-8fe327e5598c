#@ load("@ytt:data", "data")

#@ galoy_api_image_digest = "sha256@c1d579d8dcd0400b4c3907188aab60d1991ea33c196182be23fc3586c33e02f1"
#@ galoy_trigger_image_digest = "sha256@4dd63c5b715490b01e2f7c64388ab059a29526d88adc999e9668eea2cdf7ac7d"
#@ mongodb_migrate_image_digest = "sha256@cf788eea9882fc5d4e8c0da658bed8b416514dcc3c5aefc5e44788c45e4b90dd"
#@ galoy_notifications_image_digest = "sha256@da8253b3852782d228d3439185121ffc278bd1a9c0d2e6458ca8fc9db9c75b06"

#@ core_env = [
#@   "HELMREVISION=dev",
#@   "NETWORK=regtest",
#@   "OATHKEEPER_DECISION_ENDPOINT=http://oathkeeper:4456",
#@   "TWILIO_ACCOUNT_SID=AC_twilio_id",
#@   "TWILIO_AUTH_TOKEN=AC_twilio_auth_token",
#@   "TWILIO_VERIFY_SERVICE_ID=VA_twilio_service",
#@   "KRATOS_PG_CON=***************************************/default?sslmode=disable",
#@   "KRATOS_PUBLIC_API=http://kratos:4433",
#@   "KRATOS_ADMIN_API=http://kratos:4434",
#@   "KRATOS_MASTER_USER_PASSWORD=passwordHardtoFindWithNumber123",
#@   "KRATOS_CALLBACK_API_KEY=The-Value-of-My-Key",
#@   "PRICE_HOST=price",
#@   "PRICE_HISTORY_HOST=price-history",
#@   "PRICE_SERVER_HOST=stablesats",
#@   "BRIA_HOST=bria",
#@   "BRIA_API_KEY=bria_dev_000000000000000000000",
#@   "NOTIFICATIONS_HOST=notifications",
#@   "MONGODB_CON=mongodb://mongodb:27017/galoy",
#@   "REDIS_MASTER_NAME=mymaster",
#@   "REDIS_PASSWORD=",
#@   "REDIS_0_DNS=redis",
#@   "REDIS_0_PORT=6379",
#@   "REDIS_TYPE=standalone",
#@   "UNSECURE_IP_FROM_REQUEST_OBJECT=true",
#@   "UNSECURE_DEFAULT_LOGIN_CODE=000000",
#@   "GEETEST_ID=geetest_id",
#@   "GEETEST_KEY=geetest_key",
#@   "LND1_TLS=LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNZVENDQWdlZ0F3SUJBZ0lSQU9zZzdYWFR4cnVZYlhkeTY2d3RuN1F3Q2dZSUtvWkl6ajBFQXdJd09ERWYKTUIwR0ExVUVDaE1XYkc1a0lHRjFkRzluWlc1bGNtRjBaV1FnWTJWeWRERVZNQk1HQTFVRUF4TU1PRFl4T1RneApNak5tT0Roak1CNFhEVEl6TURFeE9USXdOREUxTTFvWERUTTBNRGN5TVRJd05ERTFNMW93T0RFZk1CMEdBMVVFCkNoTVdiRzVrSUdGMWRHOW5aVzVsY21GMFpXUWdZMlZ5ZERFVk1CTUdBMVVFQXhNTU9EWXhPVGd4TWpObU9EaGoKTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFM1lieUlKWU1Vcm8zZkl0UFFucysxZ2lpTXI5NQpJUXRmclFDQ2JhOWVtcjI4TENmbk1vYy9VQVFwUlg3QVlvVFRneUdiMFBuZGNUODF5ZVgvYTlPa0RLT0I4VENCCjdqQU9CZ05WSFE4QkFmOEVCQU1DQXFRd0V3WURWUjBsQkF3d0NnWUlLd1lCQlFVSEF3RXdEd1lEVlIwVEFRSC8KQkFVd0F3RUIvekFkQmdOVkhRNEVGZ1FVL1AxRHpJUkRzTEhHMU10d3NrZE5nZ0lub1Mwd2daWUdBMVVkRVFTQgpqakNCaTRJTU9EWXhPVGd4TWpObU9EaGpnZ2xzYjJOaGJHaHZjM1NDRFd4dVpDMXZkWFJ6YVdSbExUR0NEV3h1ClpDMXZkWFJ6YVdSbExUS0NEV3h1WkMxdmRYUnphV1JsTFRPQ0JHeHVaREdDQkd4dVpES0NCSFZ1YVhpQ0NuVnUKYVhod1lXTnJaWFNDQjJKMVptTnZibTZIQkg4QUFBR0hFQUFBQUFBQUFBQUFBQUFBQUFBQUFBR0hCS3dUQUJBdwpDZ1lJS29aSXpqMEVBd0lEU0FBd1JRSWhBSU5DNlJWQ3d6SzFYRnFxeVNLY0Y4QzQ5ZFlSOThjemdLNVdkcmNOCkxYYWlBaUJHYmtWeGhaeHdDaDVLQ1o1Z2M1Q2FsQ0RvaGNxVkdiaHNya0hHTFhpdHN3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=",
#@   "LND1_MACAROON=AgEDbG5kAvgBAwoQB1FdhGa9xoewc1LEXmnURRIBMBoWCgdhZGRyZXNzEgRyZWFkEgV3cml0ZRoTCgRpbmZvEgRyZWFkEgV3cml0ZRoXCghpbnZvaWNlcxIEcmVhZBIFd3JpdGUaIQoIbWFjYXJvb24SCGdlbmVyYXRlEgRyZWFkEgV3cml0ZRoWCgdtZXNzYWdlEgRyZWFkEgV3cml0ZRoXCghvZmZjaGFpbhIEcmVhZBIFd3JpdGUaFgoHb25jaGFpbhIEcmVhZBIFd3JpdGUaFAoFcGVlcnMSBHJlYWQSBXdyaXRlGhgKBnNpZ25lchIIZ2VuZXJhdGUSBHJlYWQAAAYgqHDdwGCqx0aQL1/Z3uUfzCpeBhfapGf9s/AZPOVwf6s=",
#@    "LND1_PUBKEY=03ca1907342d5d37744cb7038375e1867c24a87564c293157c95b2a9d38dcfb4c2",
#@   "LND1_DNS=lnd1",
#@   "LND1_RPCPORT=10009",
#@   "LND1_NAME=lnd1",
#@   "LND1_TYPE=offchain,onchain",
#@   "SVIX_SECRET=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE2OTE2NzIwMTQsImV4cCI6MjAwNzAzMjAxNCwibmJmIjoxNjkxNjcyMDE0LCJpc3MiOiJzdml4LXNlcnZlciIsInN1YiI6Im9yZ18yM3JiOFlkR3FNVDBxSXpwZ0d3ZFhmSGlyTXUifQ.b9s0aWSisNdUNki4edabBEToLNSwjC9-AiJQr4J3y4E",
#@   "SVIX_ENDPOINT=http://svix:8071",
#@   "EXPORTER_PORT=3003",
#@   "OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-agent:4318"]

version: #@ data.values.version
services:
  quickstart-test:
    image: busybox
    depends_on:
      - apollo-router
      - oathkeeper
      - kratos
      - galoy
      - trigger
      - redis
      - mongodb
      - mongodb-migrate
      - price
      - bitcoind
      - bitcoind-signer
      - lnd1
      - lnd-outside-1
      - bria
      - fulcrum
      - stablesats
      - svix
      - hydra
    restart: on-failure:10

  #! Proxies
  apollo-router: #@ data.values.services["apollo-router"]
  oathkeeper: #@ data.values.services["oathkeeper"]

  #! Authentication
  kratos: #@ data.values.services["kratos"]
  kratos-pg: #@ data.values.services["kratos-pg"]

  hydra: #@ data.values.services["hydra"]
  hydra-migrate: #@ data.values.services["hydra-migrate"]
  hydra-pg: #@ data.values.services["hydra-pg"]

  galoy:
#@ if galoy_api_image_digest == "local":
    build:
      context: ..
      dockerfile: core/api/Dockerfile
#@ elif galoy_api_image_digest.startswith("sha256@"):
    image: #@ "us.gcr.io/galoy-org/galoy-api@" + galoy_api_image_digest.replace("@",":")
#@ else:
    image: #@ "us.gcr.io/galoy-org/galoy-api:" + galoy_api_image_digest
#@ end
    environment: #@ core_env
    depends_on:
    - apollo-router
    - oathkeeper
    - kratos
    - lnd1
    - lnd-outside-1
    - bria
    - mongodb
    - redis
    - stablesats
    - notifications
    - price
    - price-history
    - svix
    networks:
      default:
        aliases:
        - bats-tests
  trigger:
#@ if galoy_trigger_image_digest == "local":
    build:
      context: ..
      dockerfile: core/api-trigger/Dockerfile
#@ elif galoy_trigger_image_digest.startswith("sha256@"):
    image: #@ "us.gcr.io/galoy-org/galoy-api-trigger@" + galoy_trigger_image_digest.replace("@",":")
#@ else:
    image: #@ "us.gcr.io/galoy-org/galoy-api-trigger:" + galoy_trigger_image_digest
#@ end
    environment: #@ core_env
    depends_on:
    - galoy

  notifications:
#@ if galoy_notifications_image_digest == "local":
    build:
      context: ..
      dockerfile: core/notifications/Dockerfile
#@ elif galoy_notifications_image_digest.startswith("sha256@"):
    image: #@ "us.gcr.io/galoy-org/galoy-notifications@" + galoy_notifications_image_digest.replace("@",":")
#@ else:
    image: #@ "us.gcr.io/galoy-org/galoy-notifications:" + galoy_notifications_image_digest
#@ end
    environment:
    - PG_CON=**********************************************/pg
    - PG_READ_CON=**********************************************/pg
    - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-agent:4318
    - NOTIFICATIONS_CONFIG=/config/notifications/notifications.yml
    ports:
    - 6685:6685
    volumes:
    - ${HOST_PROJECT_PATH:-.}/config/notifications/notifications.yml:/config/notifications/notifications.yml
    - ${HOST_PROJECT_PATH:-.}/config/notifications/fake_service_account.json:/app/notifications/config/notifications/fake_service_account.json
    depends_on:
    - notifications-pg
  notifications-pg: #@ data.values.services["notifications-pg"]

  #! Database
  redis: #@ data.values.services["redis"]
  mongodb: #@ data.values.services["mongodb"]
  mongodb-migrate:
#@ if mongodb_migrate_image_digest == "local":
    build:
      context: ..
      dockerfile: core/api/Dockerfile-migrate
#@ elif mongodb_migrate_image_digest.startswith("sha256@"):
    image: #@ "us.gcr.io/galoy-org/galoy-api-migrate@" + mongodb_migrate_image_digest.replace("@",":")
#@ else:
    image: #@ "us.gcr.io/galoy-org/galoy-api-migrate:" + mongodb_migrate_image_digest
#@ end
    depends_on:
    - mongodb
    environment:
    - MONGODB_ADDRESS=mongodb

  #! Price
  price: #@ data.values.services["price"]
  price-history: #@ data.values.services["price-history"]
  price-history-migrate: #@ data.values.services["price-history-migrate"]
  price-history-pg: #@ data.values.services["price-history-pg"]

  #! Bitcoin stack
  bitcoind: #@ data.values.services["bitcoind"]
  bitcoind-signer: #@ data.values.services["bitcoind-signer"]
  lnd1: #@ data.values.services["lnd1"]
  lnd-outside-1: #@ data.values.services["lnd-outside-1"]
  bria: #@ data.values.services["bria"]
  bria-pg: #@ data.values.services["bria-pg"]
  fulcrum: #@ data.values.services["fulcrum"]

  #! Stablesats
  stablesats: #@ data.values.services["stablesats"]

  #! Otel
  otel-agent: #@ data.values.services["otel-agent"]

  #! Svix / callbacks
  svix: #@ data.values.services["svix"]
  svix-pg: #@ data.values.services["svix-pg"]

#@ load("@ytt:overlay", "overlay")
#@overlay/match by=overlay.all
---
services:
  #@overlay/match by=overlay.all, expects="1+"
  _:
    #@overlay/match when=1
    #@overlay/remove
    extra_hosts: _
    #@overlay/match when=1
    volumes:
    #@overlay/match by=overlay.all, expects="0+"
    #@overlay/replace via=lambda left, right: str(left).replace("HOST_PROJECT_PATH:-.}", "HOST_PROJECT_PATH:-.}/${GALOY_QUICKSTART_PATH:-vendor/galoy-quickstart}/dev")
    - REPLACED

#@overlay/match by=overlay.all
---
services:
  svix:
    #@overlay/match missing_ok=True
    extra_hosts:
      - "dockerhost-alias:host-gateway"
