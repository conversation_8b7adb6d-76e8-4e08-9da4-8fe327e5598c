apiVersion: vendir.k14s.io/v1alpha1
kind: Config

# Relative to ci/
directories:
- path: ../.github/workflows/vendor
  contents:
  - path: . # Copy this folder out to ..
    git:
      url: https://github.com/GaloyMoney/galoy-concourse-shared.git
      ref: 360dcde99986a3af15a1727b3417602a0bbfdde3
    includePaths:
    - shared/actions/*
    excludePaths:
    - shared/actions/nodejs-*
    newRootPath: shared/actions

- path: ./vendor
  contents:
  - path: .
    git:
      url: https://github.com/GaloyMoney/galoy-concourse-shared.git
      ref: 360dcde99986a3af15a1727b3417602a0bbfdde3
    includePaths:
    - shared/ci/**/*
    excludePaths:
    - shared/ci/**/nodejs-*
    newRootPath: shared/ci
