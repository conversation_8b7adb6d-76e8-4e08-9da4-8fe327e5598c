---
version: 749
current:
  head_commit: 735c7330abd854a4b63a7eee45697842e199a6a1
  propagated_head: 67ee0820b09ad410cf414370ca30eb22e618a4e8
  files:
    "{gcp-galoy-staging}/modules/galoy/emails/recovery.tmpl":
      file_hash: 1fca2a2fcd36235550f26d5b05395845458f80ff
      from_commit: fa76a5c2fbc4693d0591df6d57783beb668e1189
      message: "chore: refactor kratos email template"
    "{gcp-galoy-staging}/modules/galoy/emails/recovery_html.tmpl":
      file_hash: 1fca2a2fcd36235550f26d5b05395845458f80ff
      from_commit: fa76a5c2fbc4693d0591df6d57783beb668e1189
      message: "chore: refactor kratos email template"
    "{gcp-galoy-staging}/modules/galoy/emails/verification.tmpl":
      file_hash: 70c6b2f686a3d7106432671fc51a3d2728b35460
      from_commit: ec41ffce45baaf563d7ca80875fd62bfc315f204
      message: "chore: move kratos to galoy (#2409)"
    "{gcp-galoy-staging}/modules/galoy/emails/verification_html.tmpl":
      file_hash: 5daf32219a8fae7e78612d1c6552c733d3e15cbb
      from_commit: ec41ffce45baaf563d7ca80875fd62bfc315f204
      message: "chore: move kratos to galoy (#2409)"
    "{gcp-galoy-staging}/modules/galoy/galoy-sensitive-values.yml.tmpl":
      file_hash: 9045c204e2681008a6a69467486d6c6e650efb4d
      from_commit: 67ee0820b09ad410cf414370ca30eb22e618a4e8
      message: "chore: update ARS and BOB fx sources (#8069)"
    "{gcp-galoy-staging}/modules/galoy/galoy-values.yml.tmpl":
      file_hash: 89600932128d5eae4eb55f5c903e093313b0e33f
      from_commit: 419956189ea1cb379fae931f002c7080ee18b608
      message: "chore: skip probing for ACINQ node (#8054)"
    "{gcp-galoy-staging}/modules/galoy/hydra.tf":
      file_hash: 673b01fe62c52f91d951a1ffda0762cc0a55493e
      from_commit: 2bdfbc48902b6a07356706c824dbfd1cfe1a41cf
      message: "fix: pg references in kratos and hydra"
    "{gcp-galoy-staging}/modules/galoy/identity-schemas/email_no_password_v0":
      file_hash: 6b72f9c8b6932d559e2631aec894c464e41bbc38
      from_commit: 22359b7a52bd4c0616aa537945e0d0208c8f1fd6
      message: "feat: email config for staging (#4284)"
    "{gcp-galoy-staging}/modules/galoy/identity-schemas/phone_email_no_password_v0":
      file_hash: 067319a889268b5737f144be624915410d672fa5
      from_commit: 0745986c742ecfffb83ca849b3b28d13d3a405ff
      message: "fix: kratos schema file name (#4303)"
    "{gcp-galoy-staging}/modules/galoy/identity-schemas/phone_no_password_v0":
      file_hash: 516eb349e087e814d8eec3b6f61ee4dd67cbdac5
      from_commit: 48d354d9c374d5a64d43ae4d7629edb98f088e76
      message: "feat: device account config v3 for staging (#4030)"
    "{gcp-galoy-staging}/modules/galoy/identity-schemas/username_password_deviceid_v0":
      file_hash: 03176dbb06e93ca7298870414ef13b8aca78c11e
      from_commit: 48d354d9c374d5a64d43ae4d7629edb98f088e76
      message: "feat: device account config v3 for staging (#4030)"
    "{gcp-galoy-staging}/modules/galoy/kratos.tf":
      file_hash: 359342b032eae38d1bdf56c25612f984b4f7024b
      from_commit: e8e00bc1ebc502d2d2d5ae1e7f636c8de5846c4e
      message: "fix: revert kratos pg name change"
    "{gcp-galoy-staging}/modules/galoy/main.tf":
      file_hash: b39a9694e4f664e1f8661c65bd335b73ed24c99c
      from_commit: 16dbe7b22dcd0e7e592c34e02d8454a7633e59e1
      message: "fix: remove twilio phone number resource (#7951)"
    "{gcp-galoy-staging}/modules/galoy/oathkeeper.tf":
      file_hash: 7aaf5ed26812c7e60203d33cecf7aa22ad20054c
      from_commit: ec41ffce45baaf563d7ca80875fd62bfc315f204
      message: "chore: move kratos to galoy (#2409)"
    "{gcp-galoy-staging}/modules/galoy/supergraph/supergraph-config.yaml":
      file_hash: d8d40ede4966185e5a2a5ab0579c8034803290fa
      from_commit: 89236aaf2d169925a35d107d0ff5e532e94281fd
      message: "chore: add notifications to supergraph"
    "{gcp-galoy-staging}/modules/galoy/supergraph/vendor/supergraph.graphql":
      file_hash: 3a25a4e8ef4b22059c9cb7fa277abce6c643d705
      from_commit: 5e86b35c1c3ce041e3a79b71885b15fc8c0de56e
      message: "chore: bump galoy-chart to '821bd84547001ea107112d78ff237f00ee0a2a0a'"
    "{gcp-galoy-staging}/modules/galoy/variables.tf":
      file_hash: b276eb8c5103ada602926c5bf405e60a8f7c133e
      from_commit: a69248d252377876a19dfa59554f0ab5ac0ea672
      message: "chore: update build numbers for mobile v2.2.306 (#8032)"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/Chart.lock":
      file_hash: 535b7fa6693daf2070d1d618ade65cba8a81caab
      from_commit: f1d67a03b98b873fc25d825c131e67e51abe39ac
      message: "chore: bump galoy-chart to '2b34ed33c556953682c8413b37015108fcd853b3'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/Chart.yaml":
      file_hash: efd453ff4452837a2ad3e887775a1605b13f6867
      from_commit: 0fff26e09d1fc60df4060b8665458bb7d99d4502
      message: "chore: bump galoy-chart to '49d65d474ed4287e0a0a8f28cf5123ec561c9c04'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/apollo-router/api-keys-schema.graphql":
      file_hash: a5d4a35eccc4642eae9aae7af041c73cc1d6f7fa
      from_commit: 2ec1b278211beb5fa26825cbe4362138b9335b48
      message: "chore: bump galoy-chart to '8d60eaf6d6f487c82a95c4f8736f236dfe14079d'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/apollo-router/notifications-schema.graphql":
      file_hash: 842ca56e716c87b7c93b0fdca1bf3cf469c82673
      from_commit: 9c6206d6805c56024074e0ea5ee26fb3464e3e42
      message: "chore: bump galoy-chart to 'e365593d6dbec03e36bf6db8b5d54472c7a3eec5'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/apollo-router/public-schema.graphql":
      file_hash: 83ee6a5fd7aa2c796ed37c6cc2263cf6e1fe55ca
      from_commit: 5e86b35c1c3ce041e3a79b71885b15fc8c0de56e
      message: "chore: bump galoy-chart to '821bd84547001ea107112d78ff237f00ee0a2a0a'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/apollo-router/supergraph.graphql":
      file_hash: e5a1a0599ddbbeeb3665221cfba9d5a880a1187a
      from_commit: 5e86b35c1c3ce041e3a79b71885b15fc8c0de56e
      message: "chore: bump galoy-chart to '821bd84547001ea107112d78ff237f00ee0a2a0a'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/Chart.yaml":
      file_hash: 4e4cc23b8f3c0cecb7f8efd76d20d5471ccf1121
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/NOTES.txt":
      file_hash: 0f67f7ebe3d9f98e3734a1e9130dea7a08d09937
      from_commit: 7635a514224397fc1f3fb7bbb756c9300cf0e65b
      message: "chore: bump galoy-chart to 'ada02c09c7f015c08511de4a6f4ea74680a9331d'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/_helpers.tpl":
      file_hash: d9e1e59be941eb5cd2d45151e78d7caab2f3959e
      from_commit: 7635a514224397fc1f3fb7bbb756c9300cf0e65b
      message: "chore: bump galoy-chart to 'ada02c09c7f015c08511de4a6f4ea74680a9331d'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/history-config-secret.yaml":
      file_hash: 717e8b6878fb4569b682d6c6a1c5a8fe2086e907
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/history-cronjob.yaml":
      file_hash: 1e027568394dee341f037617495689c8f1479d1e
      from_commit: efb009c74dbd5e88c9e61be98853f73422a43ebb
      message: "chore: bump galoy-chart to '14cec332bcd3793dcf0cb65e04459e72e8934d86'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/history-deployment.yaml":
      file_hash: 6780acec9332525b3be03a44cd79d5f489857874
      from_commit: 128f441ed3030f4365d5b8ba6bfcd13f635aeed1
      message: "chore: bump galoy-chart to 'c4bb4e1b9d9a935e662d9ae50cd23bff55d999c6'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/history-migration-job.yaml":
      file_hash: 329f84516eee94b48005d44312ebb34a05298966
      from_commit: 5b8e15e68bef7d63c6197a0c3d279bac4a8a0272
      message: "chore: bump galoy-chart to 'fc5e0815af9788394ec62b3d7dbea458a3429475'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/history-service.yaml":
      file_hash: 3ff05fc46c3aee216b8ed926a69c29dc2711dabe
      from_commit: 7635a514224397fc1f3fb7bbb756c9300cf0e65b
      message: "chore: bump galoy-chart to 'ada02c09c7f015c08511de4a6f4ea74680a9331d'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/realtime-config-secret.yaml":
      file_hash: 9dca9df29629f69efd22e0dee2cb77ab09cdba0a
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/realtime-deployment.yaml":
      file_hash: 71be47b6cf8546c184897c780f335720a3563fc4
      from_commit: e873370507647c1a4acc3207cc6f95d18937f372
      message: "chore: bump galoy-chart to '1954150cb767258cdfc7ee7e27ed417f9347aa59'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/templates/realtime-service.yaml":
      file_hash: 1e7fbd8794c3a25f2f2b8800d29031694484b15c
      from_commit: 7635a514224397fc1f3fb7bbb756c9300cf0e65b
      message: "chore: bump galoy-chart to 'ada02c09c7f015c08511de4a6f4ea74680a9331d'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/charts/price/values.yaml":
      file_hash: bfd778af3b9aba390add0326cb1e0334f8d97312
      from_commit: a5205dedbf2f45ab7afe56e0f9878ceb8480797b
      message: "chore: bump galoy-chart to '46e07a303b63aa79c36b0acae25534ad3a027e67'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/NOTES.txt":
      file_hash: 53ac1c1b166711b66f1f2bf10f318de8e7565126
      from_commit: f3b36f88edd35234b20803479815d0bd8fabac25
      message: "chore: bump monitoring-chart to '87ae0b0d962805a0597d44459d8e2c0d7254631b'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/_helpers.tpl":
      file_hash: 89c9aacd3769df3496c3b328a7c9e38f4a0e2e89
      from_commit: 91dce8541fc2363f6db4fbdb06346c0f3da2c16f
      message: "chore: bump galoy-chart to '492f6faeb7067b6a502f057c3a4ab649d6e94fc7'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/admin-ingress.yaml":
      file_hash: 67e432c37c9cf9dec4093ae921442f59c289959c
      from_commit: ea3e64f8d6737c5c3060567daa741fd2d81a95c4
      message: "chore: bump galoy-chart to '147d8f3a552175a3de59b17f55b89367f3ebf463'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/admin-service.yaml":
      file_hash: fb130834f7463d3d5ef567ca4e993a1daf649747
      from_commit: ed6e6d4b29281be4f512486f718bded458ef906e
      message: "chore: bump galoy-chart to '3aa63ca4b4f60a8bc5ed7a76c3e09d96e00e43e5'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-deployment.yaml":
      file_hash: 880c94abcc45ba9ea7d470837967881e98b4b8e0
      from_commit: e17dda3817c6281dd3babd44bbecc5dda857ad78
      message: "chore: bump galoy-chart to '2211f9cfed25da55867a3a8abe9a3f6b2a970147'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-ingress.yaml":
      file_hash: 1951bda17c9dea6d1492fa8ba3728809346487f7
      from_commit: 377957ae398553eb593a00a904381811374d6dd5
      message: "chore: bump galoy-chart to 'ef4f027ba50db565586825bad1458add42fb4465'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-keys-cm.yaml":
      file_hash: 3a849e13ef69dc5ac1c459e857f27c6e7774cd05
      from_commit: 7f35972e3f4276891a702692714136373b289c30
      message: "chore: bump galoy-chart to '99ba136682e52ab32e95b21fea37c9511be7a117'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-keys-deployment.yaml":
      file_hash: 1f81cf8a7be1c826bc4b606a846277065789653d
      from_commit: 55ce121cd440c688ff37cf3c7f1713c5fbfc0690
      message: "chore: bump galoy-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-keys-service.yaml":
      file_hash: 30ee1162490c54e832a57db77d65fca6d260957f
      from_commit: 4e0f8d3d4ac068e5a2465e253c9950f4bf413a29
      message: "chore: bump galoy-chart to 'c3646e2437fd793867418d9b742d84dcf78dadfc'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/api-service.yaml":
      file_hash: 1c32813a96aa2cda98acfe259460fdb4ac0eba8b
      from_commit: 0882c882e2b33c516afb6b024ad085042b7917d2
      message: "chore: bump galoy-chart to '6a828a9ead69618dc7ce303a7b37cb5451725377'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/consent-deployment.yaml":
      file_hash: ac1764588cf5cfa12c3c447b66fbcf014699fa47
      from_commit: 55ce121cd440c688ff37cf3c7f1713c5fbfc0690
      message: "chore: bump galoy-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/consent-ingress.yaml":
      file_hash: 7b10601bbd692c22c3d999c7d28e21d7d4a975f6
      from_commit: 3253f3df5c3d7cb1dd959a05335038ee606ccc20
      message: "chore: bump galoy-chart to '8d8fe9eca3eaa8aec062bf9e81568428f848a8aa'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/consent-service.yaml":
      file_hash: e2bf8b709b04cc5048e4bc2d852f27097f743884
      from_commit: 0c6253d1b35a1ab6c048e71caa54d087632ad74c
      message: "chore: bump galoy-chart to '1bf3111e039624698cd5592fc1704bc3ffab7b28'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/exporter-deployment.yaml":
      file_hash: 81f2f4e901b18b2fb01cebb9423fba657111ab9c
      from_commit: 069b56165e65206e99fb8a6e6c5e5281f92da614
      message: "chore: bump galoy-chart to 'cd8b4b873090fb64102c3f1d1d0e130b60f005e1'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-cronjob.yaml":
      file_hash: 776b98772e931ab69364cd25b24fab81524be3c1
      from_commit: 069b56165e65206e99fb8a6e6c5e5281f92da614
      message: "chore: bump galoy-chart to 'cd8b4b873090fb64102c3f1d1d0e130b60f005e1'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-custom-config-secret.yaml":
      file_hash: b3413decbcb716edf3a2c3a9d915c51f562c963e
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-migration-job.yaml":
      file_hash: 88c065c4a7d03c4d96c7534434e35e1ed44d0c8e
      from_commit: 069b56165e65206e99fb8a6e6c5e5281f92da614
      message: "chore: bump galoy-chart to 'cd8b4b873090fb64102c3f1d1d0e130b60f005e1'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-role.yaml":
      file_hash: 5a05ab3273b64f78dadb01a7d0104996d37fa42a
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-rolebinding.yaml":
      file_hash: 1724f6308e55e1e94a3e437461c651ef121feb20
      from_commit: 50810363498608d5188b7454faceef732f76c38c
      message: "chore: bump galoy-chart to '2f4b3cadf1b7c7a547337e7ec729d94131a9ed23'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-secrets.yaml":
      file_hash: f761c23bf59e83e7ef80fc182f747a495af4dde6
      from_commit: 91dce8541fc2363f6db4fbdb06346c0f3da2c16f
      message: "chore: bump galoy-chart to '492f6faeb7067b6a502f057c3a4ab649d6e94fc7'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/galoy-serviceaccount.yaml":
      file_hash: 73706d607ab1622e4eb47e5ce8ee904936f85b36
      from_commit: 0882c882e2b33c516afb6b024ad085042b7917d2
      message: "chore: bump galoy-chart to '6a828a9ead69618dc7ce303a7b37cb5451725377'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/mongo-backup-configmap.yaml":
      file_hash: be829c446ee33878702937faf0d5c86b70516f37
      from_commit: 185cf79e2f8a0ce67bacc62ae4c0fcf35150b893
      message: "chore: bump galoy-chart to '6b0b0d6910f8d30244ee5a3f0ea4539fe4492191'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/mongo-backup-cronjob.yaml":
      file_hash: ad8fa3665745db11154bdd5d3e9a9dd2eb7d9c95
      from_commit: e1d1d23db7555eaa80c4c1755362e6b226723960
      message: "chore: bump galoy-chart to '36d0b9c9665d1244e480c03fdc0d60ad63ea759b'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/notification-jobs-deployment.yaml":
      file_hash: f6ceb4f813532016e13322b92354c561693fe722
      from_commit: 1fc33df15b28c682114371d2cc0342d5dbbb5dd9
      message: "chore: bump galoy-chart to '6081f2c4788b6466cc7ed79686719eff827f7c70'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/notifications-cm.yaml":
      file_hash: 4e68c836a55dde6ddeec564a61be3512b26caa51
      from_commit: 09ad7e786191989167c83a39247065259791d93b
      message: "chore: bump galoy-chart to '73bc4ff28b9bbc7bdae1840c9ca94d5ee752dc47'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/notifications-deployment.yaml":
      file_hash: ce3804231fb2daf77673aac624c900bb5b091ec9
      from_commit: 1fc33df15b28c682114371d2cc0342d5dbbb5dd9
      message: "chore: bump galoy-chart to '6081f2c4788b6466cc7ed79686719eff827f7c70'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/notifications-jobs-cm.yaml":
      file_hash: 4a434c42325ec854918d9bcd062d2c18db3fa8b1
      from_commit: 09ad7e786191989167c83a39247065259791d93b
      message: "chore: bump galoy-chart to '73bc4ff28b9bbc7bdae1840c9ca94d5ee752dc47'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/notifications-service.yaml":
      file_hash: 59b683341a3e95b41faa0e71cc86b3cf6735a102
      from_commit: 0a2d391fcafc84bba646b78abf25e2fb9cdd92b8
      message: "chore: bump galoy-chart to 'f54105864bb5b73502544ad8bac3493acc6fd3ea'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/required-checks.yaml":
      file_hash: cfe02fc1eec4738e1a77392f08ecea59d974fe5b
      from_commit: 0882c882e2b33c516afb6b024ad085042b7917d2
      message: "chore: bump galoy-chart to '6a828a9ead69618dc7ce303a7b37cb5451725377'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/router-supergraph-cm.yaml":
      file_hash: d75ebd4926ec857dc2864c73d7388b4f06095e39
      from_commit: efd180e10b0f2ef71d2831d516c063650b8a1c2f
      message: "chore: bump galoy-chart to '7591864d2f2e8254ae697c69c6b7fa0c51be1be6'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/trigger-deployment.yaml":
      file_hash: 8dfab18c77fc67a7a7f3af304424a5b74436f572
      from_commit: 069b56165e65206e99fb8a6e6c5e5281f92da614
      message: "chore: bump galoy-chart to 'cd8b4b873090fb64102c3f1d1d0e130b60f005e1'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/trigger-service.yaml":
      file_hash: 56390faba6f090ace7c903e6a7b62f4095a3baf7
      from_commit: 0882c882e2b33c516afb6b024ad085042b7917d2
      message: "chore: bump galoy-chart to '6a828a9ead69618dc7ce303a7b37cb5451725377'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/websocket-deployment.yaml":
      file_hash: be14adfcb7b16d6dbdd6c4519f4c682fb603eb19
      from_commit: e17dda3817c6281dd3babd44bbecc5dda857ad78
      message: "chore: bump galoy-chart to '2211f9cfed25da55867a3a8abe9a3f6b2a970147'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/websocket-ingress.yaml":
      file_hash: ff65d1d79934f535d80f352070bca16a7889c5f9
      from_commit: 08d595b81a3a594bc48fb2ba026bf49423c2bca3
      message: "chore: bump galoy-chart to 'e4ca5f8888ff27e183843373ca2c8e6aa96b63e8'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/templates/websocket-service.yaml":
      file_hash: dbd29b7bdea324fea96ada171f462d256ffd1807
      from_commit: d7a4d3ebe859a26054a93ec5a177c770eea18cfc
      message: "chore: bump galoy-chart to '32a302d659f6b2176c243e2895e54007851b4b6b'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/chart/values.yaml":
      file_hash: 20e4a308530b63831c18e6cbb939c06a548c36ff
      from_commit: 0fff26e09d1fc60df4060b8665458bb7d99d4502
      message: "chore: bump galoy-chart to '49d65d474ed4287e0a0a8f28cf5123ec561c9c04'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/ci/tasks/galoy-smoketest.sh":
      file_hash: 5ac4775d80c613a2f9a13425d783fffa7959c155
      from_commit: 3915185de05c17bafe7c7722d262d1ee7a986772
      message: "chore: bump galoy-chart to 'c9dc432a628bafd65d8fc0c6fc593373996ba471'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 7da3881a5c6a9a79ccf25dbc129c7a8befd53c34
      message: "Bump galoy-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{gcp-galoy-staging}/modules/galoy/vendor/galoy/git-ref/ref":
      file_hash: 4e6ccb9e5b759d73c854d39ac2f965e9407e97e6
      from_commit: 0fff26e09d1fc60df4060b8665458bb7d99d4502
      message: "chore: bump galoy-chart to '49d65d474ed4287e0a0a8f28cf5123ec561c9c04'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/gcp/galoy-bbw/galoy/galoy-scaling.yml":
      file_hash: 735ae43fb95339ff6647c9e358707cb011299f23
      from_commit: d5b680849ea82fdd39dfc4f0ecf8dfeaeaf8b6b6
      message: "chore: increase galoy resources in prod (#7955)"
    "{latest}/gcp/galoy-bbw/galoy/main.tf":
      file_hash: 1d4c39c03b6b5be6a64fd8088cdcd8f289caaa01
      from_commit: 9dbaaa447b99562d2c2d81d4489a942cbffbf603
      message: "chore: enable US IPs (#8056)"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
propagated_from: gcp-galoy-staging

