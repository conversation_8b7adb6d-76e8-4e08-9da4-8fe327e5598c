secrets:
  create: true
  mapApiKey: ""
map:
  coreUrl: "http://galoy-oathkeeper-proxy.galoy-dev-galoy.svc.cluster.local:4455/graphql"
  otelExporterOtlpEndpoint: http://localhost:4318
  tracingServiceName: "map"
image:
  repository: us.gcr.io/galoy-org/galoy-map
  digest: "sha256:80976b175ca2632884b4959945b16f8b57bae2b754b074bf99857ea7b87e7069" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=62cd3f3;app=map;monorepo_subdir=apps/map;
ingress:
  enabled: false
service:
  port: 3000
  type: ClusterIP
resources: {}
