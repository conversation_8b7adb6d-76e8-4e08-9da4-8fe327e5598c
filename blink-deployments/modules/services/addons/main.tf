resource "kubernetes_namespace" "addons" {
  metadata {
    name = local.addons_namespace
  }
}

provider "hydra" {
  endpoint = "http://galoy-hydra-admin.${local.galoy_namespace}:4445"
}

resource "hydra_oauth2_client" "galoy_pay" {
  client_name                = "Blink POS"
  grant_types                = ["authorization_code"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read"]
  redirect_uris              = [for host in local.galoy_pay_hosts : "https://${host}/api/auth/callback/blink"]
  skip_consent               = true
}

resource "random_password" "galoy_pay_next_auth_secret" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "galoy_pay" {
  metadata {
    name      = "galoy-pay"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }
  data = {
    next-auth-secret = random_password.galoy_pay_next_auth_secret.result
    client-secret    = hydra_oauth2_client.galoy_pay.client_secret
  }
}


resource "kubernetes_secret" "galoy_pay_smoketest" {
  metadata {
    name      = "galoy-pay-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    galoy_pay_endpoints = jsonencode(local.galoy_pay_hosts)
    galoy_pay_port      = 80
  }
}

data "kubernetes_secret" "redis_creds" {
  metadata {
    name      = "galoy-redis-pw"
    namespace = local.galoy_namespace
  }
}
resource "kubernetes_secret" "redis_creds" {
  metadata {
    name      = "galoy-redis-pw"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }

  data = data.kubernetes_secret.redis_creds.data
}

resource "kubernetes_secret" "nostr_private_key" {
  metadata {
    name      = "galoy-nostr-private-key"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }

  data = {
    # random key
    "key" : local.nostr_private_key
  }
}

data "kubernetes_secret" "lnd_credentials" {
  metadata {
    name      = "${local.primary_offchain_lnd}-credentials"
    namespace = local.bitcoin_namespace
  }
}

resource "kubernetes_secret" "lnd_credentials" {
  metadata {
    name      = "lnd-credentials"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }

  data = data.kubernetes_secret.lnd_credentials.data
}

resource "helm_release" "galoy_pay" {
  name       = "galoy-pay"
  chart      = "${path.module}/vendor/galoy-pay/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.addons.metadata[0].name

  values = [
    templatefile("${path.module}/galoy-pay-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-pay/git-ref/ref")
      hosts : local.galoy_pay_hosts
      pay_domain : local.pay_domain
      pay_url : local.galoy_pay_endpoint
      core_gql_url_intranet : local.graphql_url_internal
      redis_namespace : local.galoy_namespace
      lnd_dns : "${local.primary_offchain_lnd}.${local.bitcoin_namespace}.svc.cluster.local"
      nostr_pubkey : local.nostr_public_key
      client_id = hydra_oauth2_client.galoy_pay.client_id
      hydra_public_api : local.hydra_public_api
      next_auth_url : "https://${local.galoy_pay_hosts[1]}"
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_service_name : local.pay_tracing_service_name
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/addons/galoy-pay-scaling.yml")
  ]
}

data "kubernetes_secret" "grafana" {
  metadata {
    namespace = local.monitoring_namespace
    name      = "grafana-creds"
  }
}

provider "grafana" {
  url  = local.grafana_url
  auth = "admin:${data.kubernetes_secret.grafana.data["admin-password"]}"

  # Add retry configuration to handle temporary unavailability
  retry_wait_min_seconds = 1
  retry_wait_max_seconds = 30
  retry_max              = 10
}

terraform {
  required_providers {
    hydra = {
      source  = "svrakitin/hydra"
      version = "0.5.2"
    }
    postgresql = {
      source  = "cyrilgdn/postgresql"
      version = "1.24.0"
    }
    grafana = {
      source  = "grafana/grafana"
      version = "1.13.4"
    }
  }
}
