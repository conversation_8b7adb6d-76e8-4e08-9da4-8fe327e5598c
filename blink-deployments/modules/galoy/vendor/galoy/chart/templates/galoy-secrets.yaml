{{- if .Values.secrets.create -}}

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.mongodb.auth.existingSecret }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/component: mongodb
type: Opaque
data:
  mongodb-root-password: {{ .Values.secrets.mongodbRootPassword | toString | b64enc }}
  mongodb-passwords: {{ .Values.secrets.mongodbPassword | toString | b64enc }}
  mongodb-metrics-password: {{ .Values.secrets.mongodbMetricsPassword | toString | b64enc }}
  mongodb-replica-set-key: {{ .Values.secrets.mongodbReplicaSetKey | toString | b64enc }}

---
apiVersion: v1
kind: Secret
metadata:
  name: galoy-mongodb-connection-string
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  mongodb-con: {{ .Values.secrets.mongodbConnectionString | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.redis.auth.existingSecret }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/component: redis
type: Opaque
data:
  {{ .Values.redis.auth.existingSecretPasswordKey }}: {{ .Values.secrets.redisPassword | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.bria.apiKeyExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.bria.apiKeyExistingSecret.key }}: {{ .Values.secrets.briaApiKey | toString | b64enc }}

---

{{ if .Values.postgresql.enabled }}

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.postgresql.auth.existingSecret }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/component: postgres
type: Opaque
data:
  password: {{ .Values.secrets.postgresPassword | toString | b64enc }}
  username: {{ .Values.postgresql.auth.username | toString | b64enc }}
  database: {{ .Values.postgresql.auth.database | toString | b64enc }}

{{ end }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.geetestExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.geetestExistingSecret.id_key }}: {{ .Values.secrets.geetestId | toString | b64enc }}
  {{ .Values.galoy.geetestExistingSecret.secret_key }}: {{ .Values.secrets.geetestSecretKey | toString | b64enc }}


---


apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.twilioExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.twilioExistingSecret.verify_service_id }}: {{ .Values.secrets.twilioVerifyServiceId | toString | b64enc }}
  {{ .Values.galoy.twilioExistingSecret.account_sid_key }}: {{ .Values.secrets.twilioAccountSid | toString | b64enc }}
  {{ .Values.galoy.twilioExistingSecret.auth_token_key }}: {{ .Values.secrets.twilioAuthToken | toString | b64enc }}
  {{ .Values.galoy.twilioExistingSecret.messaging_service_id }}: {{ .Values.secrets.twilioMessagingServiceId | toString | b64enc }}
  {{ .Values.galoy.twilioExistingSecret.welcome_content_sid }}: {{ .Values.secrets.twilioWelcomeContentSid | toString | b64enc }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.svixExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "twilio"
type: Opaque
data:
  {{ .Values.galoy.svixExistingSecret.secret_key }}: {{ .Values.secrets.svixSecretKey | toString | b64enc }}
---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.lnd1.credentialsExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.lnd1.credentialsExistingSecret.macaroon_key }}: {{ .Values.secrets.lnd1Macaroon | toString | b64enc }}
  {{ .Values.galoy.lnd1.credentialsExistingSecret.tls_key }}: {{ .Values.secrets.lnd1Tls | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.lnd1.pubkeyExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.lnd1.pubkeyExistingSecret.key }} : {{ .Values.secrets.lnd1PubKey | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.lnd2.credentialsExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.lnd2.credentialsExistingSecret.macaroon_key }}: {{ .Values.secrets.lnd2Macaroon | toString | b64enc }}
  {{ .Values.galoy.lnd2.credentialsExistingSecret.tls_key }}: {{ .Values.secrets.lnd2Tls | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.lnd2.pubkeyExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.lnd2.pubkeyExistingSecret.key }} : {{ .Values.secrets.lnd2PubKey | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.kratos.existingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.kratos.existingSecret.master_user_password }}: {{ .Values.secrets.kratosMasterUserPassword | toString | b64enc }}
  {{ .Values.galoy.kratos.existingSecret.callback_api_key }}: {{ .Values.secrets.kratosCallbackApiKey | toString | b64enc }}


---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.proxyCheckExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: {{ .Release.Name }}
type: Opaque
data:
  {{ .Values.galoy.proxyCheckExistingSecret.key }}: {{ .Values.secrets.proxyCheckApiKey | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ template "galoy.apiKeys.fullname" . }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  pg-con: {{ .Values.secrets.apiKeysPgCon | toString | b64enc }}
---

apiVersion: v1
kind: Secret
metadata:
  name: {{ template "galoy.notifications.fullname" . }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  pg-con: {{ .Values.secrets.notificationsPgCon | toString | b64enc }}
  pg-read-con: {{ .Values.secrets.notificationsPgCon | toString | b64enc }}
  smtp-password: {{ .Values.secrets.notificationsSmtpPassword | toString | b64enc }}
  firebase-service-account: {{ .Values.secrets.firebaseServiceAccountJson | toString | b64enc }}
---

apiVersion: v1
kind: Secret
metadata:
  name: {{ template "galoy.name" . }}-{{ .Values.galoy.openai.existingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.openai.existingSecret.key }}: {{ .Values.secrets.openaiApiKey | toString | b64enc }}
---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.pineconeExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.pineconeExistingSecret.key }}: {{ .Values.secrets.pineconeApiKey | toString | b64enc }}
---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.galoy.telegramExistingSecret.name }}
  labels:
    app: {{ template "galoy.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.galoy.telegramExistingSecret.private_key }}: {{ .Values.secrets.telegramPrivateKey | toString | b64enc }}
  {{ .Values.galoy.telegramExistingSecret.api_token_key }}: {{ .Values.secrets.telegramApiToken | toString | b64enc }}
{{- end -}}
