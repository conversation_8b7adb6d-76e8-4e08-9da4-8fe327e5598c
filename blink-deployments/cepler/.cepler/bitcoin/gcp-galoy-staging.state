---
version: 420
current:
  head_commit: efe842644097b301e6cff948772c592a80812d41
  files:
    "{latest}/gcp/galoy-staging/bitcoin/bitcoind-onchain-scaling.yml":
      file_hash: 692d8264cf45580d66b254006ab23cf7c8cd8ca9
      from_commit: 233dc6cf78c655d025d87853bd84bb53e0d1371d
      message: "chore: staging bitcoind-onchain limits for IBD (#4691)"
    "{latest}/gcp/galoy-staging/bitcoin/bitcoind-scaling.yml":
      file_hash: 2ba2155c110c6b20caa5be987e19f0706c784d6e
      from_commit: 446860e256d9d4f31df222a2001039d30a88ec6f
      message: "chore: increase staging bitcoind memory limit (#7749)"
    "{latest}/gcp/galoy-staging/bitcoin/bria-scaling.yml":
      file_hash: 19b8d6a1e7f1ad7b0982e902989b22f515e97257
      from_commit: 34b09989cc53502977489b959f0c54deb96e6b26
      message: "chore: add bria-scaling"
    "{latest}/gcp/galoy-staging/bitcoin/fulcrum-scaling.yml":
      file_hash: b0f26b2ffef3d9fcebbbe9947b865fd31637acd1
      from_commit: fcfbfaa9867959f6f36ab78f7bf605d94d119627
      message: "chore: increase fulcrum memory limit on staging (#4685)"
    "{latest}/gcp/galoy-staging/bitcoin/lnd-scaling.yml":
      file_hash: 520aa0bd2c6ac8bef93fd72559c4fac674bf30b8
      from_commit: 5d04e5322ab09c47234dfa61b864bb1b31fabd7f
      message: "chore: bump staging lnd resources"
    "{latest}/gcp/galoy-staging/bitcoin/main.tf":
      file_hash: e217845b42de68ae19c5f02b09b635de59531159
      from_commit: 336244d9e8362f0211c3c2a7cd00f5e66ad1784b
      message: "chore: clean bria settings as managed outside of terraform (#8048)"
    "{latest}/gcp/galoy-staging/bitcoin/mempool-scaling.yml":
      file_hash: 5f06ca33f21081c4576fecc92d06c95df333f1be
      from_commit: 28f1946bdec686d15df5dfd9272aa3a1085cf8d8
      message: "chore: bump mempool resources"
    "{latest}/gcp/galoy-staging/bitcoin/rtl-scaling.yml":
      file_hash: 06e902d0abd9f32838c29dc92c24c44759c50706
      from_commit: ad5d17078c6c8e3c8aa078dd6b5da93932d9b1ee
      message: "chore: give mempool / rtl more resources"
    "{latest}/gcp/galoy-staging/bitcoin/specter-scaling.yml":
      file_hash: 22874d8f5a94a46e964b53e9570a011f81b06a79
      from_commit: 52468826892c220673d6ea1a4f3078902040bf74
      message: "chore: increase specter cpu request and limit (#4205)"
    "{latest}/gcp/galoy-staging/shared/main.tf":
      file_hash: ddf53cabe701956c5e380b8750656c8870b708b6
      from_commit: efe842644097b301e6cff948772c592a80812d41
      message: "chore: switch staging to staging.blink.sv, remove staging.galoy.io (#8074)"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/services/bitcoin/bitcoind-onchain-values.yml":
      file_hash: a49461f94d4bf504d1b987c4f4e1c9ce690537dd
      from_commit: 305e69196f7b150e26c4c5efb4a2d0881e3f3159
      message: "feat: add the descriptor to bitcoind-onchain (#3677)"
    "{latest}/modules/services/bitcoin/bitcoind-signet-values.yml":
      file_hash: a435e8de439c745b4034e4b9d9b06f678e3c8fad
      from_commit: ec251ac375389b2c3fcfbe5a5ffe49e80be024d7
      message: "chore: add signet related options to bitcoin (#2506)"
    "{latest}/modules/services/bitcoin/bitcoind-testnet-values.yml":
      file_hash: a435e8de439c745b4034e4b9d9b06f678e3c8fad
      from_commit: e0e1d8a9db0b86db2b0ba4d4defeed922644290c
      message: "feat: small-footprint for bitcoind (#2170)"
    "{latest}/modules/services/bitcoin/bitcoind-values.yml.tmpl":
      file_hash: cd0702bd4c9c074b70d61b671b11297fcab22f2f
      from_commit: 1a454e90447ecfe9436e75bc8017389cda4e3d78
      message: "chore: remove resource values from services/bitcoind-values"
    "{latest}/modules/services/bitcoin/bitcoind.tf":
      file_hash: 7828e2b2bdd3529ab8ef29057725a43e92a3946b
      from_commit: 0607a9a9961326bd0cdaf857a9e62ef3e6ed0ffd
      message: "chore: increase bitcoind pvc size in prod (#7742)"
    "{latest}/modules/services/bitcoin/bria-blocked-addresses.yml":
      file_hash: cc06fb35741c724af216627aeff6dec737cf83be
      from_commit: 2e9a0ae04f2aa97c3c4c3118b7d54bc95e35b1bd
      message: "chore: update bria blocked addresses (#7996)"
    "{latest}/modules/services/bitcoin/bria-values.yml.tmpl":
      file_hash: d9aaecaa8063abdcfce653fc0a358373d946a8ad
      from_commit: 9afa7939894a33e95de458b63124f9bb692c52a6
      message: "chore: remove retries to mempoolSpace"
    "{latest}/modules/services/bitcoin/bria.tf":
      file_hash: adc84b51ee6a978bd61c6d1a235d2efe4729443c
      from_commit: 336244d9e8362f0211c3c2a7cd00f5e66ad1784b
      message: "chore: clean bria settings as managed outside of terraform (#8048)"
    "{latest}/modules/services/bitcoin/fulcrum-values.yml.tmpl":
      file_hash: a90b069d278f65d3c8406eb9f5c2228d1b611fd8
      from_commit: ff899b4003425089f70192de9bd73e8c7c21ba88
      message: "chore: remove old resource blocks from bitcoin"
    "{latest}/modules/services/bitcoin/fulcrum.tf":
      file_hash: c4ba63c9da5f31dc7bd7acc6a1ff6689ce15df06
      from_commit: 9c89ede52fb8f6689a9183c15333d9dbb7eb3323
      message: "chore: set lnd scaling values (#3820)"
    "{latest}/modules/services/bitcoin/lnd-values.yml.tmpl":
      file_hash: efe1219fd8ba601bfba977fcddad8ab1b4eab707
      from_commit: 16c4bbf5d5270001f404b2507f35d035ce230b17
      message: "chore: apply lnd node alias to signet also (#7912)"
    "{latest}/modules/services/bitcoin/lnd.tf":
      file_hash: 2f26a65032c7ccedf973a8b00ec8305b2176153c
      from_commit: bd65a13a8099cfba878373682ed7227eefb1bd20
      message: "chore: switch lnd2 to bitcoind2 (#6644)"
    "{latest}/modules/services/bitcoin/specter-values.yml.tmpl":
      file_hash: 70c7498b29356537227da02be3c06baf14950c98
      from_commit: 162449b223008c6a85a892bf8c4bc3478bbeb6b0
      message: "fix: remove specter nginx ingress annotation"
    "{latest}/modules/services/bitcoin/specter.tf":
      file_hash: 22e60e75af0469efd43bd70f9027c23a2f7d69b8
      from_commit: 9c89ede52fb8f6689a9183c15333d9dbb7eb3323
      message: "chore: set lnd scaling values (#3820)"
    "{latest}/modules/services/bitcoin/variables.tf":
      file_hash: 906306a15860fb0b0d815f670e6e95fb6e09fb3f
      from_commit: 336244d9e8362f0211c3c2a7cd00f5e66ad1784b
      message: "chore: clean bria settings as managed outside of terraform (#8048)"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/Chart.yaml":
      file_hash: 477544e5491f898c49fb889c4ed8ea333cfeedc8
      from_commit: 6a856a51f2e54e302b55c198cb375e26845e046d
      message: "chore: bump bitcoind-chart to '29ea9caf11baec03422f718f98392fa8c2d5d43e'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/regtest-values.yaml":
      file_hash: cecc66b330411135664e080ec628a66cf4ef3f25
      from_commit: 8953c36e99c507d38a8ea1b6c306ecef741c2a9d
      message: "chore: bump bitcoind-chart to '20acf08b904e701c9e84417b9c0f512e3b90b8c6'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/signet-values.yml":
      file_hash: aa0dafdb060abf1a9f299c76e1ff2614a75a9dca
      from_commit: 8953c36e99c507d38a8ea1b6c306ecef741c2a9d
      message: "chore: bump bitcoind-chart to '20acf08b904e701c9e84417b9c0f512e3b90b8c6'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/NOTES.txt":
      file_hash: 59a09f2d8bfa5a89165d111ab2f30d2b7ed786a7
      from_commit: 7fc329a7d6ab02b4eb727420484191a0f3f9aef7
      message: "Bump bitcoind-chart to '5cfd89bac88cddb6355ccb6b98f7447a0ffdad8d'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/_helpers.tpl":
      file_hash: 4300363da4cc6e600a6847eb19b1557ba64a0521
      from_commit: 7fc329a7d6ab02b4eb727420484191a0f3f9aef7
      message: "Bump bitcoind-chart to '5cfd89bac88cddb6355ccb6b98f7447a0ffdad8d'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/configmap.yaml":
      file_hash: a27a16a258f491b19c45dd93c01e5adef147705d
      from_commit: bdf513bf10413cd57d158d9050c7e9b082a0ca3e
      message: "chore: bump bitcoind-chart to 'bad80d03160600f391d72f0c9e5718510f75467f'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/ingress.yaml":
      file_hash: 1f2a6877816e5280dac857d6311e5220e4851757
      from_commit: fba7360b9626e8798cc8018e880711aeb80f88ae
      message: "chore: bump bitcoind-chart to '73f3b8d6008bff83843942de250b3c0586e0b445'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/network-secret.yaml":
      file_hash: d7ba9cf199dfaf113130890cb63e677b9d9140a2
      from_commit: ****************************************
      message: "chore: bump bitcoind-chart to 'a041a1da5dbc0e9ab574ffdb8db73fc26da2f99a'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/pvc.yaml":
      file_hash: 292c0db5bcbc1794113536328fc016fadec1c691
      from_commit: 7fc329a7d6ab02b4eb727420484191a0f3f9aef7
      message: "Bump bitcoind-chart to '5cfd89bac88cddb6355ccb6b98f7447a0ffdad8d'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/secrets.yaml":
      file_hash: b3f71ed24263b4c5f5c7c5ae47a66923d33b982e
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/service.yaml":
      file_hash: 2502db934b5cd0135ab04cb1930df90d75cedb08
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/serviceaccount.yaml":
      file_hash: c6c8bf7562d6eb4000adfdcd17c7b6b33040d847
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/statefulset.yaml":
      file_hash: 936d4319ccdaabe68bdd2140289f0f2f939e5dac
      from_commit: 6a856a51f2e54e302b55c198cb375e26845e046d
      message: "chore: bump bitcoind-chart to '29ea9caf11baec03422f718f98392fa8c2d5d43e'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/templates/tests/test-connection.yaml":
      file_hash: 74b212419ef15214de5da3babab13c0b83ca7fbc
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/testnet-values.yaml":
      file_hash: 8685d4ca17326b9b354f34b67a39fbd322a1be90
      from_commit: 8953c36e99c507d38a8ea1b6c306ecef741c2a9d
      message: "chore: bump bitcoind-chart to '20acf08b904e701c9e84417b9c0f512e3b90b8c6'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/chart/values.yaml":
      file_hash: ec37fcae041d1e370fc5c7b8e5620e7195f446b1
      from_commit: 6a856a51f2e54e302b55c198cb375e26845e046d
      message: "chore: bump bitcoind-chart to '29ea9caf11baec03422f718f98392fa8c2d5d43e'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/ci/tasks/bitcoind-smoketest.sh":
      file_hash: 88a355a2464763cf879949e5f18bdd4c6d5516aa
      from_commit: 8b50e84a580f52913795bbac9656bade143cc2a7
      message: "chore: bump bitcoind-chart to 'aed7815b3039642fed6f74a6e0e93151f55f4dde'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: ****************************************
      message: "Bump bitcoind-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/bitcoin/vendor/bitcoind/git-ref/ref":
      file_hash: 1acb55ce4fb94d85935fef37b0c3453def8bc79b
      from_commit: 6a856a51f2e54e302b55c198cb375e26845e046d
      message: "chore: bump bitcoind-chart to '29ea9caf11baec03422f718f98392fa8c2d5d43e'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/Chart.lock":
      file_hash: ****************************************
      from_commit: e3db5310bb6a6fa676aabb2dc777f6edc3246d77
      message: "chore: bump bria-chart to '7fda21283388bba7f4b58b5af65969dc1bd9e5e0'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/Chart.yaml":
      file_hash: 98bc43c74592c8d23c366bf7d1b4c41d2203bdc2
      from_commit: 245cc3c00742d6c65f6f2958fd68dc68c137b2ac
      message: "chore: bump bria-chart to 'af48974418b8dcec0fc4f362df6efa29b4b6afb8'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/_helpers.tpl":
      file_hash: b6ddc792bce8bf4b50e0086aa2383bc483d1e2f6
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/bria-admin-svc.yaml":
      file_hash: 3aaf067728fd625e38fd8eecf6e2c634ae5eb68c
      from_commit: 5438fca6ad04a3da9e7a16353d8753ea65b47b4e
      message: "chore: bump bria-chart to '0d3f99b4bacd1ef71264a6c2797a46d4a9d2293d'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/bria-api-svc.yaml":
      file_hash: 4d6c78fc90b883e40612870f8af203a5786c0801
      from_commit: 59a6ff0f3322a44ab229b631fbec2a3db22c4ebb
      message: "chore: bump bria-chart to 'b33e4e2e501698c423b90e3f4d23a3f0a21250d6'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/bria-cm.yaml":
      file_hash: 74d33bcf0e9cf36ab202e059a478e732519dd505
      from_commit: 489cc2a0a8304df755e101104870a6c80d4347aa
      message: "chore: bump bria-chart to '9513b8a8873ba7e2e098977f5d8b0c489fea9c03'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/bria-deployment.yaml":
      file_hash: 066b50d7dd08ec895cbf6eeaa5cfeb743b5c967e
      from_commit: 5d7a894a9fd577e528e8b012c5a45c931adc5224
      message: "chore: bump bria-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/templates/bria-secrets.yaml":
      file_hash: ab273ecb29a35f583b72778db97aee3125c7112f
      from_commit: 65776ecf12bc91cf47c40805084c9d009ecfa81f
      message: "chore: bump bria-chart to '08bbe10863f1a67c8d5cc14f162b2878ba657d21'"
    "{latest}/modules/services/bitcoin/vendor/bria/chart/values.yaml":
      file_hash: a19ff9b65657f48dbd8cfe2ac2dfe68bdb6ffc96
      from_commit: 245cc3c00742d6c65f6f2958fd68dc68c137b2ac
      message: "chore: bump bria-chart to 'af48974418b8dcec0fc4f362df6efa29b4b6afb8'"
    "{latest}/modules/services/bitcoin/vendor/bria/ci/tasks/bria-smoketest.sh":
      file_hash: df3cd87a47f886e00d6782362a644cf5820fd508
      from_commit: 2bb241f037875e9070cd72880882eed167098b03
      message: "chore: bump fulcrum-chart to '491e37269519e96ff16318003ec151519d1baead'"
    "{latest}/modules/services/bitcoin/vendor/bria/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/bria/git-ref/ref":
      file_hash: 7f2a475eacc12365958b4ed59a84dc2fd3f43325
      from_commit: 245cc3c00742d6c65f6f2958fd68dc68c137b2ac
      message: "chore: bump bria-chart to 'af48974418b8dcec0fc4f362df6efa29b4b6afb8'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/Chart.yaml":
      file_hash: 23603981583ecb61413d3f96b49fa3324e0eb91c
      from_commit: 980db787f4328d65ba85ee60112c9946a4da7172
      message: "chore: bump fulcrum-chart to 'c3805b2da47a8ac99eb6330c5483af7130325922'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/_helpers.tpl":
      file_hash: 49dcfe05d63704b47401cd5bd278e8994b40d93e
      from_commit: 644826dab7de4517da87ca2370ce7ce48f091e80
      message: "chore: bump fulcrum-chart to '940f421c2ee35fc3747565eef26896ae92734a82'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/configmap.yaml":
      file_hash: 760ce327f6f238c5695a509829eb40dbb23398cf
      from_commit: 6ad26ab2608520ec4f67ca33f4be30de9c2f9ee0
      message: "chore: bump fulcrum-chart to '6c6975563eb51bc14c3b5b547b9747446feb7cd2'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/ingress.yaml":
      file_hash: faf9db00c27f26f5f8b04347f7dc6bdc38d8b10b
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/pvc.yaml":
      file_hash: 900088580a7a5507588eda7a0b341d91be648a21
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/role.yaml":
      file_hash: 9b92e2658bb326a4bd6513d10ab61036be81c07f
      from_commit: 644826dab7de4517da87ca2370ce7ce48f091e80
      message: "chore: bump fulcrum-chart to '940f421c2ee35fc3747565eef26896ae92734a82'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/rolebinding.yaml":
      file_hash: 0eb5df9ccb88620c1d847af0cb398575af474a4e
      from_commit: 644826dab7de4517da87ca2370ce7ce48f091e80
      message: "chore: bump fulcrum-chart to '940f421c2ee35fc3747565eef26896ae92734a82'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/service.yaml":
      file_hash: 0e5d55cd4cdcc6d7202d1a99bfb4313d776f1e47
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/serviceaccount.yaml":
      file_hash: b2eca3aee126d2317fb84234d7fd03477926e3f5
      from_commit: 644826dab7de4517da87ca2370ce7ce48f091e80
      message: "chore: bump fulcrum-chart to '940f421c2ee35fc3747565eef26896ae92734a82'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/templates/statefulset.yaml":
      file_hash: 32617d6198cc7ec6b4355a12323839ba4603768a
      from_commit: 6ad26ab2608520ec4f67ca33f4be30de9c2f9ee0
      message: "chore: bump fulcrum-chart to '6c6975563eb51bc14c3b5b547b9747446feb7cd2'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/chart/values.yaml":
      file_hash: 7c77ecae69e353b465c4685a40eb4e384a6c1f09
      from_commit: 6ad26ab2608520ec4f67ca33f4be30de9c2f9ee0
      message: "chore: bump fulcrum-chart to '6c6975563eb51bc14c3b5b547b9747446feb7cd2'"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/ci/tasks/fulcrum-smoketest.sh":
      file_hash: 2546e0c7d5f5f9473cfd5a1b0982f20b21ecdcc6
      from_commit: 56019ce22aa91cb99a2a656d2c69c163605b79d5
      message: "chore: fix fulcrum vendored smoketest"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: e1cb8b28059bee70b8048b17f750209211f07ddc
      message: "chore: add fulcrum bria to vendir (#3361)"
    "{latest}/modules/services/bitcoin/vendor/fulcrum/git-ref/ref":
      file_hash: b27cc1d03f4d4076147844cc2dbf1549b1120d0f
      from_commit: 6ad26ab2608520ec4f67ca33f4be30de9c2f9ee0
      message: "chore: bump fulcrum-chart to '6c6975563eb51bc14c3b5b547b9747446feb7cd2'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/Chart.yaml":
      file_hash: 4a795bf346843c41cc7ac0080f5703898efe4d1d
      from_commit: 6a51a486444830e0e8809575e2d10e22aa5b3784
      message: "chore: bump lnd-chart to '1cb5596eddf439a059fba60c3a0e064ec1436dc7'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/NOTES.txt":
      file_hash: cc5001568cc43cb99559ff9cf6bdd2a9a40bedfb
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/_helpers.tpl":
      file_hash: 869a8a7427e3c2756f1b73c1192e9e5e4055da06
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/api-service.yaml":
      file_hash: 4d1f9dcbf9f4d6c680c01cd1e6d028efe3b19796
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/configmap.yaml":
      file_hash: 2d40bd40f4ae627400d42937fe3274dde2b46b65
      from_commit: bfd033d54f00b91db5b6077f13a5bf1b492f2332
      message: "Bump lnd-chart to '59d2fc51826532209b13839c1a8cd8c948625792'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/export-secrets-configmap.yaml":
      file_hash: ecdacab6a6fb7af12615da25b15dc21574d2af93
      from_commit: edf7f4ebc638104d18f158e9894da69c4d52d841
      message: "chore: bump lnd-chart to '4915a6546f264eab08543e69f66754b170c78aee'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/lnd-pass-secret.yaml":
      file_hash: 39d458d3b9b09344a5a6b29b0006da9a543ff8eb
      from_commit: a5f8cb06a54c2d8412208a1ee7ccda0c2ed359a8
      message: "Bump lnd-chart to 'bfa3df50c71bd9735d0454970256811bf5041271'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/pvc.yaml":
      file_hash: 7f2ea3f148ff57c1d1e732d5277e565adc7a8e88
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/role.yaml":
      file_hash: c265b9704593a62e5db1ec5a02f778f9d35cc4a0
      from_commit: 08c5d072bf5a2eb49743bb887b381fa9da2a690f
      message: "chore: bump lnd-chart to 'bb42aacedf736874f903a93da6227e6dbac0246e'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/rolebinding.yaml":
      file_hash: 9eaf078e30b8cddfe33bc3a1bdcf36fcb0aef7d0
      from_commit: 08c5d072bf5a2eb49743bb887b381fa9da2a690f
      message: "chore: bump lnd-chart to 'bb42aacedf736874f903a93da6227e6dbac0246e'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/service-p2p.yml":
      file_hash: 301b53caf2e38e6b47e504fa9044a843241834c3
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/serviceaccount.yaml":
      file_hash: eb38e964bedd6827d0418ed6e6f4150dc4f3f02e
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/statefulset.yaml":
      file_hash: 5c9fca2b4437d626f91a97f87b3b373b5396f4cb
      from_commit: e5b6ca823804692950e8125c62bd9765dc92d0d2
      message: "chore: bump lnd-chart to '189c7557007dbd60e5457e0a4421b32ea5e2285d'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/templates/wallet-init-configmap.yaml":
      file_hash: e3138c2ead8da90a8fe80b5ccfa8c33efdd89bb4
      from_commit: d8ee67ef50dd889e33773da38118956a465aac07
      message: "Bump lnd-chart to '3ba371acd5af71b694c4db7240fac02af933f9f9'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/chart/values.yaml":
      file_hash: 37f009a20873102d680014389a1bc985ea705204
      from_commit: da888762fe94a3a9269a5130ca54f1d2eb737ce9
      message: "chore: bump lnd-chart to '7bddd07ae59eef508aa220621de60d4b18bb9666'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 6a494b61b66ad8e8acc62f9f544ffa63386cc7bd
      message: "Bump lnd-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/ci/tasks/lnd-smoketest.sh":
      file_hash: 25bb3b9e2256fbc6116ee01efbeb3ad13d96c9b8
      from_commit: c525dd0d7422a6fde7d36c92e672a0c6086b5c81
      message: "chore: bump lnd-chart to '9f71d90bb9b4e95cc5c8241bb9ac204209a786ee'"
    "{latest}/modules/services/bitcoin/vendor/lnd1/git-ref/ref":
      file_hash: 792fd5614cac6f31dbe862c8809186321817a7db
      from_commit: da888762fe94a3a9269a5130ca54f1d2eb737ce9
      message: "chore: bump lnd-chart to '7bddd07ae59eef508aa220621de60d4b18bb9666'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/Chart.yaml":
      file_hash: 4a795bf346843c41cc7ac0080f5703898efe4d1d
      from_commit: 6a51a486444830e0e8809575e2d10e22aa5b3784
      message: "chore: bump lnd-chart to '1cb5596eddf439a059fba60c3a0e064ec1436dc7'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/NOTES.txt":
      file_hash: cc5001568cc43cb99559ff9cf6bdd2a9a40bedfb
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/_helpers.tpl":
      file_hash: 869a8a7427e3c2756f1b73c1192e9e5e4055da06
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/api-service.yaml":
      file_hash: 4d1f9dcbf9f4d6c680c01cd1e6d028efe3b19796
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/configmap.yaml":
      file_hash: 2d40bd40f4ae627400d42937fe3274dde2b46b65
      from_commit: bfd033d54f00b91db5b6077f13a5bf1b492f2332
      message: "Bump lnd-chart to '59d2fc51826532209b13839c1a8cd8c948625792'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/export-secrets-configmap.yaml":
      file_hash: ecdacab6a6fb7af12615da25b15dc21574d2af93
      from_commit: edf7f4ebc638104d18f158e9894da69c4d52d841
      message: "chore: bump lnd-chart to '4915a6546f264eab08543e69f66754b170c78aee'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/lnd-pass-secret.yaml":
      file_hash: 39d458d3b9b09344a5a6b29b0006da9a543ff8eb
      from_commit: a5f8cb06a54c2d8412208a1ee7ccda0c2ed359a8
      message: "Bump lnd-chart to 'bfa3df50c71bd9735d0454970256811bf5041271'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/pvc.yaml":
      file_hash: 7f2ea3f148ff57c1d1e732d5277e565adc7a8e88
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/role.yaml":
      file_hash: c265b9704593a62e5db1ec5a02f778f9d35cc4a0
      from_commit: 08c5d072bf5a2eb49743bb887b381fa9da2a690f
      message: "chore: bump lnd-chart to 'bb42aacedf736874f903a93da6227e6dbac0246e'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/rolebinding.yaml":
      file_hash: 9eaf078e30b8cddfe33bc3a1bdcf36fcb0aef7d0
      from_commit: 08c5d072bf5a2eb49743bb887b381fa9da2a690f
      message: "chore: bump lnd-chart to 'bb42aacedf736874f903a93da6227e6dbac0246e'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/service-p2p.yml":
      file_hash: 301b53caf2e38e6b47e504fa9044a843241834c3
      from_commit: c01a804b9c59df94f9c809b40174ae76d61b543e
      message: "Bump lnd-chart to '933776d1ac0eae172a00273c966833959b451deb'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/serviceaccount.yaml":
      file_hash: eb38e964bedd6827d0418ed6e6f4150dc4f3f02e
      from_commit: 3f848c50f1be7083526d0a75f07f09f16b9b56f3
      message: Double sync charts for lnd1/2
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/statefulset.yaml":
      file_hash: 5c9fca2b4437d626f91a97f87b3b373b5396f4cb
      from_commit: e5b6ca823804692950e8125c62bd9765dc92d0d2
      message: "chore: bump lnd-chart to '189c7557007dbd60e5457e0a4421b32ea5e2285d'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/templates/wallet-init-configmap.yaml":
      file_hash: e3138c2ead8da90a8fe80b5ccfa8c33efdd89bb4
      from_commit: d8ee67ef50dd889e33773da38118956a465aac07
      message: "Bump lnd-chart to '3ba371acd5af71b694c4db7240fac02af933f9f9'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/chart/values.yaml":
      file_hash: 37f009a20873102d680014389a1bc985ea705204
      from_commit: da888762fe94a3a9269a5130ca54f1d2eb737ce9
      message: "chore: bump lnd-chart to '7bddd07ae59eef508aa220621de60d4b18bb9666'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 6a494b61b66ad8e8acc62f9f544ffa63386cc7bd
      message: "Bump lnd-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/ci/tasks/lnd-smoketest.sh":
      file_hash: 25bb3b9e2256fbc6116ee01efbeb3ad13d96c9b8
      from_commit: c525dd0d7422a6fde7d36c92e672a0c6086b5c81
      message: "chore: bump lnd-chart to '9f71d90bb9b4e95cc5c8241bb9ac204209a786ee'"
    "{latest}/modules/services/bitcoin/vendor/lnd2/git-ref/ref":
      file_hash: 792fd5614cac6f31dbe862c8809186321817a7db
      from_commit: da888762fe94a3a9269a5130ca54f1d2eb737ce9
      message: "chore: bump lnd-chart to '7bddd07ae59eef508aa220621de60d4b18bb9666'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/Chart.lock":
      file_hash: c580546eb1ea367b6e1f1de41e4bbabdff02d9b7
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/Chart.yaml":
      file_hash: 7982009fd63fc07d8868be3811fd553bad85df4a
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/Chart.yaml":
      file_hash: 713db3bf4c98b287b2ca624ccdb32a16fc8a2416
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/templates/_helpers.tpl":
      file_hash: 6b1c5900bce370f4c9635c125a8f66c76940932d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/templates/pvc.yml":
      file_hash: a2cb055c3f53ddfb3987abd24e932d6b69580338
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/templates/service.yaml":
      file_hash: 85c0e5c25d44e05b33c5a340fa11e64bb7bd079d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/templates/statefulset.yaml":
      file_hash: 718102b08b4dc0ac71d5591dd1d6360d8057108a
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/templates/tests/test-connection.yaml":
      file_hash: 2696a1d3aa61d4ba91129c58edbf933fb8de0591
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/charts/loopserver/values.yaml":
      file_hash: 84b6db9113520885d46a1404cd0a6439ae3c7218
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/_helpers.tpl":
      file_hash: 6bc8163f3d789f5f47ac9bb70b760db796d4a5c2
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/export-secrets-configmap.yaml":
      file_hash: 4167638d6c46c8cb38b10d7373d33823ad4ff21f
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/pvc.yaml":
      file_hash: 52da6bd770aca104d5aa6840308f6b7873e20ace
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/role.yaml":
      file_hash: c247b6087218b1fcd8d86b0626b96c20c4aa2658
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/rolebinding.yaml":
      file_hash: e7300e8cce5899e8100e7e457cc910015b0507b7
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/service.yaml":
      file_hash: 78c166fa7b69abf6f8af269a59409646c6f4d819
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/serviceaccount.yaml":
      file_hash: 5d2f9caaebe29ea01ab877206ff528eb3c539dc5
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/statefulset.yaml":
      file_hash: 260914d2706dde919b7febfa0a2a8ac2e85f710d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/templates/tests/test-connection.yaml":
      file_hash: 01062f48dbf95446b0149fec155cd71a5de04fe6
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/chart/values.yaml":
      file_hash: d5e7632ba80b83c2f259da3d9602c02089c4a66c
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/ci/tasks/loop-smoketest.sh":
      file_hash: 13312c333e42d6f6c08471871d1c6ea49734de25
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop1/git-ref/ref":
      file_hash: 261aef069f646ca40fe0d2939efff16f7bef3293
      from_commit: d2611e0b500377924ed4ce8f1cb8d5ba594bfaec
      message: "chore: bump stablesats-chart to '440a259d4c9bce62a668efdb6ac7767f0fa120c8'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/Chart.lock":
      file_hash: c580546eb1ea367b6e1f1de41e4bbabdff02d9b7
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/Chart.yaml":
      file_hash: 7982009fd63fc07d8868be3811fd553bad85df4a
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/Chart.yaml":
      file_hash: 713db3bf4c98b287b2ca624ccdb32a16fc8a2416
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/templates/_helpers.tpl":
      file_hash: 6b1c5900bce370f4c9635c125a8f66c76940932d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/templates/pvc.yml":
      file_hash: a2cb055c3f53ddfb3987abd24e932d6b69580338
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/templates/service.yaml":
      file_hash: 85c0e5c25d44e05b33c5a340fa11e64bb7bd079d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/templates/statefulset.yaml":
      file_hash: 718102b08b4dc0ac71d5591dd1d6360d8057108a
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/templates/tests/test-connection.yaml":
      file_hash: 2696a1d3aa61d4ba91129c58edbf933fb8de0591
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/charts/loopserver/values.yaml":
      file_hash: 84b6db9113520885d46a1404cd0a6439ae3c7218
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/_helpers.tpl":
      file_hash: 6bc8163f3d789f5f47ac9bb70b760db796d4a5c2
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/export-secrets-configmap.yaml":
      file_hash: 4167638d6c46c8cb38b10d7373d33823ad4ff21f
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/pvc.yaml":
      file_hash: 52da6bd770aca104d5aa6840308f6b7873e20ace
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/role.yaml":
      file_hash: c247b6087218b1fcd8d86b0626b96c20c4aa2658
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/rolebinding.yaml":
      file_hash: e7300e8cce5899e8100e7e457cc910015b0507b7
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/service.yaml":
      file_hash: 78c166fa7b69abf6f8af269a59409646c6f4d819
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/serviceaccount.yaml":
      file_hash: 5d2f9caaebe29ea01ab877206ff528eb3c539dc5
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/statefulset.yaml":
      file_hash: 260914d2706dde919b7febfa0a2a8ac2e85f710d
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/templates/tests/test-connection.yaml":
      file_hash: 01062f48dbf95446b0149fec155cd71a5de04fe6
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/chart/values.yaml":
      file_hash: d5e7632ba80b83c2f259da3d9602c02089c4a66c
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/ci/tasks/loop-smoketest.sh":
      file_hash: 13312c333e42d6f6c08471871d1c6ea49734de25
      from_commit: 7573132b7ddc4fb0acb36d3d09c9fe7c80584fef
      message: "chore: bump galoy-chart to '278ad344234dd4ef0a71026ec843d7f44246feb5'"
    "{latest}/modules/services/bitcoin/vendor/loop2/git-ref/ref":
      file_hash: 261aef069f646ca40fe0d2939efff16f7bef3293
      from_commit: d2611e0b500377924ed4ce8f1cb8d5ba594bfaec
      message: "chore: bump stablesats-chart to '440a259d4c9bce62a668efdb6ac7767f0fa120c8'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/Chart.yaml":
      file_hash: 4ab876c7dcc2c01008f159524df698035bed5438
      from_commit: cbe703a641ff2e4dacdc74d82dfd3c9041035ec6
      message: "chore: bump mempool-chart to 'e8465db4304102a353e8fbd7e7d55341d7f0803f'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/templates/_helpers.tpl":
      file_hash: ea7dc6cf5d471d4ef0a9b7b4f20cc4ca22184626
      from_commit: cbe703a641ff2e4dacdc74d82dfd3c9041035ec6
      message: "chore: bump mempool-chart to 'e8465db4304102a353e8fbd7e7d55341d7f0803f'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/templates/pvc.yaml":
      file_hash: 2b3956ecb20c45fd5b26eac80fa11c39ac73a8df
      from_commit: ef54b6e616984b05af06af089d49f9f7de510cea
      message: "chore: bump mempool-chart to '8765aa3f158969d2e57590468f39ba268802feff'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/templates/service.yaml":
      file_hash: f969ef3c5ec057ea5fc22b12dd60ea9795cdf14b
      from_commit: cbe703a641ff2e4dacdc74d82dfd3c9041035ec6
      message: "chore: bump mempool-chart to 'e8465db4304102a353e8fbd7e7d55341d7f0803f'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/templates/statefulset.yaml":
      file_hash: a65f6e884c2ed0db4b257fa9da164df667f37d55
      from_commit: b6a684e0eff4a3934a6f14d29d8002adea9d1ae5
      message: "chore: bump mempool-chart to 'bae51ce36675fcbe629f9025556525b3dd3abaf2'"
    "{latest}/modules/services/bitcoin/vendor/mempool/chart/values.yaml":
      file_hash: 2227c30d04b67e187454665727fedf71d9488f69
      from_commit: 53fcc10154f84157b4b24b51edda68ecd11ac749
      message: "chore: bump mempool-chart to '****************************************'"
    "{latest}/modules/services/bitcoin/vendor/mempool/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: cbe703a641ff2e4dacdc74d82dfd3c9041035ec6
      message: "chore: bump mempool-chart to 'e8465db4304102a353e8fbd7e7d55341d7f0803f'"
    "{latest}/modules/services/bitcoin/vendor/mempool/ci/tasks/mempool-smoketest.sh":
      file_hash: 95d665d424412d1c39b9fd27f981a24b50b30e94
      from_commit: cbe703a641ff2e4dacdc74d82dfd3c9041035ec6
      message: "chore: bump mempool-chart to 'e8465db4304102a353e8fbd7e7d55341d7f0803f'"
    "{latest}/modules/services/bitcoin/vendor/mempool/git-ref/ref":
      file_hash: 9cdbcb920c486f4c90746396afb2b0f77bf9365d
      from_commit: 53fcc10154f84157b4b24b51edda68ecd11ac749
      message: "chore: bump mempool-chart to '****************************************'"
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/Chart.yaml":
      file_hash: 3827edeb246e0a16a31834e9b64446a9c8eba320
      from_commit: 8b5f98c64b9c11a72da5e3023a83c61e6085d810
      message: "chore: bump rtl-chart to '473383770f0f83557a2f9b2a59b8c0e3b7b0282b'"
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/templates/_helpers.tpl":
      file_hash: 6be6c7d993bffd6a391f14e8d21aa6526cff4420
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/templates/configmap_and_secret.yaml":
      file_hash: 4bd45775fc46c8a47d068910b76f1c2064a3ecf6
      from_commit: 8f87d1b89c17497598438829bf6c84df940406c1
      message: "chore: bump rtl-chart to '23377c0ec0ec96cc3ac240f0eea08861201da819'"
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/templates/deployment.yaml":
      file_hash: 525f28a9f9090f775190c4f37557fa1f4ffb3fdd
      from_commit: 8b5f98c64b9c11a72da5e3023a83c61e6085d810
      message: "chore: bump rtl-chart to '473383770f0f83557a2f9b2a59b8c0e3b7b0282b'"
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/templates/service.yaml":
      file_hash: 5dce79bc2452b09b8e206379eb1da24c5ed4f5c6
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/templates/tests/test-connection.yaml":
      file_hash: 0accbce59393f67f9eef981f06fe97b7468991da
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/rtl/chart/values.yaml":
      file_hash: 0a933841417c6859511946311f902f8ee1b9e674
      from_commit: 8f87d1b89c17497598438829bf6c84df940406c1
      message: "chore: bump rtl-chart to '23377c0ec0ec96cc3ac240f0eea08861201da819'"
    "{latest}/modules/services/bitcoin/vendor/rtl/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 8793fe67b0d354a498816a1c5acaa3daa59627e1
      message: "Bump rtl-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/bitcoin/vendor/rtl/ci/tasks/rtl-smoketest.sh":
      file_hash: f304144a82cc6c1689aef60a18eacc549e1fefae
      from_commit: a911f95c106d0bf6d9b677ea020537fd10407ce3
      message: "chore: bump rtl-chart to '816bc5802ee94e0ffe52745054eff2a27b15717a'"
    "{latest}/modules/services/bitcoin/vendor/rtl/git-ref/ref":
      file_hash: 273ee87f0394d8e93d5f7f9803d41c2be2dbbc2a
      from_commit: 8b5f98c64b9c11a72da5e3023a83c61e6085d810
      message: "chore: bump rtl-chart to '473383770f0f83557a2f9b2a59b8c0e3b7b0282b'"
    "{latest}/modules/services/bitcoin/vendor/specter/chart/Chart.yaml":
      file_hash: 9a104552eb301b857ddf31b6a945d79f77397585
      from_commit: 34fa5f6a49f91eee96f7affe8184f35f7b7509dc
      message: "chore: bump specter-chart to 'bac4373d48116492846aa306818524b8f1d0cdc4'"
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/NOTES.txt":
      file_hash: 10899f2e8fd5e07680a5da7806965bb737e9e6d2
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/_helpers.tpl":
      file_hash: eacaae18a237edfcd642076cb725fe5981688f89
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/ingress.yaml":
      file_hash: 7186107bcce54137f60e1a72b0cde94a9772a57a
      from_commit: b9f3ecaa728f5ec66f1d5ba753cdcca6c974fa17
      message: "chore: bump specter-chart to '73f3b8d6008bff83843942de250b3c0586e0b445'"
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/nginx-ingress-networkpolicy.yaml":
      file_hash: 3303ef4b23611a97b9c7711d7cad4b298e90374e
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/pvc.yaml":
      file_hash: 1849a78bfdc47f68040a44ac5a9dc8a3785d2011
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/service.yaml":
      file_hash: 0976f30583a990946121d8a42a5d09759c892864
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/serviceaccount.yaml":
      file_hash: cf835858162f5fa3a6c931cc3bb843957d7a33eb
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/statefulset.yaml":
      file_hash: dff7eebeee8cf4be3d064332703d1f9f8da75f8d
      from_commit: 34fa5f6a49f91eee96f7affe8184f35f7b7509dc
      message: "chore: bump specter-chart to 'bac4373d48116492846aa306818524b8f1d0cdc4'"
    "{latest}/modules/services/bitcoin/vendor/specter/chart/templates/tests/test-connection.yaml":
      file_hash: 32122e746bf21261c6c92bcb6af0e0ef0daf502a
      from_commit: 6d67608a3d7b13a63db685d28efffb4fa523c5b1
      message: Include ci tasks in bitcoin vendored modules
    "{latest}/modules/services/bitcoin/vendor/specter/chart/values.yaml":
      file_hash: b0d14512e586d7bc347fde40d830a210e606c9a2
      from_commit: 13a294bffd92cfc1ffd7f8feb5cfaf05aa00fa37
      message: "Bump specter-chart to 'bec295d03555f49e4cb0c422116ca537b1494d92'"
    "{latest}/modules/services/bitcoin/vendor/specter/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 0db50d70e3a418df2c239d7669e748b6e3441724
      message: "Bump specter-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{latest}/modules/services/bitcoin/vendor/specter/ci/tasks/specter-smoketest.sh":
      file_hash: dd548e67a3ec93ced9c6e39467db0e4d0e51944e
      from_commit: a3031bee31ba1de8fcb1a1b686a8e29f2ee38ffa
      message: "chore: bump specter-chart to '6e24240a512fd30a94bf146001142a32c66f278d'"
    "{latest}/modules/services/bitcoin/vendor/specter/git-ref/ref":
      file_hash: ****************************************
      from_commit: 34fa5f6a49f91eee96f7affe8184f35f7b7509dc
      message: "chore: bump specter-chart to 'bac4373d48116492846aa306818524b8f1d0cdc4'"

