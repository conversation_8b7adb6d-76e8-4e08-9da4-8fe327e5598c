apiVersion: vendir.k14s.io/v1alpha1
kind: Config
directories:
- path: dev
  contents:
  - path: ./
    git:
      url: https://github.com/blinkbitcoin/blink.git
      ref: d5c84141898aba60790412f7b8ced2d8161c2237
    includePaths:
    - dev/**/*
    excludePaths:
    - dev/bin/**/*
    - dev/.gitignore
    newRootPath: dev
- path: ./graphql
  contents:
  - path: schemas/
    git:
      url: https://github.com/blinkbitcoin/blink.git
      ref: d5c84141898aba60790412f7b8ced2d8161c2237
    includePaths:
    - core/api/src/graphql/public/schema.graphql
    - core/api/src/graphql/admin/schema.graphql
    newRootPath: core/api/src/graphql
  - path: gql/
    git:
      url: https://github.com/blinkbitcoin/blink.git
      ref: d5c84141898aba60790412f7b8ced2d8161c2237
    includePaths:
    - bats/gql/**/*
    newRootPath: bats/gql
