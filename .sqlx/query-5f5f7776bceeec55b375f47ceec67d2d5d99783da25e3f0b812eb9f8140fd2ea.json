{"db_name": "PostgreSQL", "query": "INSERT INTO bria_profile_events (id, recorded_at, sequence, event_type, event) SELECT unnested.id, $1, unnested.sequence, unnested.event_type, unnested.event FROM UNNEST($2::UUID[], $3::INT[], $4::TEXT[], $5::JSONB[]) AS unnested(id, sequence, event_type, event)", "describe": {"columns": [], "parameters": {"Left": ["Timestamptz", "UuidArray", "Int4Array", "TextArray", "JsonbArray"]}, "nullable": []}, "hash": "5f5f7776bceeec55b375f47ceec67d2d5d99783da25e3f0b812eb9f8140fd2ea"}