{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT created_at, id FROM bria_profiles WHERE (COALESCE((created_at, id) > ($3, $2), $2 IS NULL)) ORDER BY created_at ASC, id ASC LIMIT $1) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.created_at asc, i.id asc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, false, false]}, "hash": "2b16510124df133641765ab557980ef5f6f12e5470ef2e4bcb20ca149efe76a5"}