# Default values for circles.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: gcr.io/galoy-org/blink-circles
  pullPolicy: IfNotPresent
  digest: sha256:17d0bc86e3f263714245667b2fd02d34a6e4225880240d72aaca83e7cf8697dc # METADATA:: repository=https://github.com/blinkbitcoin/blink-circles;commit_ref=0e585b4;app=circles;
  git_ref: 414c840
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""
podAnnotations: {}
podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 4000
nodeSelector: {}
tolerations: []
affinity: {}
bigQuery:
  dataset: "circles"
  welcomeProfileTable: "report_circles"
  welcomeProfileChangeTable: "report_circle_updates"
tracing:
  ## OTEL Exporter OTLP Endpoint
  ##
  otelExporterOtlpEndpoint: http://localhost:4318
  ## Prefix for tracing
  ##
  prefix: blink-circles-dev
notifications:
  host: notifications.galoy-staging-galoy.svc.local
  port: 6685
cron:
  runCronInGqlServer: "false"
  cronExpression: "* * * * *"
secrets:
  create: true
  bigqueryServiceAccountJson: ""
