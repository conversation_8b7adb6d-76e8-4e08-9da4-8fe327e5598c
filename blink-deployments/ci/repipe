#!/bin/bash

set -e

if [[ $(which ytt) == "" ]]; then
  echo "You will need to install ytt to repipe. https://carvel.dev/ytt/"
  exit 1
fi

if [[ $# != 1 ]]; then
  echo "Usage: ci/repipe <environment>"
  exit 1
fi

target="${FLY_TARGET:-ciblink}"
env="$1"

if ! [[ -f ci/environments/$env.yml ]]; then
  echo "Pipeline file not found at ci/environments/$env.yml"
  exit 1
fi

REPO_ROOT_DIR="$(dirname $( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null && pwd ))"

TMPDIR=""
TMPDIR=$(mktemp -d -t repipe.XXXXXX)
trap "rm -rf ${TMPDIR}" INT TERM QUIT EXIT

PIPELINE_FILE=ci/environments/$env.yml

ytt -f $PIPELINE_FILE \
  -f ci/shared.lib.yml \
  -f ci/bump.lib.yml \
  -f ci/infra.lib.yml \
  -f ci/workers.lib.yml \
  -f ci/bitcoin.lib.yml \
  -f ci/stablesats.lib.yml \
  -f ci/addons.lib.yml \
  -f ci/cala.lib.yml \
  -f ci/galoy.lib.yml \
  -f ci/monitoring.lib.yml \
  -f ci/galoy-deps.lib.yml \
  -f ci/blink-addons.lib.yml \
  -f ci/values.yml > ${TMPDIR}/pipeline.yml

pipeline_name=$(sed -n 's/^#! @config\/pipeline.*=.*\"\(.*\)\"/\1/p' ci/environments/$env.yml)
team_name=$(sed -n 's/^#! @config\/team.*=.*\"\(.*\)\"/\1/p' ci/environments/$env.yml)

if [[ $pipeline_name == "" ]]; then
  echo "Pipeline name not found in ci/environments/$env.yml, kindly add it as:"
  echo "#! @config/pipeline = \"<pipeline name>\""
  exit 1
fi

if [[ $team_name == "" ]]; then
  echo "Team name not found in ci/environments/$env.yml, kindly add it as:"
  echo "#! @config/team = \"<team name>\""
  exit 1
fi

echo "Updating pipeline '${pipeline_name}' using team '${team_name}' @ ${target}"

fly -t ${target} set-pipeline --team=$team_name -p $pipeline_name -c ${TMPDIR}/pipeline.yml
fly -t ${target} unpause-pipeline --team=$team_name -p $pipeline_name
