variable "smoketest_kubeconfig" {
  sensitive = true
}

variable "secrets" {
  sensitive = true
}

module "shared" {
  source = "../shared"
}

module "galoy_deps" {
  source = "../../../modules/services/galoy-deps/"

  secrets = var.secrets

  name_prefix          = module.shared.name_prefix
  smoketest_kubeconfig = var.smoketest_kubeconfig

  letsencrypt_issuer_email = "<EMAIL>"
  trace_sample_pct         = 80

  unsupported_countries = module.shared.unsupported_countries
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/services/galoy-deps"
  }
}
