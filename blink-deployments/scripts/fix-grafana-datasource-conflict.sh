#!/bin/bash

# Script to fix Grafana data source 409 conflict error
# This script will find the existing data source ID and import it into Terraform state

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "modules/services/monitoring/dashboards.tf" ]; then
    print_error "This script must be run from the repository root directory"
    exit 1
fi

# Get environment from command line or detect from current directory
ENVIRONMENT=${1:-""}
if [ -z "$ENVIRONMENT" ]; then
    if [ -d "gcp/galoy-staging" ]; then
        ENVIRONMENT="galoy-staging"
    elif [ -d "gcp/galoy-bbw" ]; then
        ENVIRONMENT="galoy-bbw"
    else
        print_error "Could not detect environment. Please specify: $0 <environment>"
        exit 1
    fi
fi

print_status "Working with environment: $ENVIRONMENT"

# Check if the environment directory exists
if [ ! -d "gcp/$ENVIRONMENT/monitoring" ]; then
    print_error "Environment directory gcp/$ENVIRONMENT/monitoring not found"
    exit 1
fi

# Change to the monitoring directory
cd "gcp/$ENVIRONMENT/monitoring"

print_status "Getting Terraform outputs to find Grafana URL..."

# Get Grafana URL from Terraform outputs
GRAFANA_URL=""
if command -v tofu >/dev/null 2>&1; then
    GRAFANA_URL=$(tofu output -raw grafana_url 2>/dev/null || echo "")
else
    GRAFANA_URL=$(terraform output -raw grafana_url 2>/dev/null || echo "")
fi

if [ -z "$GRAFANA_URL" ]; then
    print_warning "Could not get Grafana URL from Terraform outputs"
    print_status "Please provide the Grafana URL manually:"
    read -p "Grafana URL (e.g., https://grafana.example.com): " GRAFANA_URL
fi

print_status "Using Grafana URL: $GRAFANA_URL"

# Get Grafana admin password
print_status "Getting Grafana admin password..."
GRAFANA_PASSWORD=""
if kubectl get secret grafana-creds -n ${ENVIRONMENT}-monitoring >/dev/null 2>&1; then
    GRAFANA_PASSWORD=$(kubectl get secret grafana-creds -n ${ENVIRONMENT}-monitoring -o jsonpath='{.data.admin-password}' | base64 -d)
    print_status "Retrieved password from Kubernetes secret"
else
    print_warning "Could not get password from Kubernetes. Please provide it manually:"
    read -s -p "Grafana admin password: " GRAFANA_PASSWORD
    echo
fi

# Check if Grafana is accessible
print_status "Checking Grafana accessibility..."
if ! curl -f -s -k --max-time 10 "$GRAFANA_URL/api/health" >/dev/null; then
    print_error "Grafana is not accessible at $GRAFANA_URL"
    print_status "Please ensure Grafana is running and accessible"
    exit 1
fi

print_status "Grafana is accessible"

# Get existing data source ID
print_status "Looking for existing 'Google Cloud Monitoring' data source..."
DATASOURCE_ID=$(curl -s -k -u "admin:$GRAFANA_PASSWORD" \
    "$GRAFANA_URL/api/datasources" | \
    jq -r '.[] | select(.name == "Google Cloud Monitoring") | .id' 2>/dev/null || echo "")

if [ -z "$DATASOURCE_ID" ] || [ "$DATASOURCE_ID" = "null" ]; then
    print_error "No existing data source found with name 'Google Cloud Monitoring'"
    print_status "This might mean:"
    print_status "1. The data source has a different name"
    print_status "2. The data source doesn't exist yet"
    print_status "3. There's an authentication issue"
    
    print_status "Listing all existing data sources:"
    curl -s -k -u "admin:$GRAFANA_PASSWORD" "$GRAFANA_URL/api/datasources" | jq -r '.[] | "\(.id): \(.name) (\(.type))"' || true
    exit 1
fi

print_status "Found existing data source with ID: $DATASOURCE_ID"

# Import the data source
print_status "Importing data source into Terraform state..."
if command -v tofu >/dev/null 2>&1; then
    CMD="tofu"
else
    CMD="terraform"
fi

if $CMD import module.monitoring.grafana_data_source.stackdriver "$DATASOURCE_ID"; then
    print_status "Successfully imported data source!"
else
    print_error "Failed to import data source"
    print_status "You can try manually with:"
    print_status "$CMD import module.monitoring.grafana_data_source.stackdriver $DATASOURCE_ID"
    exit 1
fi

# Check if there are any differences
print_status "Checking for configuration differences..."
if $CMD plan -target=module.monitoring.grafana_data_source.stackdriver | grep -q "No changes"; then
    print_status "Perfect! No configuration differences found."
else
    print_warning "There are some configuration differences. Running plan:"
    $CMD plan -target=module.monitoring.grafana_data_source.stackdriver
    print_status "You may need to apply these changes with:"
    print_status "$CMD apply -target=module.monitoring.grafana_data_source.stackdriver"
fi

print_status "Data source conflict resolution complete!"
print_status "You can now run 'terraform apply' or 'tofu apply' normally."
