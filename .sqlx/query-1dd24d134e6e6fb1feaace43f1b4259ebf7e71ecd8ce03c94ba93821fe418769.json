{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT id FROM bria_profiles WHERE (COALESCE(id < $2, true)) ORDER BY id DESC LIMIT $1) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.id desc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "1dd24d134e6e6fb1feaace43f1b4259ebf7e71ecd8ce03c94ba93821fe418769"}