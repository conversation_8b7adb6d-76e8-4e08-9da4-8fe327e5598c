fullnameOverride: ""
nameOverride: ""
bria:
  devDaemon:
    enabled: false
    bitcoindSignerEndpoint: https://bitcoind-onchain.default.svc.cluster.local:18543
  resources: {}
  tracing:
    host: localhost
    port: 4317
    serviceName: bria-dev
  app:
    blockchain:
      network: regtest
      electrumUrl: fulcrum.default.svc.cluster.local
    fees:
      mempoolSpace:
        url: https://mempool.space
        numberOfRetries: 3
    security:
      blockedAddresses: []
    deprecatedEncryptionKey:
      nonce: null
      key: null
  db:
    poolSize: 20
  admin:
    service:
      type: ClusterIP
      port: 2743
  api:
    service:
      type: ClusterIP
      port: 2742
      staticIP: ""
      annotations: {}
  labels: {}
  image:
    repository: us.gcr.io/galoy-org/bria
    digest: "sha256:3c2226df0fc242a4fd7e1767a5c9eb9ea7f55dbf2d4271ea05a42bf0d99a6f05" # METADATA:: repository=https://github.com/blinkbitcoin/bria;commit_ref=236a9cb;app=bria;
    git_ref: "55654e2"
  replicas: 2
  annotations:
  secrets:
    create: true
    pgCon: ""
    signerEncryptionKey: ""
    annotations:
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: bria
    password: bria
    database: bria
resources: {}
