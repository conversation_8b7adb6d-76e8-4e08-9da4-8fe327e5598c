[package]
name = "bria"
version = "0.1.113-dev"
edition = "2021"

[features]

fail-on-warnings = []

[dependencies]
# es-entity = { git = "https://github.com/galoymoney/cala.git", branch = "main" }
es-entity = "0.5.1"
sqlx-ledger = { version = "0.11.5", features = ["otel"] }

anyhow = "1.0.82"
bitcoincore-rpc = "0.17.0"
clap = { version = "4.5", features = ["derive", "env"] }
chrono = { version = "0.4.38", features = [
    "clock",
    "serde",
], default-features = false }
derive_builder = "0.20.0"
serde = { version = "1.0.203", features = ["derive"] }
serde_json = "1.0.117"
serde_yaml = "0.9.32"
sqlx = { version = "0.8.3", features = [
    "runtime-tokio-rustls",
    "postgres",
    "rust_decimal",
    "uuid",
    "chrono",
] }
sqlxmq = { git = "https://github.com/HyperparamAI/sqlxmq", rev = "52c3daf6af55416aefa4b1114e108f968f6c57d4", default-features = false, features = [
    "runtime-tokio-rustls",
] }
tokio = { version = "1.37", features = ["rt-multi-thread", "macros"] }
tokio-stream = { version = "0.1.15", features = ["sync"] }
tonic = "0.11.0"
tonic-health = "0.11.0"
rust_decimal = "1.35"
prost = "0.12"
prost-wkt-types = { version = "0.5", features = ["vendored-protoc"] }
rust_decimal_macros = "1.34"
rusty-money = "0.4.1"
thiserror = "1.0.61"
uuid = { version = "1.8.0", features = ["serde", "v4"] }
futures = "0.3.30"
url = "2.5.2"
rand = "0.8.5"
bdk = "0.29.0"
opentelemetry = { version = "0.27.0" }
opentelemetry_sdk = { version = "0.27.0", features = ["rt-tokio"] }
opentelemetry-otlp = { version = "0.27.0", features = [
    "http-proto",
    "reqwest-client",
] }
tracing = "0.1.40"
tracing-opentelemetry = "0.28.0"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }
serde_with = "3.8.1"
electrum-client = "0.18.0"
reqwest = { version = "0.12.5", default-features = false, features = [
    "json",
    "rustls-tls",
] }
async-trait = "0.1.80"
base64 = "0.22.1"
tempfile = "3.10.1"
hex = "0.4.3"
chacha20poly1305 = "0.10.1"
regex = "1.10.4"
miniscript = "10.0"
reqwest-retry = "0.5.0"
reqwest-middleware = "0.3"
tonic_lnd = { version = "0.2.0", package = "fedimint-tonic-lnd", features = [
    "lightningrpc",
] }

[dev-dependencies]
serial_test = "*"

[build-dependencies]
protobuf-src = { version = "1.1.0" }
tonic-build = { version = "0.11.0", features = ["prost"] }
