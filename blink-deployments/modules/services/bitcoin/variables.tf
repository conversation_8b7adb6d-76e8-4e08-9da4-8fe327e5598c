variable "name_prefix" {}
variable "gcp_project" {}
variable "gcp_region" {}

variable "bitcoin_network" {}
variable "specter_dns" {}
variable "lnd2_image_tag" { default = "" }
variable "lnd1_public_ip" {}
variable "lnd2_public_ip" {}
variable "lnd_min_channel_size" { default = "10000000" }
variable "lnd_base_fee" { default = "0" }
variable "lnd_fee_rate" { default = "500" }
variable "lnd1_alias" { default = "default" }
variable "lnd2_alias" { default = "default" }
variable "bria_replicas" { default = 2 }
variable "fulcrum_endpoint" { default = "default" }

variable "okex_exchange_address" {}

variable "bitcoind_pvc_size" { default = "1000Gi" }
variable "bitcoind2_pvc_size" { default = "1000Gi" }

variable "sync_from_bucket" { default = true }

variable "bitcoind_onchain_sync_from_bucket" { default = true }
variable "bitcoind_onchain_pvc_size" { default = "1000Gi" }

variable "bria_cold_wallet_name" { default = "default" }

variable "lnd1_pass" {
  default   = ""
  sensitive = true
}
variable "lnd1_existing_claim" {
  default = ""
}
variable "lnd2_existing_claim" {
  default = ""
}
variable "lnd1_storage_class" {
  default = ""
}
variable "lnd2_storage_class" {
  default = ""
}

variable "secrets" { sensitive = true }

variable "ha_pg" { default = true }
variable "pg_tier" { default = "db-custom-2-3840" }

locals {
  name_prefix = var.name_prefix
  gcp_project = var.gcp_project
  gcp_region  = var.gcp_region
  vpc_name    = "${local.name_prefix}-vpc"
  ha_pg       = var.ha_pg
  pg_tier     = var.pg_tier

  bitcoin_namespace   = "${var.name_prefix}-bitcoin"
  smoketest_namespace = "${var.name_prefix}-smoketest"
  otel_namespace      = "${local.name_prefix}-otel"
  specter_dns         = var.specter_dns
  bitcoin_network     = var.bitcoin_network
  rpc_port            = var.bitcoin_network == "mainnet" ? 8332 : 38332
  p2p_port            = var.bitcoin_network == "mainnet" ? 8333 : 38333

  lnd_serving_bitcoind          = "bitcoind"
  bitcoind_endpoint             = "${local.lnd_serving_bitcoind}:${local.rpc_port}"
  bitcoind_zmq_block_endpoint   = "tcp://${local.lnd_serving_bitcoind}:28332"
  bitcoind_zmq_tx_endpoint      = "tcp://${local.lnd_serving_bitcoind}:28333"
  bitcoind_rpc_pass_secret_name = "${local.lnd_serving_bitcoind}-rpcpassword"

  lnd2_serving_bitcoind          = "bitcoind2"
  bitcoind2_endpoint             = "${local.lnd2_serving_bitcoind}:${local.rpc_port}"
  bitcoind2_zmq_block_endpoint   = "tcp://${local.lnd2_serving_bitcoind}:28332"
  bitcoind2_zmq_tx_endpoint      = "tcp://${local.lnd2_serving_bitcoind}:28333"
  bitcoind2_rpc_pass_secret_name = "${local.lnd2_serving_bitcoind}-rpcpassword"

  onchain_bitcoind_endpoint = "https://bitcoind-onchain:${local.rpc_port}"

  bitcoind_pvc_size  = var.bitcoind_pvc_size
  bitcoind2_pvc_size = var.bitcoind2_pvc_size

  sync_from_bucket = var.sync_from_bucket

  bitcoind_onchain_pvc_size         = var.bitcoind_onchain_pvc_size
  bitcoind_onchain_sync_from_bucket = var.bitcoind_onchain_sync_from_bucket

  lnd1_alias             = var.lnd1_alias == "default" ? "${var.name_prefix}-node" : var.lnd1_alias
  lnd2_alias             = var.lnd2_alias == "default" ? "${var.name_prefix}-node" : var.lnd2_alias
  lnd1_internal_ip       = "********"
  lnd2_internal_ip       = "********"
  bria_internal_ip       = "********"
  bria_endpoint          = "${local.bria_internal_ip}:2742"
  bria_admin_internal_ip = "********"
  bria_admin_endpoint    = "${local.bria_admin_internal_ip}:2743"
  fulcrum_endpoint       = var.fulcrum_endpoint == "default" ? "fulcrum.${local.bitcoin_namespace}.svc.cluster.local:50001" : var.fulcrum_endpoint
  lnd1_public_ip         = var.lnd1_public_ip
  lnd1_pass              = var.lnd1_pass
  lnd2_public_ip         = var.lnd2_public_ip
  lnd1_existing_claim    = var.lnd1_existing_claim
  lnd2_existing_claim    = var.lnd2_existing_claim
  lnd_min_channel_size   = var.lnd_min_channel_size
  lnd_base_fee           = var.lnd_base_fee
  lnd_fee_rate           = var.lnd_fee_rate
  lnd1_image_tag         = ""
  lnd2_image_tag         = var.lnd2_image_tag

  lnd1_storage_class = var.lnd1_storage_class
  lnd2_storage_class = var.lnd2_storage_class

  okex_exchange_address       = var.okex_exchange_address
  stablesats_withdraw_address = "${local.name_prefix}-stablesats"
  bria_replicas               = var.bria_replicas
  bria_admin_api_key          = jsondecode(var.secrets).bria_admin_api_key
  bria_signer_encryption_key  = jsondecode(var.secrets).bria_signer_encryption_key
  bria_account_name           = "${local.name_prefix}-main"
  bria_legacy_hot_wallet_name = "${local.name_prefix}-legacy-hot"
  bria_hot_wallet_name        = "${local.name_prefix}-hot"
  bria_cold_wallet_name       = var.bria_cold_wallet_name == "default" ? "${local.name_prefix}-cold" : var.bria_cold_wallet_name
  bria_pg_instance_name       = "${local.name_prefix}-bria"
  bria_databases              = ["bria"]
  big_query_viewers           = ["serviceAccount:<EMAIL>"]

  otel_host                 = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local"
  tracing_bria_service_name = "${local.name_prefix}-bria"

  bitcoind_signer_descriptor = jsondecode(var.secrets).bitcoind_signer.descriptor
  bitcoind_signer_xpub       = jsondecode(var.secrets).bitcoind_signer.xpub
}
