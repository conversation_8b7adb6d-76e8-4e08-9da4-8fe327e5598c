---
version: 270
current:
  head_commit: efe842644097b301e6cff948772c592a80812d41
  files:
    "{latest}/gcp/galoy-staging/blink-addons/main.tf":
      file_hash: 81c2a67abba2f1ead42e60549d1e94b7a6bd0877
      from_commit: efe842644097b301e6cff948772c592a80812d41
      message: "chore: switch staging to staging.blink.sv, remove staging.galoy.io (#8074)"
    "{latest}/gcp/galoy-staging/shared/main.tf":
      file_hash: ddf53cabe701956c5e380b8750656c8870b708b6
      from_commit: efe842644097b301e6cff948772c592a80812d41
      message: "chore: switch staging to staging.blink.sv, remove staging.galoy.io (#8074)"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{latest}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{latest}/modules/services/blink-addons/blink-circles-values.yml.tmpl":
      file_hash: ****************************************
      from_commit: 2308cda888f6a529f4ad5498623931de463befa8
      message: "Revert \"revert: \"chore: inject notifications config to circles (#6147)\" (#6171)\" (#6217)"
    "{latest}/modules/services/blink-addons/blink-circles.tf":
      file_hash: ****************************************
      from_commit: 0ad42f055e5b27431b8b26e022f7dd100efbb835
      message: "chore: updating tf library for hydra (#6532)"
    "{latest}/modules/services/blink-addons/blink-fiat-values.yml.tmpl":
      file_hash: 84c40b203293ad33c57a8457f2f3615b9d61ab68
      from_commit: e004c45247325dac7c8230b47120ef16ee8a6e68
      message: "chore: update fiat vars (#6439)"
    "{latest}/modules/services/blink-addons/blink-fiat.tf":
      file_hash: 8789118ed634c5575b83233686ffa55a40f96de8
      from_commit: e004c45247325dac7c8230b47120ef16ee8a6e68
      message: "chore: update fiat vars (#6439)"
    "{latest}/modules/services/blink-addons/blink-kyc-values.yml.tmpl":
      file_hash: 7e5f93540c0f531a24c83e81ac964702898e4169
      from_commit: eed21e054c6b38442ecb100a30076e699bac20cf
      message: "chore: add kyc notifications config (#6294)"
    "{latest}/modules/services/blink-addons/blink-kyc.tf":
      file_hash: e30066629420f248001d3619b9bb27932f30ab03
      from_commit: b69386f28720299a7f71b41bb747fc6bdf9d6ffc
      message: "chore: remove migration related settings"
    "{latest}/modules/services/blink-addons/main.tf":
      file_hash: 2e90054a7d16bd6a202b569565b7153217ebfa21
      from_commit: 65097b215903c7107814842b1d76913967f7f507
      message: "refactor: move fiat to dedicated tf file"
    "{latest}/modules/services/blink-addons/variables.tf":
      file_hash: fe02ccd3dad368c835de418d3475fd3256416dcb
      from_commit: 4dd0166f634551619ad6855d16d7325af566e8c9
      message: "chore: update blink fiat buy fee to 2%"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/Chart.yaml":
      file_hash: 10cb54eb751e4917535eb20dce52101874e955bd
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/README.md":
      file_hash: bfe8ad932713242d810f26626694265ae9a8ca6d
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/example.values.yaml":
      file_hash: 4bb1fe7f2dc062c157e7a689a074efd6e032e220
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/backoffice-v2/templates/configmap.yaml":
      file_hash: a3fdd749915f9c161cb0ed292294802bc41248d5
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/backoffice-v2/templates/deployment.yaml":
      file_hash: fb23c3629dfd002301eaf378fbdc4a2f48cc5de7
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/backoffice-v2/templates/service.yaml":
      file_hash: 1f2943a7b3d9d4b22ce8531da58c0f6db7282c2f
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/headless-example/templates/configmap.yaml":
      file_hash: 146237879c86b90649efdf4ade98c25f427b8614
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/headless-example/templates/deployment.yaml":
      file_hash: 38212ae1537f195eb8b2a721eed7e4a52ded7397
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/headless-example/templates/service.yaml":
      file_hash: 9bb78370ed209aa40710e2b248e66b70a237a91f
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/websocket-service/templates/configmap.yaml":
      file_hash: 424a7ac8ad6aa08ccc817d192e92dad3b2bf8f4e
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/websocket-service/templates/deployment.yaml":
      file_hash: f6848e669c483effa87fa535ea3d8517becceb08
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/websocket-service/templates/service.yaml":
      file_hash: 5f9ec0a0748ecc704c9b27223354631ec0a53e1b
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflow-dashboard/templates/configmap.yaml":
      file_hash: b5d12a8642ff4692ba62ad11fd93c98c48620bf1
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflow-dashboard/templates/deployment.yaml":
      file_hash: aba3c601a2d8ba9c0d614dca74ecdc0c8361c3b0
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflow-dashboard/templates/service.yaml":
      file_hash: 6f33032b7b79830056594779ee5a33422b76ab22
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflows-service/templates/configmap.yaml":
      file_hash: ec583f88362bdd42ac055794cbc25a723906b9a8
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflows-service/templates/deployment.yaml":
      file_hash: 03b5abd52e61ffc2284ef8010f2d24d5b18d0c4d
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/services/workflows-service/templates/service.yaml":
      file_hash: 20c609d3c826a59d0b0f9feeac803c6cb11f2ec9
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/chart/templates/import.yaml":
      file_hash: 72a2f978e5118e6ed304a75becf754848cdad5ea
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/ci/tasks/ballerine-smoketest.sh":
      file_hash: aafcb78dff7b5e222b99ac938dd7804d13a63579
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/ballerine/git-ref/ref":
      file_hash: 40fd6f041d88a45d0ed1337c3fb0f2928946c0c6
      from_commit: 18d01ca2f43a9d42cc43e2a4f64fee52ae6441ad
      message: "Bump ballerine-chart to 'a7e83e81c403f1a77de88a076539c362362560a6'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/Chart.yaml":
      file_hash: 2d96566af3639d5ea1701af8cfe1f5d43c6b72a0
      from_commit: 6b7d7fe99300aff47707e0895a12a0abf4be754a
      message: "Bump blink-fiat-chart to 'a4bdf436fac8498280369be2b4bdecf51a38c3f3'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/templates/_helpers.tpl":
      file_hash: 6dfcfae9ea72f4294ea24b58bf410e36f99094c2
      from_commit: 46e53666afdbe0b17f676d8a7ec8db7ebe8b1ad8
      message: "Bump blink-fiat-chart to 'fc5fa4311dc9c5249fc0614f32cf78d4fcda63a1'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/templates/deployment.yaml":
      file_hash: 08976f3308f5b44d7f8cbc8a45d86ebfb681e6c4
      from_commit: 3581f6d460c6e48ba53bb0bee25175149349ac10
      message: "Bump blink-fiat-chart to '818aef7c0e546548aca16d9881ee25cb36e7d977'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/templates/ingress.yaml":
      file_hash: b2a357823e3015e8aff9697153f76bed87784368
      from_commit: 46e53666afdbe0b17f676d8a7ec8db7ebe8b1ad8
      message: "Bump blink-fiat-chart to 'fc5fa4311dc9c5249fc0614f32cf78d4fcda63a1'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/templates/secrets.yaml":
      file_hash: a6b629b82bf9be40911739067bb6fc9388c98d1b
      from_commit: a461235670df4d8996d33c2b75812f471a3ed4dc
      message: "Bump blink-fiat-chart to 'cc3f0a15f910c7b9964623792a0cec1c0877a32b'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/templates/service.yaml":
      file_hash: ac2d35f8c728375ff1d9b706ec14a2297a4fe1bd
      from_commit: 46e53666afdbe0b17f676d8a7ec8db7ebe8b1ad8
      message: "Bump blink-fiat-chart to 'fc5fa4311dc9c5249fc0614f32cf78d4fcda63a1'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/chart/values.yaml":
      file_hash: 7340db14675a4b4668a491e7fcb2c23a7663da45
      from_commit: 6b7d7fe99300aff47707e0895a12a0abf4be754a
      message: "Bump blink-fiat-chart to 'a4bdf436fac8498280369be2b4bdecf51a38c3f3'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/ci/tasks/blink-fiat-smoketest.sh":
      file_hash: f50820c4bcdc84a789095e267e6e79d3376bb1c1
      from_commit: 46e53666afdbe0b17f676d8a7ec8db7ebe8b1ad8
      message: "Bump blink-fiat-chart to 'fc5fa4311dc9c5249fc0614f32cf78d4fcda63a1'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 46e53666afdbe0b17f676d8a7ec8db7ebe8b1ad8
      message: "Bump blink-fiat-chart to 'fc5fa4311dc9c5249fc0614f32cf78d4fcda63a1'"
    "{latest}/modules/services/blink-addons/vendor/blink-fiat/git-ref/ref":
      file_hash: a13fa1f7f2602797222dcd747ae60675b84cd476
      from_commit: 6b7d7fe99300aff47707e0895a12a0abf4be754a
      message: "Bump blink-fiat-chart to 'a4bdf436fac8498280369be2b4bdecf51a38c3f3'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/Chart.yaml":
      file_hash: 745504763491d04d7e70c5a2feb5adcb5d8e3557
      from_commit: 2aefe4dfcdba8bd0f3be1a8c8852380b7f0e03f0
      message: "Bump blink-kyc-chart to 'e3ecd9dd83c1b22b1fe05fc39177ae2bfdbcc7c2'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/subgraph/schema.graphql":
      file_hash: 45d2d617933801f60a9d4bff2d90e62dcc62976f
      from_commit: c1f615263fc4da9a63d8b2c8b54ecab8125f0ba5
      message: "Bump blink-kyc-chart to '086aff5d8c73ff2bbb72d77c28e53127e78d2f55'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/templates/_helpers.tpl":
      file_hash: 4ac390e4396a960f2a7707681b21bf169a864bd7
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/templates/deployment.yaml":
      file_hash: baf193d4003d40413fa961f8819fdc73ee1ae427
      from_commit: 97ee732752d6494825eee95bc0d1f43b66837ef4
      message: "Bump blink-kyc-chart to '24defcdade2572851cc4f86dc041bd4a5d261a9a'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/templates/ingress.yaml":
      file_hash: 57dca9f93c1954b9f4903adb51509d186cedd118
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/templates/secrets.yaml":
      file_hash: c9761fb6e9ae3d2cf28bfb5ecc951c230b635edc
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/templates/service.yaml":
      file_hash: 06f4c6bd7d06de491bc4cf5d2683e98e932d00b1
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/chart/values.yaml":
      file_hash: 192034b2c5ea19fbf0e065a0af26f1cb862da522
      from_commit: 2aefe4dfcdba8bd0f3be1a8c8852380b7f0e03f0
      message: "Bump blink-kyc-chart to 'e3ecd9dd83c1b22b1fe05fc39177ae2bfdbcc7c2'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/ci/tasks/blink-kyc-smoketest.sh":
      file_hash: ****************************************
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 78f69d314ffc67ff40d3ba0c2fb495a16b0d0d02
      message: "Bump blink-kyc-chart to '61b573c5e4556c85eaa7c78948821450059ec603'"
    "{latest}/modules/services/blink-addons/vendor/blink-kyc/git-ref/ref":
      file_hash: ****************************************
      from_commit: 03b50b828ea6721bfc9fb4f6e841a5f5dadba1a2
      message: "Bump blink-kyc-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/Chart.yaml":
      file_hash: ****************************************
      from_commit: 1ba33ea19ede5784a7b6c7c1c3c4066701b32c8f
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/subgraph/schema.graphql":
      file_hash: bc47fb9e25180fd3cc4fc2597d500ccb9b7384d9
      from_commit: d77d1f5c5b124c4b52bfe7da72a4172eb92fcd39
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/templates/_helpers.tpl":
      file_hash: 0eaeb5764a5b01bd159822686d50e526275eb616
      from_commit: 123427883749c807666b5a177e6b57cf3b8a0060
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/templates/deployment.yaml":
      file_hash: 0979daa87d0218c5138cd8ceeb1e73b1df10e553
      from_commit: 61c9b158bca49ca75fef8260a39b7f74a12a84af
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/templates/secrets.yaml":
      file_hash: c2f7935f55e8b17e07c171a2c922adbf7a80f21a
      from_commit: 61c9b158bca49ca75fef8260a39b7f74a12a84af
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/templates/service.yaml":
      file_hash: eb631569c9b735af11645aa1781cac773ff30061
      from_commit: 65215113eac0adc3a89b6647797a45e9051428ad
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/chart/values.yaml":
      file_hash: ****************************************
      from_commit: 1ba33ea19ede5784a7b6c7c1c3c4066701b32c8f
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/ci/tasks/circles-smoketest.sh":
      file_hash: ****************************************
      from_commit: 7e243ab7ae118b7111d23aa516473460955d6d29
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 7e243ab7ae118b7111d23aa516473460955d6d29
      message: "Bump circles-chart to '****************************************'"
    "{latest}/modules/services/blink-addons/vendor/circles/git-ref/ref":
      file_hash: ****************************************
      from_commit: 1ba33ea19ede5784a7b6c7c1c3c4066701b32c8f
      message: "Bump circles-chart to '****************************************'"

