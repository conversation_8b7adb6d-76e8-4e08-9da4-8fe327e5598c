{"db_name": "PostgreSQL", "query": "SELECT i.id AS \"id: PayoutQueueId\", e.sequence, e.event, e.recorded_at FROM bria_payout_queues i JOIN bria_payout_queue_events e ON i.id = e.id WHERE i.id = ANY($1) ORDER BY i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "id: PayoutQueueId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["UuidArray"]}, "nullable": [false, false, false, false]}, "hash": "d6784f313ea771f1d714d4a2f48dba8a541d306a675c9229ebb32878f574d01d"}