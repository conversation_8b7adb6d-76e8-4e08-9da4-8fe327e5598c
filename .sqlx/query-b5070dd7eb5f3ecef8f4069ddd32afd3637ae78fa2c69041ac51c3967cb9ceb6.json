{"db_name": "PostgreSQL", "query": "SELECT i.id AS \"id: ProfileId\", e.sequence, e.event, e.recorded_at FROM bria_profiles i JOIN bria_profile_events e ON i.id = e.id WHERE i.id = ANY($1) ORDER BY i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["UuidArray"]}, "nullable": [false, false, false, false]}, "hash": "b5070dd7eb5f3ecef8f4069ddd32afd3637ae78fa2c69041ac51c3967cb9ceb6"}