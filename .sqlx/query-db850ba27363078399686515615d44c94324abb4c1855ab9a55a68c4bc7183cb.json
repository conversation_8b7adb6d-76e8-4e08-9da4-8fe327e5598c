{"db_name": "PostgreSQL", "query": "INSERT INTO bria_payout_queue_events (id, recorded_at, sequence, event_type, event) SELECT $1, $2, ROW_NUMBER() OVER () + $3, unnested.event_type, unnested.event FROM UNNEST($4::text[], $5::jsonb[]) AS unnested(event_type, event)", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "Timestamptz", "Int8", "TextArray", "JsonbArray"]}, "nullable": []}, "hash": "db850ba27363078399686515615d44c94324abb4c1855ab9a55a68c4bc7183cb"}