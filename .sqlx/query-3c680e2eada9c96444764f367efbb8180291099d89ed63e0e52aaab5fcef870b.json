{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT account_id, created_at, id FROM bria_profiles WHERE ((account_id = $1) AND (COALESCE((created_at, id) < ($4, $3), $3 IS NULL))) ORDER BY created_at DESC, id DESC LIMIT $2) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.created_at desc, i.id desc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "<PERSON><PERSON>", "Timestamptz"]}, "nullable": [false, false, false, false]}, "hash": "3c680e2eada9c96444764f367efbb8180291099d89ed63e0e52aaab5fcef870b"}