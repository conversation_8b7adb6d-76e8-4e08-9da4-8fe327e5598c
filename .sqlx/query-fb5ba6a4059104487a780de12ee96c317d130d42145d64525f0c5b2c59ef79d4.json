{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT id FROM bria_profiles WHERE (COALESCE(id > $2, true)) ORDER BY id ASC LIMIT $1) SELECT i.id AS \"entity_id: ProfileId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_profile_events e ON i.id = e.id ORDER BY i.id asc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: ProfileId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["Int8", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "fb5ba6a4059104487a780de12ee96c317d130d42145d64525f0c5b2c59ef79d4"}