# [bria release v0.1.112](https://github.com/GaloyMoney/bria/releases/tag/0.1.112)


### Bug Fixes

- More warnings
- Warnings
- Udpate bdk_transactions sent type (#604)

### Miscellaneous Tasks

- Bump flake
- Bump ring from 0.17.8 to 0.17.14 (#599)

# [bria release v0.1.111](https://github.com/GaloyMoney/bria/releases/tag/0.1.111)


### Bug Fixes

- Handle deposits to internal addresses from external source (#597)

# [bria release v0.1.110](https://github.com/GaloyMoney/bria/releases/tag/0.1.110)


### Bug Fixes

- Check code (#596)
- Handle negative balances and trigger an alert (#594)

# [bria release v0.1.109](https://github.com/GaloyMoney/bria/releases/tag/0.1.109)


### Features

- Explicitly handle 'UtxoDoesNotExistError' from sync (#587)

### Miscellaneous Tasks

- Update rust and fix lints (#588)

# [bria release v0.1.108](https://github.com/GaloyMoney/bria/releases/tag/0.1.108)


### Bug Fixes

- Audit issues (#583)

### Features

- Add txid and vout to payout (#582)

# [bria release v0.1.107](https://github.com/GaloyMoney/bria/releases/tag/0.1.107)


### Bug Fixes

- Correct serde_with position

### Miscellaneous Tasks

- Bump otel deps (#561)
- Suppress clippy warnings after rustc update (#559)
- Bump url from 2.5.0 to 2.5.2 (#558)
- Bump reqwest from 0.12.3 to 0.12.5 (#556)
- Bump thiserror from 1.0.60 to 1.0.61 (#554)
- Bump prost from 0.12.4 to 0.12.6 (#553)
- Bump serde_json from 1.0.116 to 1.0.117 (#549)
- Bump serde from 1.0.200 to 1.0.203 (#552)
- Bump reqwest-middleware from 0.3.0 to 0.3.1 (#547)
- Bump thiserror from 1.0.59 to 1.0.60 (#548)

# [bria release v0.1.106](https://github.com/GaloyMoney/bria/releases/tag/0.1.106)


### Features

- Use nix for gh actions test (#541)

### Miscellaneous Tasks

- Bump flake
- Bump base64 from 0.22.0 to 0.22.1 (#543)
- Bump serde from 1.0.199 to 1.0.200 (#544)
- Bump serde from 1.0.197 to 1.0.199 (#542)
- Bump serde_with from 3.7.0 to 3.8.1 (#540)
- Bump thiserror from 1.0.58 to 1.0.59 (#539)
- Bump serde_json from 1.0.115 to 1.0.116 (#534)
- Bump serial_test from 3.0.0 to 3.1.1 (#536)

### Refactor

- Paginate list-payouts cmd (#546)

# [bria release v0.1.105](https://github.com/GaloyMoney/bria/releases/tag/0.1.105)


### Miscellaneous Tasks

- Use fedimint-tonic-lnd (#532)
- Bump prost-wkt-types from 0.5.0 to 0.5.1 (#526)
- Bump anyhow from 1.0.81 to 1.0.82 (#527)
- Bump chrono from 0.4.37 to 0.4.38 (#525)
- Bump async-trait from 0.1.79 to 0.1.80 (#524)

# [bria release v0.1.104](https://github.com/GaloyMoney/bria/releases/tag/0.1.104)


### Miscellaneous Tasks

- Bump prost from 0.12.3 to 0.12.4 (#522)
- Bump rust via nix flake
- Bumps reqwest related deps and h2 (#523)
- Bump rust_decimal from 1.34.3 to 1.35.0 (#519)
- Bump tokio from 1.36.0 to 1.37.0 (#518)

# [bria release v0.1.103](https://github.com/GaloyMoney/bria/releases/tag/0.1.103)


### Miscellaneous Tasks

- Bump serde_json from 1.0.114 to 1.0.115 (#514)
- Bump clap from 4.5.1 to 4.5.4 (#515)
- Bump chrono from 0.4.35 to 0.4.37 (#512)
- Bump tokio-stream from 0.1.14 to 0.1.15 (#511)

# [bria release v0.1.102](https://github.com/GaloyMoney/bria/releases/tag/0.1.102)



# [bria release v0.1.101](https://github.com/GaloyMoney/bria/releases/tag/0.1.101)


### Features

- Add update profile cli command (#510)

# [bria release v0.1.100](https://github.com/GaloyMoney/bria/releases/tag/0.1.100)


### Bug Fixes

- Update max payout field name (#508)
- Max_payout in test

### Features

- Add max_payout_sats to profile (#507)

### Miscellaneous Tasks

- Bump async-trait + regex

### Refactor

- Rename maximum -> max

# [bria release v0.1.99](https://github.com/GaloyMoney/bria/releases/tag/0.1.99)


### Miscellaneous Tasks

- Bump deps

# [bria release v0.1.98](https://github.com/GaloyMoney/bria/releases/tag/0.1.98)


### Bug Fixes

- Payout external (#500)

### Miscellaneous Tasks

- Bump chronos / derive_builder (#501)
- Bump flake (#499)

# [bria release v0.1.97](https://github.com/GaloyMoney/bria/releases/tag/0.1.97)


### Bug Fixes

- Lock rows and skip them during concurrent poll (#498)

### Documentation

- Remove Quickstart section (#497)
- Unify capitalization (#496)
- Update install instructions and demo walkthrough (#495)
- Extract out bria demo (#494)
- Using nix for development (#493)

### Miscellaneous Tasks

- Remove accidental file

# [bria release v0.1.96](https://github.com/GaloyMoney/bria/releases/tag/0.1.96)


### Miscellaneous Tasks

- Move meta spawn jobs to trace output

# [bria release v0.1.95](https://github.com/GaloyMoney/bria/releases/tag/0.1.95)



# [bria release v0.1.94](https://github.com/GaloyMoney/bria/releases/tag/0.1.94)


### Miscellaneous Tasks

- Bump sqlx-ledger, opentelemetry and tracing related deps (#488)
- Bump tokio from 1.35.1 to 1.36.0 (#489)

# [bria release v0.1.93](https://github.com/GaloyMoney/bria/releases/tag/0.1.93)


### Miscellaneous Tasks

- Bump base64 from 0.21.7 to 0.22.0 (#487)
- Bump anyhow from 1.0.79 to 1.0.80 (#485)
- Bump tempfile from 3.10.0 to 3.10.1 (#486)
- Bump tonic and tonic-health to 0.11.0 (#483)
- Bump serde from 1.0.196 to 1.0.197 (#480)
- Bump rust_decimal from 1.34.2 to 1.34.3 (#478)
- Bump thiserror from 1.0.56 to 1.0.57 (#479)

# [bria release v0.1.92](https://github.com/GaloyMoney/bria/releases/tag/0.1.92)


### Miscellaneous Tasks

- Bump serde_json from 1.0.113 to 1.0.114 (#477)
- Bump tempfile from 3.9.0 to 3.10.0 (#476)
- Bump reqwest from 0.11.23 to 0.11.24 (#475)
- Bump serde_with from 3.5.1 to 3.6.1 (#469)
- Bump rust_decimal_macros from 1.33.1 to 1.34.2 (#473)
- Bump serde_yaml from 0.9.30 to 0.9.32 (#474)
- Bump derive_builder from 0.13.0 to 0.13.1 (#471)

# [bria release v0.1.91](https://github.com/GaloyMoney/bria/releases/tag/0.1.91)


### Performance

- Cache spks + txs to avoid n-plus-one in bdk (#472)

# [bria release v0.1.90](https://github.com/GaloyMoney/bria/releases/tag/0.1.90)


### Miscellaneous Tasks

- More tracing for bdk <-> pg interactions

# [bria release v0.1.89](https://github.com/GaloyMoney/bria/releases/tag/0.1.89)


### Bug Fixes

- Add musl target to rust-toolchain

### Miscellaneous Tasks

- Add targets to rust-toolchain.toml
- Pin rust to 1.75.0
- Bump tonic-build from 0.10.2 to 0.11.0 (#470)
- Bump rust_decimal from 1.33.1 to 1.34.2 (#467)
- Bump chrono from 0.4.31 to 0.4.33 (#462)
- Bump serde_json from 1.0.111 to 1.0.113 (#465)
- Bump serde_with from 3.4.0 to 3.5.1 (#460)
- Bump serde from 1.0.195 to 1.0.196 (#463)

# [bria release v0.1.88](https://github.com/GaloyMoney/bria/releases/tag/0.1.88)


### Miscellaneous Tasks

- Bump osx building
- Bump derive_builder from 0.12.0 to 0.13.0 (#459)

# [bria release v0.1.87](https://github.com/GaloyMoney/bria/releases/tag/0.1.87)


### Miscellaneous Tasks

- Bump regex from 1.10.2 to 1.10.3 (#458)
- Bump uuid from 1.6.1 to 1.7.0 (#456)
- Bump clap from 4.4.17 to 4.4.18 (#455)
- Bump h2 to version 0.3.24 (#457)
- Bump clap from 4.4.16 to 4.4.17 (#454)
- Bump clap from 4.4.14 to 4.4.16 (#453)

# [bria release v0.1.86](https://github.com/GaloyMoney/bria/releases/tag/0.1.86)


### Miscellaneous Tasks

- Bump clap from 4.4.12 to 4.4.14 (#449)
- Bump opentelemetry_sdk from 0.21.1 to 0.21.2 (#450)
- Bump serde_yaml from 0.9.29 to 0.9.30 (#451)
- Bump serial_test from 2.0.0 to 3.0.0 (#447)
- Bump serde from 1.0.194 to 1.0.195 (#446)
- Bump serde_json from 1.0.109 to 1.0.111 (#443)
- Bump thiserror from 1.0.53 to 1.0.56 (#444)
- Bump anyhow from 1.0.78 to 1.0.79 (#441)
- Bump serde from 1.0.193 to 1.0.194 (#438)

### Refactor

- Remove dependency from app -> tonic

# [bria release v0.1.85](https://github.com/GaloyMoney/bria/releases/tag/0.1.85)


### Miscellaneous Tasks

- Fallback to blockstream for fee estimation (#440)
- Bump async-trait from 0.1.76 to 0.1.77 (#436)

# [bria release v0.1.84](https://github.com/GaloyMoney/bria/releases/tag/0.1.84)


### Bug Fixes

- Clippy

### Miscellaneous Tasks

- Bump flake (#435)
- Bump thiserror from 1.0.52 to 1.0.53 (#432)
- Bump anyhow from 1.0.77 to 1.0.78 (#433)
- Bump async-trait from 0.1.75 to 0.1.76 (#434)
- Bump clap from 4.4.11 to 4.4.12 (#431)
- Bump anyhow from 1.0.76 to 1.0.77 (#429)
- Bump tempfile from 3.8.1 to 3.9.0 (#430)
- Bump thiserror from 1.0.51 to 1.0.52 (#428)
- Bump anyhow from 1.0.75 to 1.0.76 (#426)
- Bump serde_yaml from 0.9.27 to 0.9.29 (#427)
- Bump futures from 0.3.29 to 0.3.30 (#425)
- Bump tokio from 1.35.0 to 1.35.1 (#422)
- Bump async-trait from 0.1.74 to 0.1.75 (#423)
- Bump reqwest from 0.11.22 to 0.11.23 (#421)
- Bump thiserror from 1.0.50 to 1.0.51 (#420)

# [bria release v0.1.83](https://github.com/GaloyMoney/bria/releases/tag/0.1.83)


### Refactor

- Create profile using try_from (#419)

# [bria release v0.1.82](https://github.com/GaloyMoney/bria/releases/tag/0.1.82)


### Miscellaneous Tasks

- Sort addreses in address_extractor

### Refactor

- Move cpfp details to tx_summary (#418)

# [bria release v0.1.81](https://github.com/GaloyMoney/bria/releases/tag/0.1.81)


### Miscellaneous Tasks

- Add jitter to minimum change utxo (#417)
- Bump tokio from 1.34.0 to 1.35.0 (#416)

# [bria release v0.1.80](https://github.com/GaloyMoney/bria/releases/tag/0.1.80)


### Refactor

- Configurable number of retries (#415)

# [bria release v0.1.79](https://github.com/GaloyMoney/bria/releases/tag/0.1.79)


### Miscellaneous Tasks

- Add total_change_sats to process payout queue
- Bump clap from 4.4.10 to 4.4.11 (#411)

### Refactor

- Add better error type to the decode error (#412)

# [bria release v0.1.78](https://github.com/GaloyMoney/bria/releases/tag/0.1.78)


### Bug Fixes

- Pass force_min_change_sats in convert

### Features

- Add force_min_change_sats to PayoutQueueConfig (#414)

### Refactor

- Introduce PsbtBuilderConfig (#413)

# [bria release v0.1.77](https://github.com/GaloyMoney/bria/releases/tag/0.1.77)


### Bug Fixes

- Attribution in chain of cpfp txs (#409)

### Miscellaneous Tasks

- Update bdk, bitcoincore-rpc and electrum-client (#407)

### Testing

- Use bitcoind as signing client for build_psbt fn (#408)

# [bria release v0.1.76](https://github.com/GaloyMoney/bria/releases/tag/0.1.76)


### Features

- Add batch_inclusion_estimated_at to proto::Payout (#405)

# [bria release v0.1.75](https://github.com/GaloyMoney/bria/releases/tag/0.1.75)



# [bria release v0.1.74](https://github.com/GaloyMoney/bria/releases/tag/0.1.74)


### Miscellaneous Tasks

- Temp ignore audit warning
- Record more tracing in outbox
- Bump serde_with from 3.3.0 to 3.4.0 (#404)
- Bump rust_decimal_macros from 1.32.0 to 1.33.1 (#403)

# [bria release v0.1.73](https://github.com/GaloyMoney/bria/releases/tag/0.1.73)


### Miscellaneous Tasks

- Connect batch_broadcasting span

# [bria release v0.1.72](https://github.com/GaloyMoney/bria/releases/tag/0.1.72)


### Miscellaneous Tasks

- Add fees to batch tracing

# [bria release v0.1.71](https://github.com/GaloyMoney/bria/releases/tag/0.1.71)


### Features

- Cpfp detected before block height (#402)

### Miscellaneous Tasks

- Bump url from 2.4.1 to 2.5.0 (#400)

# [bria release v0.1.70](https://github.com/GaloyMoney/bria/releases/tag/0.1.70)


### Bug Fixes

- Clippy

# [bria release v0.1.69](https://github.com/GaloyMoney/bria/releases/tag/0.1.69)


### Bug Fixes

- Alse use income_settled_ledger_tx_id in recursion

### Miscellaneous Tasks

- Use flake (#401)

# [bria release v0.1.68](https://github.com/GaloyMoney/bria/releases/tag/0.1.68)


### Bug Fixes

- Add income_settled_ledger_tx_id as cpfp condition

# [bria release v0.1.67](https://github.com/GaloyMoney/bria/releases/tag/0.1.67)


### Bug Fixes

- Clippy
- Remove utxo from cpfp if no fee bump needed
- Clippy

### Refactor

- Let BDK choose unconfirmed UTXOs not needing extra fees

# [bria release v0.1.66](https://github.com/GaloyMoney/bria/releases/tag/0.1.66)


### Features

- Cpfp (#399)

### Miscellaneous Tasks

- Bump rust_decimal from 1.32.0 to 1.33.1 (#398)

# [bria release v0.1.65](https://github.com/GaloyMoney/bria/releases/tag/0.1.65)


### Miscellaneous Tasks

- Add vbyte / fee to utxos (#397)

# [bria release v0.1.64](https://github.com/GaloyMoney/bria/releases/tag/0.1.64)


### Features

- Queue_config.select_unconfirmed_utxos (#396)

### Refactor

- Small cleanups to outbox listener

# [bria release v0.1.63](https://github.com/GaloyMoney/bria/releases/tag/0.1.63)


### Bug Fixes

- No error when change utxo dropped

### Miscellaneous Tasks

- Bump tokio from 1.33.0 to 1.34.0 (#392)
- Bump reqwest from 0.11.20 to 0.11.22 (#391)

# [bria release v0.1.62](https://github.com/GaloyMoney/bria/releases/tag/0.1.62)


### Miscellaneous Tasks

- Use grpc instead of http in otel (#390)

# [bria release v0.1.61](https://github.com/GaloyMoney/bria/releases/tag/0.1.61)


### Miscellaneous Tasks

- Bump serde from 1.0.190 to 1.0.192 (#389)
- Bump opentelemetry-otlp from 0.13.0 to 0.14.0 (#387)
- Bump tempfile from 3.8.0 to 3.8.1 (#388)

# [bria release v0.1.60](https://github.com/GaloyMoney/bria/releases/tag/0.1.60)


### Miscellaneous Tasks

- Add timeout to call to mempool (#386)
- Bump serde_yaml from 0.9.25 to 0.9.27 (#382)
- Bump async-trait from 0.1.73 to 0.1.74 (#383)
- Bump serde_json from 1.0.107 to 1.0.108 (#380)
- Bump clap from 4.4.2 to 4.4.7 (#381)
- Bump chrono from 0.4.28 to 0.4.31 (#376)
- Bump uuid from 1.4.1 to 1.5.0 (#377)
- Remove dead code
- Bump futures from 0.3.28 to 0.3.29 (#371)
- Bump thiserror from 1.0.48 to 1.0.50 (#370)

# [bria release v0.1.59](https://github.com/GaloyMoney/bria/releases/tag/0.1.59)


### Bug Fixes

- Make validation checks on payout entity (#374)
- Make cancel payout atomic (#373)

# [bria release v0.1.58](https://github.com/GaloyMoney/bria/releases/tag/0.1.58)


### Miscellaneous Tasks

- Bump serde from 1.0.188 to 1.0.190 (#369)
- Bump tracing from 0.1.37 to 0.1.40 (#367)
- Bump base64 from 0.21.4 to 0.21.5 (#368)
- Bump sqlx from 0.7.1 to 0.7.2 (#364)
- Bump sqlx-ledger from 0.10.0 to 0.11.1 (#366)
- Bump regex from 1.10.1 to 1.10.2 (#365)
- Bump sqlxmq to 0.5 (#362)
- Bump regex from 1.9.4 to 1.10.1 (#361)
- Bump base64 from 0.21.3 to 0.21.4 (#360)
- Bump serde_json from 1.0.105 to 1.0.107 (#358)
- Bump tokio from 1.32.0 to 1.33.0 (#357)

# [bria release v0.1.57](https://github.com/GaloyMoney/bria/releases/tag/0.1.57)


### Bug Fixes

- Check-code error on concourse (#356)

### Miscellaneous Tasks

- Upgrade tonic and prost (#355)
- Bump tracing-opentelemetry from 0.20.0 to 0.21.0 (#340)

# [bria release v0.1.56](https://github.com/GaloyMoney/bria/releases/tag/0.1.56)


### Miscellaneous Tasks

- Use otel instead of jaeger (#347)
- Bump flake

# [bria release v0.1.55](https://github.com/GaloyMoney/bria/releases/tag/0.1.55)


### Miscellaneous Tasks

- Bump thiserror from 1.0.47 to 1.0.48 (#345)

# [bria release v0.1.54](https://github.com/GaloyMoney/bria/releases/tag/0.1.54)


### Miscellaneous Tasks

- Bump chrono from 0.4.27 to 0.4.28 (#342)
- Bump clap from 4.4.1 to 4.4.2 (#343)
- Specify devEnvVars
- Gitignore .env

### Styling

- Consistent style across api_client (#341)

# [bria release v0.1.53](https://github.com/GaloyMoney/bria/releases/tag/0.1.53)


### Features

- Profile spending policy (#339)

# [bria release v0.1.52](https://github.com/GaloyMoney/bria/releases/tag/0.1.52)


### Bug Fixes

- >> for osx crosscompile
- Add targets to rust-toolchain.toml

### Miscellaneous Tasks

- Bump via cargo update (#338)
- Bump tempfile from 3.7.1 to 3.8.0 (#329)
- Bump clap from 4.3.19 to 4.4.1 (#336)
- Add flake (#337)
- Bump tokio from 1.30.0 to 1.32.0
- Bump serde_with from 3.2.0 to 3.3.0 (#327)
- Bump anyhow from 1.0.72 to 1.0.75 (#326)
- Bump serde from 1.0.183 to 1.0.185 (#324)
- Bump thiserror from 1.0.44 to 1.0.47 (#325)
- Bump serde_json from 1.0.104 to 1.0.105 (#320)
- Bump async-trait from 0.1.72 to 0.1.73 (#322)
- Bump rust_decimal_macros from 1.30.0 to 1.31.0 (#317)
- Bump bdk from 0.28.1 to 0.28.2 (#318)
- Bump tempfile from 3.7.0 to 3.7.1 (#319)

# [bria release v0.1.51](https://github.com/GaloyMoney/bria/releases/tag/0.1.51)


### Miscellaneous Tasks

- Bump serde from 1.0.181 to 1.0.183 (#316)
- Bump tokio from 1.29.1 to 1.30.0 (#315)
- Bump serde_with from 3.1.0 to 3.2.0 (#314)

# [bria release v0.1.50](https://github.com/GaloyMoney/bria/releases/tag/0.1.50)


### Miscellaneous Tasks

- Update opentelemetry, tracing-opentelemetry, sqlx-ledger and opentelemetry-jaeger (#313)

# [bria release v0.1.49](https://github.com/GaloyMoney/bria/releases/tag/0.1.49)


### Miscellaneous Tasks

- Bump regex from 1.9.1 to 1.9.3 (#308)
- Bump serde from 1.0.177 to 1.0.181 (#307)

### Refactor

- Create dev entities inline (#312)

# [bria release v0.1.48](https://github.com/GaloyMoney/bria/releases/tag/0.1.48)


### Bug Fixes

- Validate queue / wallet before submitting payout

### Miscellaneous Tasks

- Bump rust_decimal from 1.30.0 to 1.31.0 (#304)
- Remove whitespace

# [bria release v0.1.47](https://github.com/GaloyMoney/bria/releases/tag/0.1.47)


### Features

- Submit payout to wallet destination (#306)

### Refactor

- Better naming of destination argument

# [bria release v0.1.47](https://github.com/GaloyMoney/bria/releases/tag/0.1.47)


### Features

- Submit payout to wallet destination (#306)

# [bria release v0.1.46](https://github.com/GaloyMoney/bria/releases/tag/0.1.46)


### Features

- Add expected_time for SubmitPayoutResponse (#298)

# [bria release v0.1.45](https://github.com/GaloyMoney/bria/releases/tag/0.1.45)


### Bug Fixes

- Handle detected -> dropped -> detected cycle

### Miscellaneous Tasks

- Add err to handle_journal_event tracing

# [bria release v0.1.44](https://github.com/GaloyMoney/bria/releases/tag/0.1.44)


### Miscellaneous Tasks

- Bump serde_json from 1.0.102 to 1.0.104 (#300)
- Bump serde from 1.0.175 to 1.0.177 (#299)

### Refactor

- Remove deprecated queries (#302)

# [bria release v0.1.43](https://github.com/GaloyMoney/bria/releases/tag/0.1.43)


### Documentation

- Add quickstart and video image to readme (#291)

### Miscellaneous Tasks

- Bump retries for dev deamon (again)
- Bump retries for dev daemon
- Bump clap from 4.3.17 to 4.3.19 (#301)

# [bria release v0.1.42](https://github.com/GaloyMoney/bria/releases/tag/0.1.42)


### Miscellaneous Tasks

- Bump tempfile from 3.6.0 to 3.7.0 (#297)
- Bump thiserror from 1.0.43 to 1.0.44 (#296)
- Bump async-trait from 0.1.71 to 0.1.72 (#295)
- Bump serde from 1.0.171 to 1.0.175 (#293)
- Bump serde_yaml from 0.9.24 to 0.9.25 (#294)
- Update license to MPL
- Bump anyhow from 1.0.71 to 1.0.72 (#288)
- Bump clap from 4.3.16 to 4.3.17 (#289)
- Bump serde_with from 3.0.0 to 3.1.0 (#290)
- Fix typo (#292)

# [bria release v0.1.41](https://github.com/GaloyMoney/bria/releases/tag/0.1.41)


### Bug Fixes

- Demo link in README.md (#284)

### Documentation

- Use correct SIGNER_ENCRYPTION_KEY (#287)
- Fix typos
- Fix watch the video
- Improve top level bullets
- Extend readme
- Attach demo

### Miscellaneous Tasks

- Bump serde_yaml from 0.9.22 to 0.9.24 (#281)
- Bump uuid from 1.4.0 to 1.4.1 (#278)
- Bump clap from 4.3.11 to 4.3.16 (#285)
- Update sqlx to 0.7.1 (#283)
- Add license

### Refactor

- Use expect instead of unwrap (#279)

# [bria release v0.1.40](https://github.com/GaloyMoney/bria/releases/tag/0.1.40)



# [bria release v0.1.39](https://github.com/GaloyMoney/bria/releases/tag/0.1.39)


### Bug Fixes

- Remove unwarp in signer_config decryption

# [bria release v0.1.38](https://github.com/GaloyMoney/bria/releases/tag/0.1.38)


### Bug Fixes

- Return last_err from signing

# [bria release v0.1.37](https://github.com/GaloyMoney/bria/releases/tag/0.1.37)


### Features

- Payout cancelled (#270)

### Miscellaneous Tasks

- Add key rotation (#274)
- Implement Debug for SignerEncryptionConfig (#273)
- Bump serde_json from 1.0.100 to 1.0.102 (#271)

### Testing

- More robust start_daemon delay

# [bria release v0.1.36](https://github.com/GaloyMoney/bria/releases/tag/0.1.36)



# [bria release v0.1.34](https://github.com/GaloyMoney/bria/releases/tag/0.1.34)


### Miscellaneous Tasks

- Bump Cargo.lock
- Bump deps

### Testing

- Fix path to wallet
- Revert to original bitcoind_inner
- Longer wait between bitcoind retries
- Random bitcoind wallet name

# [bria release v0.1.33](https://github.com/GaloyMoney/bria/releases/tag/0.1.33)


### Features

- Add manually triggered payout queues (#263)
- Added sorted_multisig (#258)

### Miscellaneous Tasks

- Bump serde_json from 1.0.99 to 1.0.100 (#262)
- Bump serde from 1.0.164 to 1.0.167 (#264)
- Bump clap from 4.3.9 to 4.3.10 (#256)
- Bump tokio from 1.29.0 to 1.29.1 (#257)
- Bump uuid from 1.3.4 to 1.4.0 (#251)
- Bump clap from 4.3.8 to 4.3.9 (#254)
- Begin retry_backoff at 2s for proecss_payout_queue
- Bump tokio from 1.28.2 to 1.29.0 (#253)

### Testing

- Add bitcoind broadcast test for multisig (#265)

# [bria release v0.1.32](https://github.com/GaloyMoney/bria/releases/tag/0.1.32)


### Documentation

- Update README with dev instructions

### Features

- Add  utxo_dropped (#245)

### Miscellaneous Tasks

- Bump serde_yaml from 0.9.21 to 0.9.22 (#248)
- Bump clap from 4.3.5 to 4.3.8 (#249)
- Bump serde_json from 1.0.97 to 1.0.99 (#250)

# [bria release v0.1.31](https://github.com/GaloyMoney/bria/releases/tag/0.1.31)


### Bug Fixes

- Return AddressNotFound instead of EntityError

# [bria release v0.1.30](https://github.com/GaloyMoney/bria/releases/tag/0.1.30)


### Bug Fixes

- Estimate fee leak (#247)

### Miscellaneous Tasks

- Dummy whitespace
- Bump deps

# [bria release v0.1.29](https://github.com/GaloyMoney/bria/releases/tag/0.1.29)


### Bug Fixes

- Get-address shows wrong changeAddress flag

### Miscellaneous Tasks

- Remove empty file

### Testing

- Reduce pool size in test
- Remove dir on retry
- Retry connecting to bitcoind
- Make wallet + psbt run serially

# [bria release v0.1.28](https://github.com/GaloyMoney/bria/releases/tag/0.1.28)


### Bug Fixes

- Missing std:: prefix

### Miscellaneous Tasks

- Add get-payout command (#243)
- Bump serde_json from 1.0.96 to 1.0.97 (#241)

### Testing

- Attempt to remove wallet dir
- Try load wallet before create
- Report firts error in bitcoind_client
- Better context output

# [bria release v0.1.27](https://github.com/GaloyMoney/bria/releases/tag/0.1.27)


### Bug Fixes

- Correction for create batch template (#242)

### Features

- Get batch (#239)

### Miscellaneous Tasks

- Add get_address command (#240)

### Refactor

- Some restructuring (#238)

# [bria release v0.1.26](https://github.com/GaloyMoney/bria/releases/tag/0.1.26)


### Bug Fixes

- Sqlx-data
- Implement delete for bdk-utxos/transactions

# [bria release v0.1.25](https://github.com/GaloyMoney/bria/releases/tag/0.1.25)


### Bug Fixes

- Do not ignore bdk error on sync
- Clippy

### Miscellaneous Tasks

- Trace current_height

# [bria release v0.1.24](https://github.com/GaloyMoney/bria/releases/tag/0.1.24)


### Miscellaneous Tasks

- Add some more trace data

# [bria release v0.1.23](https://github.com/GaloyMoney/bria/releases/tag/0.1.23)


### Miscellaneous Tasks

- Alert not drained queue (#237)
- Bump rust_decimal_macros from 1.29.1 to 1.30.0 (#236)
- Bump clap from 4.3.3 to 4.3.4 (#235)

# [bria release v0.1.22](https://github.com/GaloyMoney/bria/releases/tag/0.1.22)


### Miscellaneous Tasks

- Wire vout for payout in all layers (#233)

# [bria release v0.1.21](https://github.com/GaloyMoney/bria/releases/tag/0.1.21)


### Features

- Add block_list for addresses (#228)

### Miscellaneous Tasks

- Bump uuid from 1.3.3 to 1.3.4 (#232)
- Bump sqlx-ledger
- Bump clap from 4.3.2 to 4.3.3 (#229)

# [bria release v0.1.20](https://github.com/GaloyMoney/bria/releases/tag/0.1.20)


### Bug Fixes

- Use xpub id in dev bootstrap for signer

# [bria release v0.1.19](https://github.com/GaloyMoney/bria/releases/tag/0.1.19)


### Features

- Add find_payout_by_external_id (#227)

# [bria release v0.1.18](https://github.com/GaloyMoney/bria/releases/tag/0.1.18)


### Refactor

- Delete error.rs (#226)

# [bria release v0.1.17](https://github.com/GaloyMoney/bria/releases/tag/0.1.17)


### Bug Fixes

- Retry insert if ordered key disapears (#211)

### Miscellaneous Tasks

- Another iteration on adding errors (#217)
- Bump tempfile from 3.5.0 to 3.6.0 (#218)
- Add job error (#215)
- Bump clap from 4.3.1 to 4.3.2 (#216)
- Add xpub errors (#212)
- Bump clap from 4.3.0 to 4.3.1 (#214)
- Bump url from 2.3.1 to 2.4.0 (#213)
- Add ledger errors (#210)
- Bump chrono from 0.4.25 to 0.4.26 (#202)
- Add new errors (#206)
- Rename find_external_by_wallet_id -> list_external_by_wallet_id (#209)

### Refactor

- Add outbox errors (#224)
- Add errors for profile (#223)

### Testing

- Increase max_connections in helpers::init_pool
- Rename external_id_does_not_exist test
- Assert application errors

# [bria release v0.1.16](https://github.com/GaloyMoney/bria/releases/tag/0.1.16)


### Bug Fixes

- Checking correct constraint

### Miscellaneous Tasks

- Add errors for profile (#205)

# [bria release v0.1.15](https://github.com/GaloyMoney/bria/releases/tag/0.1.15)


### Bug Fixes

- Clippy

### Miscellaneous Tasks

- Return NOT_FOUND when external-id does not exist

# [bria release v0.1.14](https://github.com/GaloyMoney/bria/releases/tag/0.1.14)


### Features

- Add find-address-by-external-id (#203)

### Refactor

- Rename FindAddress cli cmd

# [bria release v0.1.13](https://github.com/GaloyMoney/bria/releases/tag/0.1.13)


### Bug Fixes

- New-address after sync (#204)

# [bria release v0.1.12](https://github.com/GaloyMoney/bria/releases/tag/0.1.12)


### Miscellaneous Tasks

- Include tonic-health (#201)
- Bump tokio from 1.28.1 to 1.28.2 (#198)
- Bump chrono from 0.4.24 to 0.4.25 (#199)

# [bria release v0.1.11](https://github.com/GaloyMoney/bria/releases/tag/0.1.11)


### Miscellaneous Tasks

- Change rpc defaults (#200)

# [bria release v0.1.10](https://github.com/GaloyMoney/bria/releases/tag/0.1.10)


### Miscellaneous Tasks

- Add BITCOIND_SIGNER_ENDPOINT to dev daemon
- Add name to PayoutQueueNotFound

# [bria release v0.1.9](https://github.com/GaloyMoney/bria/releases/tag/0.1.9)


### Miscellaneous Tasks

- Bump dependencies (#197)

# [bria release v0.1.8](https://github.com/GaloyMoney/bria/releases/tag/0.1.8)


### Miscellaneous Tasks

- Create dev queue in dev-bootstrap

# [bria release v0.1.7](https://github.com/GaloyMoney/bria/releases/tag/0.1.7)


### Miscellaneous Tasks

- Include proportional fees in outbox (#196)
- Replace mempool_space with mempool_space_client (#195)

# [bria release v0.1.6](https://github.com/GaloyMoney/bria/releases/tag/0.1.6)


### Miscellaneous Tasks

- Pass only hostname for mempool_space (#194)
- Update sqlx-data
- Make index incrementation atomic

# [bria release v0.1.5](https://github.com/GaloyMoney/bria/releases/tag/0.1.5)


### Bug Fixes

- Typo proporional -> proportional (#193)
- Clippy

### Features

- Estimate_fee (#192)
- Add mempool-space to docker-compose (#186)

### Miscellaneous Tasks

- Rename mempool in docker compose
- Half hour priority
- Bump prost-wkt-types to released version
- Bump base64 from 0.21.0 to 0.21.1 (#189)
- Bump electrum timeout to 60s
- Remove SyncProgress (not supported for electrum)

### Refactor

- Add url field to MempoolSpaceClient (#191)

### Testing

- Restart mempool in e2e tests
- Increase timeout for bitcoind_sync

# [bria release v0.1.4](https://github.com/GaloyMoney/bria/releases/tag/0.1.4)


### Refactor

- Use better batching for bdk persistance (#188)

# [bria release v0.1.3](https://github.com/GaloyMoney/bria/releases/tag/0.1.3)


### Miscellaneous Tasks

- Pass progress tracker to bdk sync
- Set max_retry_delay to 60s for sync_wallet
- Dedup payout_queue scheduling
- Bump reqwest from 0.11.17 to 0.11.18 (#187)

# [bria release v0.1.2](https://github.com/GaloyMoney/bria/releases/tag/0.1.2)


### Miscellaneous Tasks

- Output has_more in span

# [bria release v0.1.1](https://github.com/GaloyMoney/bria/releases/tag/0.1.1)


### Bug Fixes

- Change bitcoin to mainnet for BlockchainConfig (#185)
- Better encumbered fees estimation

### Miscellaneous Tasks

- Sync 100 txs at a time

# [bria release v0.1.0](https://github.com/GaloyMoney/bria/releases/tag/0.1.0)


### Bug Fixes

- Cleanup to the encrypt signer config pr (#184)
- No default for PG_CON

### Features

- [**breaking**] Use meaningful id's for accounts (#181)
- Encrypt and persist signer config (#177)

### Miscellaneous Tasks

- Bump uuid from 1.3.2 to 1.3.3 (#183)
- Handle max_retry_backof and extend signing job
- Remove comment (#179)

### Refactor

- Pass JobsConfig to jobs runner
- UnbatchedPayout.commit_to_batch
- UnbatchedPayouts container
- Extract construct_psbt to PsbtBuilder
- Group_name -> payout_queue

### Testing

- Remove redundant debug output
- Attempt retries in bria_init
- Fix bria.yml for e2e tests

# [bria release v0.0.24](https://github.com/GaloyMoney/bria/releases/tag/0.0.24)


### Miscellaneous Tasks

- Improve tracing
- Bump serde from 1.0.162 to 1.0.163 (#178)
- Bump tokio from 1.28.0 to 1.28.1 (#176)

# [bria release v0.0.23](https://github.com/GaloyMoney/bria/releases/tag/0.0.23)


### Miscellaneous Tasks

- Add batch_id to payout events
- Remove redundant config options (#175)

# [bria release v0.0.22](https://github.com/GaloyMoney/bria/releases/tag/0.0.22)


### Refactor

- CommittedToBatch

# [bria release v0.0.21](https://github.com/GaloyMoney/bria/releases/tag/0.0.21)


### Features

- Add update-batch-group (#167)
- Payout events (#168)

### Miscellaneous Tasks

- Add optional wallet creation in dev bootstrap (#173)
- Missing payout events (#172)
- Rename batch-group (#170)

### Refactor

- Rename payout-queued -> payout-submitted (#174)
- Fix outstanding naming in job/mod.rs
- Logical -> effective (#169)

# [bria release v0.0.20](https://github.com/GaloyMoney/bria/releases/tag/0.0.20)


### Bug Fixes

- Batch group description is optional

### Miscellaneous Tasks

- Add description in list-batch-groups (#165)
- Add DbConfig (#164)
- Add electrum fee estimator

### Refactor

- Multi change outputs (#166)

# [bria release v0.0.19](https://github.com/GaloyMoney/bria/releases/tag/0.0.19)


### Features

- --dev flag for daemon to auto bootstrap (#157)
- List-xpubs (#162)

### Miscellaneous Tasks

- Add descriptors to ensure no duplicate use (#163)
- Bump sqlx-ledger
- Bump serde from 1.0.160 to 1.0.162 (#160)

### Testing

- Assert_summaries_match helper

# [bria release v0.0.18](https://github.com/GaloyMoney/bria/releases/tag/0.0.18)


### Bug Fixes

- Do not unwrap derivation path after parse

# [bria release v0.0.17](https://github.com/GaloyMoney/bria/releases/tag/0.0.17)


### Miscellaneous Tasks

- Whitespace
- Whitespace
- Refactor keychain (#161)

# [bria release v0.0.16](https://github.com/GaloyMoney/bria/releases/tag/0.0.16)


### Bug Fixes

- Address pr reviews
- Fix formatting errors
- Add suggested changes

### Features

- Add account balance summary (#159)
- Add import-descriptors (#158)
- Add list-wallets
- Add list_batch_groups

### Miscellaneous Tasks

- Bump clap from 4.2.5 to 4.2.7
- Fix txPriority output in list-batch-groups
- Remove (unused) dust config

# [bria release v0.0.16](https://github.com/GaloyMoney/bria/releases/tag/0.0.16)


### Bug Fixes

- Address pr reviews
- Fix formatting errors
- Add suggested changes

### Features

- Add account balance summary (#159)
- Add import-descriptors (#158)
- Add list-wallets
- Add list_batch_groups

### Miscellaneous Tasks

- Bump clap from 4.2.5 to 4.2.7
- Fix txPriority output in list-batch-groups
- Remove (unused) dust config

# [bria release v0.0.15](https://github.com/GaloyMoney/bria/releases/tag/0.0.15)


### Bug Fixes

- Switch to 'STANDARD' base64 engine to keep trailing '='
- Add 'sighash_type' argument for signing psbt with bitcoind
- Add missing Bitcoind connect branch

### Documentation

- Add demo in readme

### Features

- Update address
- Implement bitcoind signer
- Add 'BitcoindSignerConfig' handling to api grpc interface

### Miscellaneous Tasks

- Poll augmenter from OutboxListener
- Use EcdsaSighashType as const
- Bump serde_with from 2.3.3 to 3.0.0
- Wire augment option through
- Swap out deprecated base64 encode/decode
- Reformat long command
- Bump anyhow from 1.0.70 to 1.0.71

### Refactor

- Declare and assign a DEFAULT_SIGHASH_TYPE for re-use
- Pass sighash from unsigned psbt into signer
- Make OutboxEvent generic
- Add 2nd bitcoind container for signer wallet
- Remove intermediate SignerConfig type in cli module
- Rename confirmed -> settledUtxo in balance summary

### Testing

- Back to bc
- Remove dependency on bc
- Connect to bitcoind-signer for payouts test
- Test update-address via event augmentation
- Restart bitcoind-signer as well to clear chain state
- Flaky lnd_sync tests on re-run, or run after bitcoind_sync
- Add a check for deliberate transition before block mine
- Why sweepall is not confirming in bria
- Add 'exit 1' to some checks
- Swap out sendtoaddress for manual tx to spend unconfirmed
- Add bitcoind sync tests
- Update 'signing-complete' check
- Swap in bitcoind as payout signer

# [bria release v0.0.14](https://github.com/GaloyMoney/bria/releases/tag/0.0.14)


### Miscellaneous Tasks

- Switch event proto idx
- Logical balance before utxos
- Consistent dir namings (daemon-pid)
- Bump sqlx-ledger to v0.7.7
- Bump uuid from 1.3.1 to 1.3.2

# [bria release v0.0.13](https://github.com/GaloyMoney/bria/releases/tag/0.0.13)


### Features

- Watch-events cli cmd

### Miscellaneous Tasks

- Remove redundant FOR UPDATE
- Complete include PayoutInfo in batch metadata
- Remove bria_batch_spent_utxos and revamp utxo handling
- Add involved_keychains to WalletSummary
- Add payout to batch metadata WIP
- Forgot CONFIRMED_UTXO -> SETTLED_UTXO renaming
- Return correct type for event stream
- Add OutboxListener
- Outbox listener WIP
- Persist journal events in outbox
- OutboxEvent boilerplate
- Add account_id to all metadata
- More Outbox boilerplate
- Bump tracing from 0.1.37 to 0.1.38
- Journal events boilerplate
- Cargo update
- Bump sqlx-ledger
- Handle_outbox boilerplate
- Add keep_alive thread to job executor
- Bump serde_json from 1.0.95 to 1.0.96

### Refactor

- Rename entry-types
- BatchInfo -> BatchWalletInfo
- Remove additional_metadata from PayoutQueuedMeta
- Wallet_summary.signing_keychains
- Remove Uuid from batch/repo.rs
- WalletTransactionSummary naming
- Move PayoutDestination to primitives
- Consistent column naming
- Better naming for templates
- Small cleanups

# [bria release v0.0.12](https://github.com/GaloyMoney/bria/releases/tag/0.0.12)


### Bug Fixes

- Set address events

### Refactor

- Proper signing session initialization event
- Proper address initialization event
- Proper xpub initialization event
- Move original out of XPubValue
- Payouts as events
- Events in batch_group
- Better wallet structure
- Persist wallet with events
- Use EntityEvents::persist in signing session repo
- Use EntityEvents::persist
- Use events in xpub entity

# [bria release v0.0.11](https://github.com/GaloyMoney/bria/releases/tag/0.0.11)


### Bug Fixes

- Clippy
- NewUtxo field visibility

### Features

- List-addresses cli cmd
- Add addresses repository
- Pass metadata json arg in to grpc service
- Add 'metadata' arg to queue-payout cmd

### Miscellaneous Tasks

- Sync addresses in sync_wallet job
- Bump tracing-subscriber from 0.3.16 to 0.3.17
- Improve address entity
- Submit batch execution
- Update 'h2' for RUSTSEC-2023-0034 vulnerability
- Implement Display trait on AddressCreationInfo
- Submit_batch template
- Use tx_summary in create_batch template
- Bump clap from 4.2.2 to 4.2.4
- Signing finalized / broadcasting broadcasts
- Bump tonic-build from 0.9.1 to 0.9.2
- Some pre-accounting cleanup
- Batch_finalizing
- Set-signer-config triggers batch-signing
- Batch_signing
- Bump tonic-build from 0.9.1 to 0.9.2
- List-signing-sessions cli cmd
- List-signing-sessions
- Persist updated sessions
- Complete persistance of new signing sessions
- Some signing boilerplate
- Move jobs to singular
- Add signing_session module
- Pass XPubs to jobs
- Introduce entity module
- Access xpubs via wallet
- Add bitcoind/signet.conf
- Bump prost from 0.11.8 to 0.11.9
- Use forked prost-wkt-types
- Improve rust idioms
- Handle json conversion error in ApiClient::queue_payout
- Handle struct parsing error in Bria::queue_payout
- Add prost-types

### Refactor

- Make external_id is address by default
- Persist address via events
- Persist_new_session -> persist_sessions
- Assign address_id to external_id if none is passed in
- Make (address_string, keychain_id) combination unique
- Add 'profile_id' to Address entity
- Change 'new_external_address' return to domain AddressCreationInfo type
- Add new props to NewAddress grpc request
- Add new props to new-address cli command
- Pass in pg tx to utxo use cases
- Restructure foreign references
- Make queue_payout metadata prop optional

### Testing

- Add list-addresses to e2e tests
- Add new args to new-address test
- Add metadata arg to queue-payout test

# [bria release v0.0.10](https://github.com/GaloyMoney/bria/releases/tag/0.0.10)


### Bug Fixes

- Check-code
- Handle spent change utxo
- Correct deferred logical out

### Miscellaneous Tasks

- Sync tx confirmation in line
- Bump tonic from 0.9.1 to 0.9.2
- Bump clap from 4.2.1 to 4.2.2

# [bria release v0.0.9](https://github.com/GaloyMoney/bria/releases/tag/0.0.9)


### Miscellaneous Tasks

- Return error on ElectrumBlockchain config

# [bria release v0.0.8](https://github.com/GaloyMoney/bria/releases/tag/0.0.8)


### Bug Fixes

- Support for vpub import

# [bria release v0.0.7](https://github.com/GaloyMoney/bria/releases/tag/0.0.7)


### Bug Fixes

- Missing commit call
- Only auth with active keys

### Features

- Introduce profile

### Miscellaneous Tasks

- Expose create profile api key

### Refactor

- Rename account -> profile in token_store

# [bria release v0.0.6](https://github.com/GaloyMoney/bria/releases/tag/0.0.6)


### Features

- List accounts

### Miscellaneous Tasks

- Rename AccountCreate -> CreateAccount

# [bria release v0.0.5](https://github.com/GaloyMoney/bria/releases/tag/0.0.5)


### Bug Fixes

- Bria home in release images

### Miscellaneous Tasks

- Bump sqlx-ledger from 0.5.11 to 0.5.12

# [bria release v0.0.4](https://github.com/GaloyMoney/bria/releases/tag/0.0.4)


### Bug Fixes

- Release images

# [bria release v0.0.3](https://github.com/GaloyMoney/bria/releases/tag/0.0.3)


### Bug Fixes

- Dev version

# [bria release v0.0.2](https://github.com/GaloyMoney/bria/releases/tag/0.0.2)
