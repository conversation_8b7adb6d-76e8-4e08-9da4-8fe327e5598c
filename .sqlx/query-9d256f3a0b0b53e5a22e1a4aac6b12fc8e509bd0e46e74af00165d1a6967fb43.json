{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT id FROM bria_payout_queues WHERE account_id = $1) SELECT i.id AS \"entity_id: PayoutQueueId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_payout_queue_events e ON i.id = e.id ORDER BY i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: PayoutQueueId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "9d256f3a0b0b53e5a22e1a4aac6b12fc8e509bd0e46e74af00165d1a6967fb43"}