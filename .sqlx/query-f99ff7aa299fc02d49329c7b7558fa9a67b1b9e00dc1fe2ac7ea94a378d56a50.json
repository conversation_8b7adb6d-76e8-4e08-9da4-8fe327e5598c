{"db_name": "PostgreSQL", "query": "WITH entities AS (SELECT account_id, id FROM bria_payout_queues WHERE ((account_id = $1) AND (COALESCE(id > $3, true))) ORDER BY id ASC LIMIT $2) SELECT i.id AS \"entity_id: PayoutQueueId\", e.sequence, e.event, e.recorded_at FROM entities i JOIN bria_payout_queue_events e ON i.id = e.id ORDER BY i.id asc, i.id, e.sequence", "describe": {"columns": [{"ordinal": 0, "name": "entity_id: PayoutQueueId", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "sequence", "type_info": "Int4"}, {"ordinal": 2, "name": "event", "type_info": "Jsonb"}, {"ordinal": 3, "name": "recorded_at", "type_info": "Timestamptz"}], "parameters": {"Left": ["<PERSON><PERSON>", "Int8", "<PERSON><PERSON>"]}, "nullable": [false, false, false, false]}, "hash": "f99ff7aa299fc02d49329c7b7558fa9a67b1b9e00dc1fe2ac7ea94a378d56a50"}