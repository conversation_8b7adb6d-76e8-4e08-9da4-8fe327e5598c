module "shared" {
  source = "../shared"
}

variable "host_index" {
  default = 0
}

locals {
  project              = module.shared.gcp_project
  zone                 = "${module.shared.gcp_region}-b"
  region               = module.shared.gcp_region
  name_prefix          = module.shared.name_prefix
  name_postfix         = var.host_index
  default_machine_type = "n2-standard-8"
  image                = "ubuntu-os-cloud/ubuntu-2204-lts"
  docker_host_tag      = "docker-host-${local.name_postfix}"
  deployer_sa          = "<EMAIL>"
  users = [
    "user:<EMAIL>",
    "user:<EMAIL>",
    "user:<EMAIL>",
    "serviceAccount:${local.deployer_sa}",
  ]
  docker_host_name = "docker-host-${local.name_postfix}"
}

provider "google" {
  project = local.project
  region  = local.region
  zone    = local.zone
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/docker-host"
  }
}
