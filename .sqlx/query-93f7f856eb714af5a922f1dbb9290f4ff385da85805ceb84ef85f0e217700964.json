{"db_name": "PostgreSQL", "query": "INSERT INTO bria_profile_events (id, recorded_at, sequence, event_type, event) SELECT $1, $2, ROW_NUMBER() OVER () + $3, unnested.event_type, unnested.event FROM UNNEST($4::text[], $5::jsonb[]) AS unnested(event_type, event)", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON>", "Timestamptz", "Int8", "TextArray", "JsonbArray"]}, "nullable": []}, "hash": "93f7f856eb714af5a922f1dbb9290f4ff385da85805ceb84ef85f0e217700964"}