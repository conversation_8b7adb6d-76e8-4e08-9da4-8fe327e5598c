#@ load("@ytt:data", "data")

#@ render_secrets = "false"
#@ if data.values.github_ssh_key_base64 != "":
#@    render_secrets = "true"
#@ end

#@ if render_secrets == "true":
apiVersion: v1
kind: Secret
metadata:
  name: github-key
data:
  ssh-privatekey: #@ data.values.github_ssh_key_base64
#@ end
---
#@ def git_ref(ref):
path: git-ref
inline:
  paths:
    #@yaml/text-templated-strings
    ref: |
      (@= ref @)
#@ end

apiVersion: vendir.k14s.io/v1alpha1
kind: Config
directories:

#! galoy-infra vendoring
#!   bootstrap
#!   inception
#!   platform
#!   postgresql
#!   smoketest

- path: modules/infra/vendor
  contents:
  - #@ git_ref(data.values.infra_git_ref)
  - path: tf
    git:
      url: https://github.com/GaloyMoney/galoy-infra.git
      ref: #@ data.values.infra_git_ref
    includePaths:
    - modules/bootstrap/gcp/**
    - modules/inception/gcp/**
    - modules/platform/gcp/**
    - modules/postgresql/gcp/**
    - modules/smoketest/gcp/**
    newRootPath: modules

#!  Bitcoin deployment vendoring
#!    bitcoind-chart
#!    lnd-chart
#!    bitcoind-chart
#!    specter-chart
#!    bria-chart
#!    fulcrum-chart

- path: modules/services/bitcoin/vendor/bitcoind
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.bitcoind_git_ref
    includePaths:
    - charts/bitcoind/**/*
    newRootPath: charts/bitcoind
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.bitcoind_git_ref
    includePaths:
    - ci/tasks/bitcoind-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.bitcoind_git_ref)

- path: modules/services/bitcoin/vendor/lnd1
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.lnd_git_ref
    includePaths:
    - charts/lnd/**/*
    newRootPath: charts/lnd
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.lnd_git_ref
    includePaths:
    - ci/tasks/lnd-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.lnd_git_ref)

- path: modules/services/bitcoin/vendor/lnd2
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.lnd_git_ref
    includePaths:
    - charts/lnd/**/*
    newRootPath: charts/lnd
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.lnd_git_ref
    includePaths:
    - ci/tasks/lnd-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.lnd_git_ref)

- path: modules/services/bitcoin/vendor/specter
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.specter_git_ref
    includePaths:
    - charts/specter/**/*
    newRootPath: charts/specter
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.specter_git_ref
    includePaths:
    - ci/tasks/specter-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.specter_git_ref)

- path: modules/services/bitcoin/vendor/bria
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.bria_git_ref
    includePaths:
    - charts/bria/**/*
    newRootPath: charts/bria
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.bria_git_ref
    includePaths:
    - ci/tasks/bria-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.bria_git_ref)

- path: modules/services/bitcoin/vendor/fulcrum
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.fulcrum_git_ref
    includePaths:
    - charts/fulcrum/**/*
    newRootPath: charts/fulcrum
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.fulcrum_git_ref
    includePaths:
    - ci/tasks/fulcrum-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.fulcrum_git_ref)

#! Blink deployment vendoring
#!    galoy-chart

- path: modules/galoy/vendor/galoy
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_git_ref
    includePaths:
    - charts/galoy/**/*
    newRootPath: charts/galoy
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_git_ref
    includePaths:
    - ci/tasks/galoy-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.galoy_git_ref)

#! Blink deployment vendoring
#!    monitoring-chart

- path: modules/services/monitoring/vendor/monitoring
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.monitoring_git_ref
    includePaths:
    - charts/monitoring/**/*
    newRootPath: charts/monitoring
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.monitoring_git_ref
    includePaths:
    - ci/tasks/monitoring-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.monitoring_git_ref)

#!  Addons deployment vendoring
#!    admin-panel-chart

- path: modules/services/addons/vendor/admin-panel
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.admin_panel_git_ref
    includePaths:
    - charts/admin-panel/**/*
    newRootPath: charts/admin-panel
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.admin_panel_git_ref
    includePaths:
    - ci/tasks/admin-panel-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.admin_panel_git_ref)

#!  Addons deployment vendoring
#!    galoy-pay-chart

- path: modules/services/addons/vendor/galoy-pay
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_pay_git_ref
    includePaths:
    - charts/galoy-pay/**/*
    newRootPath: charts/galoy-pay
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_pay_git_ref
    includePaths:
    - ci/tasks/galoy-pay-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.galoy_pay_git_ref)

#!  Stablesats deployment vendoring
#!    stablesats

- path: modules/services/stablesats/vendor/stablesats
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.stablesats_git_ref
    includePaths:
    - charts/stablesats/**/*
    newRootPath: charts/stablesats
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.stablesats_git_ref
    includePaths:
    - ci/tasks/stablesats-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.stablesats_git_ref)

#!    api-dashboard

- path: modules/services/addons/vendor/api-dashboard
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.api_dashboard_git_ref
    includePaths:
    - charts/api-dashboard/**/*
    newRootPath: charts/api-dashboard
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.api_dashboard_git_ref
    includePaths:
    - ci/tasks/api-dashboard-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.api_dashboard_git_ref)

#!  Galoy deps deployment vendoring
#!    galoy-deps

- path: modules/services/galoy-deps/vendor/galoy-deps
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_deps_git_ref
    includePaths:
    - charts/galoy-deps/**/*
    newRootPath: charts/galoy-deps
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.galoy_deps_git_ref
    includePaths:
    - ci/tasks/galoy-deps-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.galoy_deps_git_ref)
- path: modules/services/galoy-deps/vendor/strimzi-kafka-operator
  contents:
  - path: crds
    git:
      url: https://github.com/strimzi/strimzi-kafka-operator.git
      ref: #@ data.values.strimzi_kafka_operator_version
    includePaths:
    - helm-charts/helm3/strimzi-kafka-operator/crds/*.yaml
    newRootPath: helm-charts/helm3/strimzi-kafka-operator/crds

#! Kafka connect deployment vendoring
#!    kafka-connect-chart

- path: modules/services/monitoring/vendor/kafka-connect
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.kafka_connect_git_ref
    includePaths:
    - charts/kafka-connect/**/*
    newRootPath: charts/kafka-connect
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.kafka_connect_git_ref
    includePaths:
    - ci/tasks/kafka-connect-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.kafka_connect_git_ref)


#!  Blink addons deployment vendoring
#!    circles

- path: modules/services/blink-addons/vendor/circles
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.circles_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - charts/circles/**/*
    newRootPath: charts/circles
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.circles_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - ci/tasks/circles-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.circles_git_ref)

#! blink-fiat

- path: modules/services/blink-addons/vendor/blink-fiat
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.blink_fiat_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - charts/blink-fiat/**/*
    newRootPath: charts/blink-fiat
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.blink_fiat_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - ci/tasks/blink-fiat-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.blink_fiat_git_ref)

#! blink-kyc

- path: modules/services/blink-addons/vendor/blink-kyc
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.blink_kyc_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - charts/blink-kyc/**/*
    newRootPath: charts/blink-kyc
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.blink_kyc_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - ci/tasks/blink-kyc-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.blink_kyc_git_ref)


#! map chart

- path: modules/services/addons/vendor/map
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.map_git_ref
    includePaths:
    - charts/map/**/*
    newRootPath: charts/map
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.map_git_ref
    includePaths:
    - ci/tasks/map-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.map_git_ref)

#!    voucher

- path: modules/services/addons/vendor/voucher
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.voucher_git_ref
    includePaths:
    - charts/voucher/**/*
    newRootPath: charts/voucher
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.voucher_git_ref
    includePaths:
    - ci/tasks/voucher-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.voucher_git_ref)

#! Cala
- path: modules/services/cala/vendor/cala
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.cala_git_ref
    includePaths:
    - charts/cala/**/*
    newRootPath: charts/cala
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: #@ data.values.cala_git_ref
    includePaths:
    - ci/tasks/cala-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.cala_git_ref)

- path: modules/services/cala/vendor/cala-enterprise
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.cala_enterprise_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - charts/cala-enterprise/**/*
    newRootPath: charts/cala-enterprise
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: #@ data.values.cala_enterprise_git_ref
      #@ if render_secrets == "true":
      secretRef:
        name: github-key
      #@ end
    includePaths:
    - ci/tasks/cala-enterprise-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - #@ git_ref(data.values.cala_enterprise_git_ref)
