{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1747179050, "narHash": "sha256-qhFMmDkeJX9KJwr5H32f1r7Prs7XbQWtO0h3V0a0rFY=", "owner": "NixOS", "repo": "nixpkgs", "rev": "adaa24fbf46737f3f1b5497bf64bae750f82942e", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs", "rust-overlay": "rust-overlay"}}, "rust-overlay": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1747332411, "narHash": "sha256-2LOxMLddhMoJphMU/72Ls6Rvp38aJUrp7OhWwyvslek=", "owner": "oxalica", "repo": "rust-overlay", "rev": "10d4529b7ead35863caa77993915104345524bed", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}